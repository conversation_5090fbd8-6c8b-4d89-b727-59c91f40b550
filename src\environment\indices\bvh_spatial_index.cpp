// src/environment/indices/bvh_spatial_index.cpp

#include "environment/indices/bvh_spatial_index.h"
#include "environment/coordinate/coordinate_converter.h"
#include "utils/logging.h"
#include <fcl/broadphase/broadphase_dynamic_AABB_tree.h>

namespace NSDrones {
namespace NSEnvironment {

    // === BvhObjectData实现 ===

    BvhObjectData::BvhObjectData(const ObjectID& obj_id,
                                const EcefPoint& ecef_pos,
                                const BoundingBox& local_box)
        : id(obj_id), ecefPosition(ecef_pos), localBounds(local_box) {

        // 计算ECEF包围盒
        fcl::Vector3d min_local(local_box.min_.x(), local_box.min_.y(), local_box.min_.z());
        fcl::Vector3d max_local(local_box.max_.x(), local_box.max_.y(), local_box.max_.z());

        fcl::Vector3d min_ecef(ecef_pos.x() + min_local.x(), ecef_pos.y() + min_local.y(), ecef_pos.z() + min_local.z());
        fcl::Vector3d max_ecef(ecef_pos.x() + max_local.x(), ecef_pos.y() + max_local.y(), ecef_pos.z() + max_local.z());

        ecefBounds = BoundingBox(min_ecef, max_ecef);
    }

    // === BvhSpatialIndex实现 ===

    BvhSpatialIndex::BvhSpatialIndex() {
        LOG_INFO("初始化BvhSpatialIndex，使用ECEF坐标系统和FCL BVH");

        // 创建FCL BVH管理器
        bvh_manager_ = std::make_unique<fcl::DynamicAABBTreeCollisionManagerd>();
    }

    BvhSpatialIndex::~BvhSpatialIndex() {
        LOG_INFO("销毁BvhSpatialIndex，对象数量: {}", objects_.size());
    }

    bool BvhSpatialIndex::initialize(const nlohmann::json& config_json,
                                   const NSParams::ParamValues& global_params,
                                   const std::optional<BoundingBox>& map_bounds) {
        LOG_INFO("初始化BvhSpatialIndex配置");
        
        try {
            // 从配置中读取内存限制
            if (config_json.contains("memory_limit_mb")) {
                memory_limit_ = config_json["memory_limit_mb"].get<size_t>() * 1024 * 1024;
            }

            LOG_INFO("BvhSpatialIndex初始化完成 - 内存限制: {} MB",
                    memory_limit_ / (1024 * 1024));

            return true;
        } catch (const std::exception& e) {
            LOG_ERROR("BvhSpatialIndex初始化失败: {}", e.what());
            return false;
        }
    }

    std::shared_ptr<fcl::CollisionObjectd> BvhSpatialIndex::createFCLObject(
        const EcefPoint& ecef_pos,
        const BoundingBox& local_bounds) const {
        
        // 创建一个简单的盒子几何体来表示对象
        auto box_geometry = std::make_shared<fcl::Boxd>(
            local_bounds.max_.x() - local_bounds.min_.x(),
            local_bounds.max_.y() - local_bounds.min_.y(), 
            local_bounds.max_.z() - local_bounds.min_.z()
        );
        
        // 创建变换矩阵
        fcl::Transform3d transform = fcl::Transform3d::Identity();
        transform.translation() = fcl::Vector3d(ecef_pos.x(), ecef_pos.y(), ecef_pos.z());
        
        return std::make_shared<fcl::CollisionObjectd>(box_geometry, transform);
    }

    void BvhSpatialIndex::updateBVHManager() {
        // 清空当前BVH管理器
        bvh_manager_->clear();
        
        // 添加所有对象到BVH管理器
        for (const auto& [id, obj_data] : objects_) {
            if (obj_data.fclObject) {
                bvh_manager_->registerObject(obj_data.fclObject.get());
            }
        }
        
        // 更新BVH结构
        bvh_manager_->update();
        
        LOG_TRACE("BVH管理器已更新，包含 {} 个对象", objects_.size());
    }

    void BvhSpatialIndex::addObject(const SpatialObject& object) {
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);
        
        LOG_TRACE("添加对象到BVH索引: {}", object.id);
        
        // 转换WGS84坐标到ECEF
        EcefPoint ecef_pos = CoordinateConverter::wgs84ToECEF(object.position);
        
        // 创建BVH对象数据
        BvhObjectData obj_data(object.id, ecef_pos, object.localBounds);
        obj_data.fclObject = createFCLObject(ecef_pos, object.localBounds);
        
        // 存储对象
        objects_[object.id] = std::move(obj_data);
        
        // 更新BVH管理器
        updateBVHManager();

        LOG_TRACE("对象 {} 已添加到BVH索引", object.id);
    }

    void BvhSpatialIndex::addObjects(const std::vector<SpatialObject>& objects) {
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);
        
        LOG_INFO("批量添加 {} 个对象到BVH索引", objects.size());
        
        for (const auto& object : objects) {
            EcefPoint ecef_pos = CoordinateConverter::wgs84ToECEF(object.position);
            BvhObjectData obj_data(object.id, ecef_pos, object.localBounds);
            obj_data.fclObject = createFCLObject(ecef_pos, object.localBounds);
            objects_[object.id] = std::move(obj_data);
        }
        
        // 一次性更新BVH管理器
        updateBVHManager();
        
        LOG_INFO("批量添加完成，BVH索引现包含 {} 个对象", objects_.size());
    }

    void BvhSpatialIndex::updateObject(const SpatialObjectUpdate& update) {
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);
        
        auto it = objects_.find(update.id);
        if (it == objects_.end()) {
            LOG_WARN("尝试更新不存在的对象: {}", update.id);
            return;
        }
        
        LOG_TRACE("更新BVH索引中的对象: {}", update.id);
        
        bool needs_bvh_update = false;
        
        // 更新位置
        if (update.newPosition.has_value()) {
            GeographicLib::Geocentric earth(GeographicLib::Constants::WGS84_a(), GeographicLib::Constants::WGS84_f());
            double x, y, z;
            earth.Forward(update.newPosition.value().latitude, update.newPosition.value().longitude, update.newPosition.value().altitude, x, y, z);
            EcefPoint new_ecef_pos(x, y, z);
            it->second.ecefPosition = new_ecef_pos;
            needs_bvh_update = true;
        }
        
        // 更新形状
        if (update.newLocalBounds.has_value()) {
            it->second.localBounds = update.newLocalBounds.value();
            it->second.fclObject = createFCLObject(it->second.ecefPosition, it->second.localBounds);
            needs_bvh_update = true;
        }
        
        if (needs_bvh_update) {
            updateBVHManager();
        }
        
        LOG_TRACE("对象 {} 更新完成", update.id);
    }

    void BvhSpatialIndex::updateObjects(const std::vector<SpatialObjectUpdate>& updates) {
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);
        
        LOG_INFO("批量更新 {} 个对象", updates.size());
        
        bool needs_bvh_update = false;
        
        for (const auto& update : updates) {
            auto it = objects_.find(update.id);
            if (it == objects_.end()) {
                LOG_WARN("尝试更新不存在的对象: {}", update.id);
                continue;
            }
            
            if (update.newPosition.has_value()) {
                EcefPoint new_ecef_pos = CoordinateConverter::wgs84ToECEF(update.newPosition.value());
                it->second.ecefPosition = new_ecef_pos;
                needs_bvh_update = true;
            }
            
            if (update.newLocalBounds.has_value()) {
                it->second.localBounds = update.newLocalBounds.value();
                it->second.fclObject = createFCLObject(it->second.ecefPosition, it->second.localBounds);
                needs_bvh_update = true;
            }
        }
        
        if (needs_bvh_update) {
            updateBVHManager();
        }
        
        LOG_INFO("批量更新完成");
    }

    void BvhSpatialIndex::updateObjectPosition(const ObjectID& objectId,
                                             const WGS84Point& newPosition,
                                             const std::optional<WGS84Point>& oldPosition) {
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);
        
        auto it = objects_.find(objectId);
        if (it == objects_.end()) {
            LOG_WARN("尝试更新不存在对象的位置: {}", objectId);
            return;
        }
        
        LOG_TRACE("更新对象位置: {} -> {}", objectId, newPosition.toString());
        
        // 转换新位置到ECEF
        EcefPoint new_ecef_pos = CoordinateConverter::wgs84ToECEF(newPosition);
        it->second.ecefPosition = new_ecef_pos;
        
        // 重新创建FCL对象
        it->second.fclObject = createFCLObject(new_ecef_pos, it->second.localBounds);
        
        updateBVHManager();

        LOG_TRACE("对象 {} 位置更新完成", objectId);
    }

    void BvhSpatialIndex::updateObjectPositions(
        const std::vector<std::pair<ObjectID,
        std::pair<std::optional<WGS84Point>, WGS84Point>>>& positionUpdates) {
        
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);
        
        LOG_INFO("批量更新 {} 个对象位置", positionUpdates.size());
        
        for (const auto& [objectId, positions] : positionUpdates) {
            auto it = objects_.find(objectId);
            if (it == objects_.end()) {
                LOG_WARN("尝试更新不存在对象的位置: {}", objectId);
                continue;
            }
            
            const auto& newPosition = positions.second;
            EcefPoint new_ecef_pos = CoordinateConverter::wgs84ToECEF(newPosition);
            it->second.ecefPosition = new_ecef_pos;
            it->second.fclObject = createFCLObject(new_ecef_pos, it->second.localBounds);
        }
        
        updateBVHManager();

        LOG_INFO("批量位置更新完成");
    }

    void BvhSpatialIndex::removeObject(const ObjectID& objectId) {
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);
        
        auto it = objects_.find(objectId);
        if (it == objects_.end()) {
            LOG_WARN("尝试移除不存在的对象: {}", objectId);
            return;
        }
        
        LOG_TRACE("从BVH索引移除对象: {}", objectId);
        
        objects_.erase(it);
        updateBVHManager();

        LOG_TRACE("对象 {} 已从BVH索引移除", objectId);
    }

    void BvhSpatialIndex::removeObjects(const std::vector<ObjectID>& objectIds) {
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);
        
        LOG_INFO("批量移除 {} 个对象", objectIds.size());
        
        for (const auto& objectId : objectIds) {
            auto it = objects_.find(objectId);
            if (it != objects_.end()) {
                objects_.erase(it);
            } else {
                LOG_WARN("尝试移除不存在的对象: {}", objectId);
            }
        }
        
        updateBVHManager();

        LOG_INFO("批量移除完成，BVH索引现包含 {} 个对象", objects_.size());
    }

    // === 查询功能实现 ===

    std::vector<ObjectID> BvhSpatialIndex::findObjectsInRegion(
        const WGS84BoundingBox& region,
        const std::optional<SpatialQueryFilter>& filter) const {

        std::shared_lock<std::shared_mutex> lock(objects_mutex_);

        LOG_TRACE("在区域内查询对象: [{:.6f},{:.6f}] - [{:.6f},{:.6f}]",
                 region.minLongitude, region.minLatitude,
                 region.maxLongitude, region.maxLatitude);

        auto start_time = std::chrono::high_resolution_clock::now();

        // 转换WGS84区域到ECEF
        EcefPoint min_ecef = CoordinateConverter::wgs84ToECEF(
            WGS84Point(region.minLongitude, region.minLatitude, region.minAltitude));
        EcefPoint max_ecef = CoordinateConverter::wgs84ToECEF(
            WGS84Point(region.maxLongitude, region.maxLatitude, region.maxAltitude));

        std::vector<ObjectID> results;

        // 遍历所有对象进行包围盒检测
        for (const auto& [id, obj_data] : objects_) {
            // 检查ECEF包围盒是否与查询区域相交
            bool intersects = (obj_data.ecefBounds.min_.x() <= max_ecef.x() && obj_data.ecefBounds.max_.x() >= min_ecef.x()) &&
                             (obj_data.ecefBounds.min_.y() <= max_ecef.y() && obj_data.ecefBounds.max_.y() >= min_ecef.y()) &&
                             (obj_data.ecefBounds.min_.z() <= max_ecef.z() && obj_data.ecefBounds.max_.z() >= min_ecef.z());
            if (intersects) {
                results.push_back(id);
            }
        }

        // 应用过滤器
        if (filter.has_value()) {
            results = applyFilter(results, filter);
        }

        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);

        LOG_TRACE("区域查询完成，找到 {} 个对象，耗时 {:.2f}ms", results.size(), duration.count() / 1000.0);
        return results;
    }

    std::vector<ObjectID> BvhSpatialIndex::findObjectsNearPoint(
        const WGS84Point& point, double radius,
        const std::optional<SpatialQueryFilter>& filter) const {

        std::shared_lock<std::shared_mutex> lock(objects_mutex_);

        LOG_TRACE("在点 {} 附近 {:.1f}m 范围内查询对象", point.toString(), radius);

        auto start_time = std::chrono::high_resolution_clock::now();

        // 转换查询点到ECEF
        EcefPoint ecef_point = CoordinateConverter::wgs84ToECEF(point);

        std::vector<ObjectID> results;

        // 遍历所有对象进行距离检测
        for (const auto& [id, obj_data] : objects_) {
            // 计算ECEF坐标系中的欧几里得距离
            double dx = obj_data.ecefPosition.x() - ecef_point.x();
            double dy = obj_data.ecefPosition.y() - ecef_point.y();
            double dz = obj_data.ecefPosition.z() - ecef_point.z();
            double distance = std::sqrt(dx*dx + dy*dy + dz*dz);
            if (distance <= radius) {
                results.push_back(id);
            }
        }

        // 应用过滤器
        if (filter.has_value()) {
            results = applyFilter(results, filter);
        }

        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);

        LOG_TRACE("点查询完成，找到 {} 个对象，耗时 {:.2f}ms", results.size(), duration.count() / 1000.0);
        return results;
    }

    bool BvhSpatialIndex::objectsIntersect(const ObjectID& objectId1, const ObjectID& objectId2) const {
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);

        auto it1 = objects_.find(objectId1);
        auto it2 = objects_.find(objectId2);

        if (it1 == objects_.end() || it2 == objects_.end()) {
            LOG_WARN("检查相交时发现不存在的对象: {} 或 {}", objectId1, objectId2);
            return false;
        }

        // 检查ECEF包围盒是否相交（FCL BoundingBox使用overlap方法）
        bool intersects = it1->second.ecefBounds.overlap(it2->second.ecefBounds);

        LOG_TRACE("对象相交检查: {} vs {} = {}", objectId1, objectId2, intersects);
        return intersects;
    }

    // === 对象信息获取 ===

    std::optional<WGS84BoundingBox> BvhSpatialIndex::getObjectBounds(const ObjectID& objectId) const {
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);

        auto it = objects_.find(objectId);
        if (it == objects_.end()) {
            return std::nullopt;
        }

        // 将ECEF包围盒转换回WGS84
        EcefPoint min_ecef(it->second.ecefBounds.min_.x(), it->second.ecefBounds.min_.y(), it->second.ecefBounds.min_.z());
        EcefPoint max_ecef(it->second.ecefBounds.max_.x(), it->second.ecefBounds.max_.y(), it->second.ecefBounds.max_.z());
        WGS84Point min_wgs84 = CoordinateConverter::ecefToWGS84(min_ecef);
        WGS84Point max_wgs84 = CoordinateConverter::ecefToWGS84(max_ecef);

        return WGS84BoundingBox(min_wgs84.longitude, max_wgs84.longitude,
                                       min_wgs84.latitude, max_wgs84.latitude,
                                       min_wgs84.altitude, max_wgs84.altitude);
    }

    std::optional<SpatialObjectData> BvhSpatialIndex::getObjectData(const ObjectID& objectId) const {
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);

        auto it = objects_.find(objectId);
        if (it == objects_.end()) {
            return std::nullopt;
        }

        SpatialObjectData data;
        data.position = CoordinateConverter::ecefToWGS84(it->second.ecefPosition);
        data.localBounds = it->second.localBounds;

        return data;
    }

    std::vector<ObjectID> BvhSpatialIndex::getAllObjectIds() const {
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);

        std::vector<ObjectID> ids;
        ids.reserve(objects_.size());

        for (const auto& [id, obj_data] : objects_) {
            ids.push_back(id);
        }

        return ids;
    }

    // === 缓存和内存管理 ===

    bool BvhSpatialIndex::configureQueryCache(bool enable, size_t maxSize,
                                             std::chrono::milliseconds maxAge) {
        // 空实现：缓存由Environment统一管理
        LOG_DEBUG("BvhSpatialIndex::configureQueryCache - 缓存由Environment统一管理，忽略配置");
        return true;
    }

    void BvhSpatialIndex::clearQueryCache() {
        // 空实现：缓存由Environment统一管理
        LOG_DEBUG("BvhSpatialIndex::clearQueryCache - 缓存由Environment统一管理，忽略清除操作");
    }

    void BvhSpatialIndex::setMemoryLimit(size_t bytesLimit) {
        memory_limit_ = bytesLimit;
        LOG_INFO("内存限制设置为: {} MB", bytesLimit / (1024 * 1024));
    }

    size_t BvhSpatialIndex::getEstimatedMemoryUsage() const {
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);

        // 估算内存使用量
        size_t usage = 0;

        // 对象数据
        usage += objects_.size() * sizeof(BvhObjectData);

        // FCL对象（估算）
        usage += objects_.size() * 1024; // 每个FCL对象大约1KB

        return usage;
    }



    // === 序列化 ===

    nlohmann::json BvhSpatialIndex::serialize() const {
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);

        nlohmann::json j;
        j["type"] = "BvhSpatialIndex";
        j["object_count"] = objects_.size();
        j["memory_limit_bytes"] = memory_limit_;

        return j;
    }

    bool BvhSpatialIndex::deserialize(const nlohmann::json& json) {
        try {
            if (json.value("type", "") != "BvhSpatialIndex") {
                LOG_ERROR("反序列化失败：类型不匹配");
                return false;
            }

            memory_limit_ = json.value("memory_limit_bytes", 0);

            LOG_INFO("BvhSpatialIndex反序列化完成");
            return true;
        } catch (const std::exception& e) {
            LOG_ERROR("BvhSpatialIndex反序列化失败: {}", e.what());
            return false;
        }
    }





    std::vector<ObjectID> BvhSpatialIndex::applyFilter(
        const std::vector<ObjectID>& object_ids,
        const std::optional<SpatialQueryFilter>& filter) const {

        if (!filter.has_value() || !filter->customFilter) {
            return object_ids;
        }

        std::vector<ObjectID> filtered_results;

        for (const auto& id : object_ids) {
            auto it = objects_.find(id);
            if (it != objects_.end()) {
                SpatialObjectData data;
                data.position = CoordinateConverter::ecefToWGS84(it->second.ecefPosition);
                data.localBounds = it->second.localBounds;

                if (filter->customFilter(id, data)) {
                    filtered_results.push_back(id);
                }
            }
        }

        return filtered_results;
    }

} // namespace NSEnvironment
} // namespace NSDrones
