// src/planning/task_planners/task_planner_surveysphere.cpp
#include "planning/task_planners/surveysphere_taskplanner.h"
#include "planning/planning_result.h"
#include "planning/planning_types.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/task_strategies.h"
#include "mission/control_point.h"
#include "uav/uav.h"
#include "environment/environment.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"
#include "environment/geometry/geometry_manager.h"
#include "core/types.h"
#include <vector>
#include <cmath>
#include <string>
#include <optional>
#include <utility>

namespace NSDrones {
	namespace NSPlanning {

		SurveySphereTaskPlanner::SurveySphereTaskPlanner(
			IPathPlannerPtr path_planner,
			ITrajectoryOptimizerPtr traj_optimizer)
			: ITaskPlanner(path_planner, traj_optimizer) {}

		// 新增: 初始化方法实现 (目前为空)
		bool SurveySphereTaskPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) {
			// TODO: 如果 SurveySphereTaskPlanner 未来有自己的配置参数，在此处加载和处理
			LOG_DEBUG("[SurveySphereTaskPlanner] 初始化完成 (无特定配置)。");
			return true;
		}

		/**
		 * @brief 规划 SURVEY_SPHERE 任务。
		 */
		PlanningResult SurveySphereTaskPlanner::planTask(
			const NSMission::Task& task,
			const std::vector<NSUav::UavPtr>& assigned_uavs,
			const std::map<ObjectID, NSUav::UavState>& start_states)
		{
			LOG_INFO("[SurveySphereTaskPlanner] 任务 [{}] 开始规划...", task.getId());
			PlanningResult result;

			// --- 1. 验证任务类型和基本参数 ---
			if (task.getType() != TaskType::SURVEY_SPHERE) {
				result.setStatus(false, "内部错误：SurveySphereTaskPlanner 接收到非 SURVEY_SPHERE 类型的任务 (" + NSUtils::enumToString(task.getType()) + ")");
				LOG_ERROR("[SurveySphereTaskPlanner] 任务 [{}] 类型错误: {}", task.getId(), result.getMessage());
				return result;
			}
			if (assigned_uavs.empty()) {
				result.setStatus(false, "无无人机分配给 SurveySphereTask [" + task.getId() + "]");
				LOG_ERROR("[SurveySphereTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}
			LOG_INFO("[SurveySphereTaskPlanner] 任务 [{}] 分配给 {} 架无人机...", task.getId(), assigned_uavs.size());

			// --- 2. 获取并验证详细参数 ---
			auto params_ptr = task.getTaskParameters<NSMission::SurveySphereTaskParams>();
			if (!params_ptr) {
				result.setStatus(false, "无法获取 SurveySphereTask [" + task.getId() + "] 的有效参数结构体。");
				LOG_ERROR("[SurveySphereTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}

			// 验证半径
			if (params_ptr->radius <= Constants::GEOMETRY_EPSILON) {
				std::string msg = fmt::format("无效的球面勘察参数：任务 [{}] 半径 ({:.3f}) 必须为正。", task.getId(), params_ptr->radius);
				result.setStatus(false, msg);
				LOG_ERROR("[SurveySphereTaskPlanner] {}", msg);
				return result;
			}
			// 验证拍摄角度列表
			if (params_ptr->photo_angles.empty()) {
				result.setStatus(false, "无效的球面勘察参数：任务 [" + task.getId() + "] 拍摄角度列表为空。");
				LOG_ERROR("[SurveySphereTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}
			const NSMission::ControlPoint& center_cp_ref = params_ptr->center_point;
			LOG_DEBUG("[SurveySphereTaskPlanner] 任务 [{}] 参数: 球心控制点类型={}, 原始WGS84=({}), 半径={:.2f}, 拍摄点数={}",
				task.getId(), NSUtils::enumToString(center_cp_ref.type), center_cp_ref.position.toString(),	params_ptr->radius, params_ptr->photo_angles.size());

			// --- 3. 检查依赖项 ---
			auto environment = getEnvironment();
			if (!environment || !path_planner_) {
				result.setStatus(false, "内部错误：SurveySphereTaskPlanner 缺少必要的依赖项 (环境/路径规划器)。");
				LOG_ERROR("[SurveySphereTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}

			// --- 4. 获取球心绝对位置 --- (所有拍摄点都相对于此中心)
			LOG_DEBUG("[SurveySphereTaskPlanner] 任务 [{}]: 步骤 1: 获取球心绝对位置...", task.getId());
			auto center_pos_res_pair = getAbsolutePosition(center_cp_ref, task);
			if (!center_pos_res_pair.second) {
				std::string msg = "无法获取 SurveySphereTask [" + task.getId() + "] 的球心绝对位置。";
				result.setStatus(false, msg);
				LOG_ERROR("[SurveySphereTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
				return result;
			}
			EcefPoint survey_center_ecef = center_pos_res_pair.first;
			// 转换为WGS84坐标用于日志输出
			WGS84Point survey_center_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(survey_center_ecef);
			LOG_DEBUG("[SurveySphereTaskPlanner] 任务 [{}]: 球心绝对位置: {}", task.getId(), survey_center_wgs84.toString());

			// --- 5. 为每个无人机规划路径 --- (访问所有拍摄点)
			// 当前简化处理：假设只有一个无人机或多个无人机独立访问所有拍摄点
			// TODO: 如果是多无人机协同勘察，需要在这里进行拍摄点分配
			bool overall_success = true;
			for (const auto& uav : assigned_uavs) {
				if (!uav) {
					LOG_WARN("[SurveySphereTaskPlanner] 任务 [{}]: 跳过空的无人机指针。", task.getId());
					continue;
				}
				const ObjectID& uav_id = uav->getId();
				LOG_INFO("[SurveySphereTaskPlanner] 任务 [{}]: 开始为无人机 [{}] 规划球面勘察路径...", task.getId(), uav_id);

				auto start_state_it = start_states.find(uav_id);
				if (start_state_it == start_states.end()) {
					std::string msg = "缺少无人机 [" + uav_id + "] 的起始状态。";
					result.addWarning(WarningEvent{ WarningType::INVALID_STATE, msg, 0, {}, uav_id, task.getId() });
					LOG_ERROR("[SurveySphereTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					overall_success = false;
					LOG_INFO("[SurveySphereTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (缺少状态)。", task.getId(), uav_id);
					continue;
				}
				const NSUav::UavState& start_state = start_state_it->second;
				const NSUav::IDynamicModel* dynamics = uav->getDynamicsModel().get();
				if (!dynamics) {
					std::string msg = "无人机 [" + uav_id + "] 缺少有效的动力学模型。";
					result.addWarning(WarningEvent{ WarningType::INVALID_STATE, msg, 0, {}, uav_id, task.getId() });
					LOG_ERROR("[SurveySphereTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					overall_success = false;
					LOG_INFO("[SurveySphereTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (缺少动力学模型)。", task.getId(), uav_id);
					continue;
				}
				LOG_DEBUG("[SurveySphereTaskPlanner] 任务 [{}], 无人机 [{}]: 起始状态 Pos={}, Time={:.3f}",
					task.getId(), uav_id, start_state.position.toString(), start_state.time_stamp);

				RouteSegment total_route_segment; // 存储当前无人机的完整航线
				NSUav::UavState last_wp_state = start_state; // 跟踪上一个航路点/拍摄点的状态
				bool uav_plan_success = true; // 跟踪当前无人机规划是否成功

				// --- 5.1 依次规划到每个拍摄点 --- (按参数列表顺序访问)
				// TODO: 可以根据距离或其他策略优化访问顺序
				for (size_t i = 0; i < params_ptr->photo_angles.size(); ++i) {
					const AnglePair& angle = params_ptr->photo_angles[i];
					LOG_DEBUG("[SurveySphereTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 2.{}: 规划到拍摄点 #{} (方位角 {:.1f}°, 俯仰角 {:.1f}°)...",
						task.getId(), uav_id, i + 1, i + 1, angle.azimuth_deg, angle.elevation_deg);

					// --- 5.1.1 计算拍摄点的绝对位置 (球面坐标转笛卡尔坐标，相对于球心) ---
					double az_rad = angle.azimuth_deg * Constants::DEG_TO_RAD; // 方位角转弧度
					double el_rad = angle.elevation_deg * Constants::DEG_TO_RAD; // 俯仰角转弧度
					// 计算相对球心的偏移向量
					Vector3D offset(
						params_ptr->radius * std::cos(el_rad) * std::cos(az_rad), // X = r * cos(el) * cos(az)
						params_ptr->radius * std::cos(el_rad) * std::sin(az_rad), // Y = r * cos(el) * sin(az)
						params_ptr->radius * std::sin(el_rad)                   // Z = r * sin(el)
					);
					// 将球心WGS84坐标转换为ECEF坐标进行几何计算
					EcefPoint survey_center_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(survey_center_wgs84);
					EcefPoint target_ecef(survey_center_ecef + offset); // 绝对位置 = 球心 + 偏移
					LOG_DEBUG("[SurveySphereTaskPlanner] 任务 [{}], 无人机 [{}]: 拍摄点 #{} 目标ECEF位置: {}",
						task.getId(), uav_id, i + 1, target_ecef.toString());

					// --- 5.1.2 规划几何路径 (从上一个点到当前拍摄点) ---
					std::vector<EcefPoint> geometric_path;
					auto path_constraints_opt = task.getStrategyByType<NSMission::PathConstraintStrategy>();
					const NSMission::PathConstraintStrategy* path_constraints_ptr = path_constraints_opt ? path_constraints_opt.get() : nullptr;
					// 转换目标ECEF位置为WGS84坐标用于路径规划接口
					WGS84Point target_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(target_ecef);
					LOG_DEBUG("[SurveySphereTaskPlanner] 任务 [{}], 无人机 [{}]: 调用路径规划器: 从 {} 到 {}",
						task.getId(), uav_id, last_wp_state.position.toString(), target_wgs84.toString());
					// 转换为ECEF坐标进行路径规划
					EcefPoint start_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(last_wp_state.position);
					bool path_found = path_planner_->findPath(start_ecef, target_ecef, dynamics, path_constraints_ptr, geometric_path);
					if (!path_found || geometric_path.size() < 2) {
						std::string msg_fmt = fmt::format("未能找到无人机 [{}] 前往拍摄点 #{} {} 的路径。", uav_id, i + 1, target_wgs84.toString());
						result.addWarning(WarningEvent{ WarningType::PLANNING_FAILURE, msg_fmt, last_wp_state.time_stamp, last_wp_state.position, uav_id, task.getId() });
						LOG_ERROR("[SurveySphereTaskPlanner] 任务 [{}]: {}", task.getId(), msg_fmt);
						uav_plan_success = false;
						LOG_INFO("[SurveySphereTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (路径规划器未找到路径)。", task.getId(), uav_id);
						break; // 当前无人机规划失败，跳出拍摄点循环
					}
					LOG_DEBUG("[SurveySphereTaskPlanner] 任务 [{}], 无人机 [{}]: 几何路径找到 {} 个点。", task.getId(), uav_id, geometric_path.size());

					// --- 5.1.3 平滑和时间参数化 ---
					RouteSegment segment_to_add;
					double desired_speed = task.getDesiredSpeed(8.0); // 获取任务期望速度
					LOG_DEBUG("[SurveySphereTaskPlanner] 任务 [{}], 无人机 [{}]: 开始平滑和时间参数化段 #{}, 速度 {:.1f} m/s, {} 个几何点...",
						task.getId(), uav_id, i + 1, desired_speed, geometric_path.size());
					if (!smoothAndTimeParameterizeECEF(geometric_path, uav, last_wp_state, desired_speed, segment_to_add, &result, task.getStrategies())) {
						// smoothAndTimeParameterize 内部会记录错误
						LOG_ERROR("[SurveySphereTaskPlanner] 任务 [{}], 无人机 [{}] 路径处理失败 (拍摄点 #{}, 平滑/参数化过程出错)。", task.getId(), uav_id, i + 1);
						uav_plan_success = false;
						LOG_INFO("[SurveySphereTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (平滑/参数化过程出错)。", task.getId(), uav_id);
						break;
					}
					LOG_DEBUG("[SurveySphereTaskPlanner] 任务 [{}], 无人机 [{}]: 段 #{} 平滑和参数化完成，生成 {} 个航点。", task.getId(), uav_id, i + 1, segment_to_add.size());

					// --- 5.1.4 在目标点添加拍照动作 --- (简化示例，实际应通过 PayloadAction)
					if (!segment_to_add.empty()) {
						NSMission::PayloadActionCommand photo_cmd;
						photo_cmd.command_name = "TakePhoto"; // 假设的拍照命令名称
						// 可以添加参数，例如指向球心
						// photo_cmd.parameters["target_point"] = survey_center;
						// photo_cmd.parameters["focus"] = true;
						segment_to_add.back().payload_actions.push_back(photo_cmd);
						LOG_DEBUG("[SurveySphereTaskPlanner] 任务 [{}], 无人机 [{}]: 在拍摄点 #{} 添加拍照动作。", task.getId(), uav_id, i + 1);
					}

					// --- 5.1.5 合并航段 --- (与 FollowPath 逻辑类似)
					if (!total_route_segment.empty() && !segment_to_add.empty()) {
						// 检查起点重合
						if ((total_route_segment.back().position - segment_to_add.front().position).norm() < Constants::GEOMETRY_EPSILON &&
							std::abs(total_route_segment.back().time_stamp - segment_to_add.front().time_stamp) < Constants::TIME_EPSILON) {
							// 合并载荷动作
							if (!segment_to_add.front().payload_actions.empty()) {
								LOG_TRACE("[SurveySphereTaskPlanner] 任务 [{}], 无人机 [{}]: 合并航段: 将新段起点的 {} 个载荷动作合并到前一段终点。",
									task.getId(), uav_id, segment_to_add.front().payload_actions.size());
								total_route_segment.back().payload_actions.insert(total_route_segment.back().payload_actions.end(),
									std::make_move_iterator(segment_to_add.front().payload_actions.begin()), // 使用 std::move_iterator 提高效率
									std::make_move_iterator(segment_to_add.front().payload_actions.end()));
							}
							// 移除重复起点
							segment_to_add.erase(segment_to_add.begin());
							LOG_TRACE("[SurveySphereTaskPlanner] 任务 [{}], 无人机 [{}]: 合并航段: 移除重复起点，新段剩余 {} 点。", task.getId(), uav_id, segment_to_add.size());
						}
						else if (segment_to_add.front().time_stamp < total_route_segment.back().time_stamp - Constants::TIME_EPSILON) {
							// 调整时间戳
							Time time_offset = total_route_segment.back().time_stamp - segment_to_add.front().time_stamp + Constants::TIME_EPSILON;
							LOG_WARN("[SurveySphereTaskPlanner] 任务 [{}], 无人机 [{}]: 路径段 {} 时间戳不连续 (新起点 {:.3f} < 旧终点 {:.3f})，后续时间戳将增加 {:.4f}s",
								task.getId(), uav_id, i + 1, segment_to_add.front().time_stamp, total_route_segment.back().time_stamp, time_offset);
							for (auto& rp : segment_to_add) { rp.time_stamp += time_offset; }
						}
					}

					// --- 5.1.6 添加到总路径并更新状态 ---
					if (!segment_to_add.empty()) {
						total_route_segment.insert(total_route_segment.end(), segment_to_add.begin(), segment_to_add.end());
						PlannedRoute temp_route_for_check(uav_id);
						temp_route_for_check.addWaypoints(segment_to_add);
						checkSegmentWarnings(temp_route_for_check, uav_id, result, task.getId()); // 检查新段警告
						last_wp_state = NSUav::stateFromRoutePt(total_route_segment.back()); // 更新状态为新段的终点
						LOG_TRACE("[SurveySphereTaskPlanner] 任务 [{}], 无人机 [{}]: 航段 #{} 已合并，总点数: {}, 新状态 Pos={}, Time={:.3f}",
							task.getId(), uav_id, i + 1, total_route_segment.size(),
							last_wp_state.position.toString(), last_wp_state.time_stamp);
					}
					else {
						LOG_WARN("[SurveySphereTaskPlanner] 任务 [{}], 无人机 [{}]: 平滑/参数化后路径段 {} 为空，状态未更新。", task.getId(), uav_id, i + 1);
					}
				} // 结束拍摄点循环

				// --- 5.2 添加当前无人机的最终结果 ---
				if (uav_plan_success && !total_route_segment.empty()) {
					PlannedRoute route(uav_id);
					route.addWaypoints(total_route_segment);
					result.addRoute(std::move(route));
					LOG_INFO("[SurveySphereTaskPlanner] 任务 [{}]: 为无人机 [{}] 生成球面勘察航线成功，{} 个点。", task.getId(), uav_id, total_route_segment.size());
				}
				else if (uav_plan_success) { // 规划流程成功，但路径为空
					std::string msg = "成功为无人机 [" + uav_id + "] 完成规划，但未生成有效航线（可能是所有路径段处理后都为空）。";
					result.addWarning(WarningEvent{ WarningType::PLANNING_FAILURE, msg, 0, {}, uav_id, task.getId() });
					LOG_WARN("[SurveySphereTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
				}
				else { // uav_plan_success 为 false
					// 失败原因已在循环中记录
					LOG_ERROR("[SurveySphereTaskPlanner] 任务 [{}]: 无人机 [{}] 球面勘察规划失败。", task.getId(), uav_id);
					overall_success = false; // 标记整体任务部分失败
				}
			} // 结束无人机循环

			// --- 6. 设置最终状态 ---
			if (!result.getAllRoutes().empty() && overall_success) {
				result.setStatus(true, "所有分配的无人机均成功规划球面勘察路径。");
				LOG_INFO("[SurveySphereTaskPlanner] 任务 [{}] 规划成功。", task.getId());
			}
			else if (!result.getAllRoutes().empty() && !overall_success) {
				result.setStatus(true, "部分无人机成功规划球面勘察路径，但存在失败或警告。");
				LOG_WARN("[SurveySphereTaskPlanner] 任务 [{}] 规划部分成功，存在失败或警告。", task.getId());
			}
			else if (overall_success && result.getAllRoutes().empty() && !assigned_uavs.empty()) {
				// 这种情况可能是所有无人机规划都成功了，但是生成的路径都是空的
				result.setStatus(false, "所有无人机规划流程完成，但未生成任何有效航线。");
				LOG_WARN("[SurveySphereTaskPlanner] 任务 [{}] 规划完成但未生成航线。", task.getId());
			}
			else { // overall_success 为 false 且没有生成任何路径
				if (!result.getMessage().empty()) { // 如果已有错误消息，则不覆盖
					LOG_ERROR("[SurveySphereTaskPlanner] 任务 [{}] 规划失败。原因见先前日志。", task.getId());
				}
				else {
					result.setStatus(false, "所有分配的无人机均未能成功规划球面勘察路径。");
					LOG_ERROR("[SurveySphereTaskPlanner] 任务 [{}] 规划失败。", task.getId());
				}
			}

			return result;
		}

	} // namespace NSPlanning
} // namespace NSDrones