#pragma once

#include "core/geometry/ishape.h"
#include "core/geometry/shapes/box_shape.h"
#include "core/geometry/shapes/sphere_shape.h"
#include "core/geometry/shapes/cylinder_shape.h"
#include "core/geometry/shapes/cone_shape.h"
#include "core/geometry/shapes/capsule_shape.h"
#include "core/geometry/shapes/ellipsoid_shape.h"
#include "core/geometry/shapes/convex_hull_shape.h"
#include "core/geometry/shapes/mesh_shape.h"
#include "core/geometry/shapes/compound_shape.h"
#include "core/geometry/shapes/polygon_shape.h"
#include <memory>
#include <string>
#include <functional>
#include <unordered_map>

namespace NSDrones {
namespace NSCore {

    /**
     * @brief 形状创建参数
     */
    struct ShapeCreationParams {
        ShapeType type;
        std::unordered_map<std::string, double> numeric_params;
        std::unordered_map<std::string, std::string> string_params;
        std::unordered_map<std::string, std::vector<double>> vector_params;

        ShapeCreationParams(ShapeType t) : type(t) {}

        // 便捷方法
        ShapeCreationParams& setParam(const std::string& name, double value) {
            numeric_params[name] = value;
            return *this;
        }

        ShapeCreationParams& setParam(const std::string& name, const std::string& value) {
            string_params[name] = value;
            return *this;
        }

        ShapeCreationParams& setParam(const std::string& name, const std::vector<double>& value) {
            vector_params[name] = value;
            return *this;
        }

        double getNumeric(const std::string& name, double default_value = 0.0) const {
            auto it = numeric_params.find(name);
            return it != numeric_params.end() ? it->second : default_value;
        }

        std::string getString(const std::string& name, const std::string& default_value = "") const {
            auto it = string_params.find(name);
            return it != string_params.end() ? it->second : default_value;
        }

        std::vector<double> getVector(const std::string& name, const std::vector<double>& default_value = {}) const {
            auto it = vector_params.find(name);
            return it != vector_params.end() ? it->second : default_value;
        }
    };

    /**
     * @brief 形状工厂类
     * 
     * 提供统一的形状创建接口，支持：
     * - 基于类型的形状创建
     * - 基于参数的形状创建
     * - 基于JSON的形状创建
     * - 自定义形状注册
     */
    class ShapeFactory {
    public:
        using ShapeCreator = std::function<std::unique_ptr<IShape>(const ShapeCreationParams&)>;

    private:
        static std::unordered_map<ShapeType, ShapeCreator> creators_;
        static std::unordered_map<std::string, ShapeType> type_name_map_;
        static bool initialized_;

    public:
        /**
         * @brief 初始化工厂（注册默认形状创建器）
         */
        static void initialize();

        /**
         * @brief 创建形状
         * @param shape_type 形状类型
         * @param params 创建参数
         * @return 形状实例
         */
        static std::unique_ptr<IShape> createShape(ShapeType shape_type, const ShapeCreationParams& params = ShapeCreationParams(ShapeType::UNKNOWN));

        /**
         * @brief 创建形状
         * @param type_name 形状类型名称
         * @param params 创建参数
         * @return 形状实例
         */
        static std::unique_ptr<IShape> createShape(const std::string& type_name, const ShapeCreationParams& params);

        /**
         * @brief 从JSON创建形状
         * @param json JSON对象
         * @return 形状实例
         */
        static std::unique_ptr<IShape> createFromJson(const nlohmann::json& json);

        /**
         * @brief 注册自定义形状创建器
         * @param type 形状类型
         * @param creator 创建器函数
         */
        static void registerCreator(ShapeType type, ShapeCreator creator);

        /**
         * @brief 注册形状类型名称映射
         * @param name 类型名称
         * @param type 形状类型
         */
        static void registerTypeName(const std::string& name, ShapeType type);

        /**
         * @brief 检查是否支持指定类型
         * @param type 形状类型
         * @return 是否支持
         */
        static bool isSupported(ShapeType type);

        /**
         * @brief 检查是否支持指定类型名称
         * @param type_name 形状类型名称
         * @return 是否支持
         */
        static bool isSupported(const std::string& type_name);

        /**
         * @brief 获取所有支持的形状类型
         * @return 形状类型列表
         */
        static std::vector<ShapeType> getSupportedTypes();

        /**
         * @brief 获取所有支持的形状类型名称
         * @return 类型名称列表
         */
        static std::vector<std::string> getSupportedTypeNames();

        // 便捷创建方法
        /**
         * @brief 创建长方体
         * @param length 长度
         * @param width 宽度
         * @param height 高度
         * @return BoxShape实例
         */
        static std::unique_ptr<BoxShape> createBox(double length, double width, double height);

        /**
         * @brief 创建立方体
         * @param size 边长
         * @return BoxShape实例
         */
        static std::unique_ptr<BoxShape> createCube(double size);

        /**
         * @brief 创建球体
         * @param radius 半径
         * @return SphereShape实例
         */
        static std::unique_ptr<SphereShape> createSphere(double radius);

        /**
         * @brief 创建圆柱体
         * @param radius 半径
         * @param height 高度
         * @return CylinderShape实例
         */
        static std::unique_ptr<CylinderShape> createCylinder(double radius, double height);

        /**
         * @brief 创建圆锥体
         * @param radius 底面半径
         * @param height 高度
         * @return ConeShape实例
         */
        static std::unique_ptr<ConeShape> createCone(double radius, double height);

        /**
         * @brief 创建胶囊体
         * @param radius 半径
         * @param height 圆柱部分高度
         * @return CapsuleShape实例
         */
        static std::unique_ptr<CapsuleShape> createCapsule(double radius, double height);

        /**
         * @brief 创建椭球体
         * @param radius_x X轴半径
         * @param radius_y Y轴半径
         * @param radius_z Z轴半径
         * @return EllipsoidShape实例
         */
        static std::unique_ptr<EllipsoidShape> createEllipsoid(double radius_x, double radius_y, double radius_z);

        /**
         * @brief 创建凸包
         * @param vertices 顶点列表
         * @return ConvexHullShape实例
         */
        static std::unique_ptr<ConvexHullShape> createConvexHull(const std::vector<fcl::Vector3d>& vertices);

        /**
         * @brief 创建三角网格
         * @param vertices 顶点列表
         * @param triangles 三角形列表
         * @return MeshShape实例
         */
        static std::unique_ptr<MeshShape> createMesh(const std::vector<fcl::Vector3d>& vertices,
                                                    const std::vector<fcl::Triangle>& triangles);

        /**
         * @brief 创建复合形状
         * @return CompoundShape实例
         */
        static std::unique_ptr<CompoundShape> createCompound();

    private:
        /**
         * @brief 注册默认形状创建器
         */
        static void registerDefaultCreators();

        /**
         * @brief 确保工厂已初始化
         */
        static void ensureInitialized();
    };

    /**
     * @brief 形状工具类
     * 
     * 提供形状相关的实用工具方法
     */
    class ShapeUtils {
    public:
        /**
         * @brief 计算两个形状的最小距离
         * @param shape1 第一个形状
         * @param transform1 第一个形状的变换
         * @param shape2 第二个形状
         * @param transform2 第二个形状的变换
         * @return 最小距离
         */
        static double computeDistance(const IShape& shape1, const fcl::Transform3d& transform1,
                                     const IShape& shape2, const fcl::Transform3d& transform2);

        /**
         * @brief 检查两个形状是否相交
         * @param shape1 第一个形状
         * @param transform1 第一个形状的变换
         * @param shape2 第二个形状
         * @param transform2 第二个形状的变换
         * @return 是否相交
         */
        static bool checkIntersection(const IShape& shape1, const fcl::Transform3d& transform1,
                                     const IShape& shape2, const fcl::Transform3d& transform2);

        /**
         * @brief 计算形状的包围球
         * @param shape 形状
         * @param transform 变换
         * @return 包围球（中心点和半径）
         */
        static std::pair<fcl::Vector3d, double> computeBoundingSphere(const IShape& shape, 
                                                                     const fcl::Transform3d& transform = fcl::Transform3d::Identity());

        /**
         * @brief 简化形状（减少复杂度）
         * @param shape 原始形状
         * @param tolerance 简化容差
         * @return 简化后的形状
         */
        static std::unique_ptr<IShape> simplifyShape(const IShape& shape, double tolerance = 1e-3);

        /**
         * @brief 验证形状的有效性
         * @param shape 形状
         * @return 是否有效
         */
        static bool validateShape(const IShape& shape);
    };

} // namespace NSCore
} // namespace NSDrones