// src/planning/task_planners/task_planner_surveycylinder.cpp
#include "planning/task_planners/surveycylinder_taskplanner.h"
#include "planning/planning_result.h"
#include "planning/planning_types.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/task_strategies.h"
#include "mission/control_point.h"
#include "uav/uav.h"
#include "environment/environment.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"
#include "environment/geometry/geometry_manager.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include <vector>
#include <cmath>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <utility>

namespace NSDrones {
	namespace NSPlanning {

		SurveyCylinderTaskPlanner::SurveyCylinderTaskPlanner(
			IPathPlannerPtr path_planner,
			ITrajectoryOptimizerPtr traj_optimizer)
			: ITaskPlanner(path_planner, traj_optimizer) {}

		// 新增: 初始化方法实现 (目前为空)
		bool SurveyCylinderTaskPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) {
			// TODO: 如果 SurveyCylinderTaskPlanner 未来有自己的配置参数，在此处加载和处理
			LOG_DEBUG("[SurveyCylinderTaskPlanner] 初始化完成 (无特定配置)。");
			return true;
		}

		/**
		 * @brief 规划 SURVEY_CYLINDER 任务。
		 */
		PlanningResult SurveyCylinderTaskPlanner::planTask(
			const NSMission::Task& task,
			const std::vector<NSUav::UavPtr>& assigned_uavs,
			const std::map<ObjectID, NSUav::UavState>& start_states)
		{
			LOG_INFO("[SurveyCylinderTaskPlanner] 任务 [{}] 开始规划...", task.getId());
			PlanningResult result;
			result.setStatus(true);

			// --- 1. 验证任务类型和基本参数 ---
			if (task.getType() != TaskType::SURVEY_CYLINDER) {
				result.setStatus(false, "内部错误：SurveyCylinderTaskPlanner 接收到非 SURVEY_CYLINDER 类型的任务 (" + NSUtils::enumToString(task.getType()) + ")");
				LOG_ERROR("[SurveyCylinderTaskPlanner] 任务 [{}] 类型错误: {}", task.getId(), result.getMessage());
				return result;
			}
			if (assigned_uavs.empty()) {
				result.setStatus(false, "无无人机分配给 SurveyCylinderTask [" + task.getId() + "]");
				LOG_ERROR("[SurveyCylinderTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}
			LOG_INFO("[SurveyCylinderTaskPlanner] 任务 [{}] 分配给 {} 架无人机...", task.getId(), assigned_uavs.size());

			// --- 2. 获取并验证详细参数 ---
			auto params_ptr = task.getTaskParameters<NSMission::SurveyCylinderTaskParams>();
			if (!params_ptr) {
				result.setStatus(false, "无法获取 SurveyCylinderTask [" + task.getId() + "] 的有效参数结构体。");
				LOG_ERROR("[SurveyCylinderTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}
			// 验证几何参数
			if (params_ptr->radius <= Constants::GEOMETRY_EPSILON ||
				params_ptr->height <= Constants::GEOMETRY_EPSILON ||
				params_ptr->line_spacing <= Constants::GEOMETRY_EPSILON) {
				// 使用 stringstream 替换 fmt::format
				std::ostringstream oss_msg;
				oss_msg << "无效的圆柱勘察参数：任务 [" << task.getId()
					<< "] 半径(" << std::fixed << std::setprecision(2) << params_ptr->radius
					<< ")/高度(" << std::fixed << std::setprecision(2) << params_ptr->height
					<< ")/行间距(" << std::fixed << std::setprecision(2) << params_ptr->line_spacing
					<< ") 必须为正。";
				std::string msg = oss_msg.str();
				result.setStatus(false, msg);
				LOG_ERROR("[SurveyCylinderTaskPlanner] {}", msg); // msg 已包含 TaskID
				return result;
			}
			LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}] 参数: 底面中心控制点类型={}, 原始WGS84={}, 半径={:.2f}m, 高度={:.2f}m, 行间距={:.2f}m, 起始高度={}, 方向={}",
				task.getId(), NSUtils::enumToString(params_ptr->center_bottom.type),
				params_ptr->center_bottom.position.toString(),
				params_ptr->radius, params_ptr->height, params_ptr->line_spacing,
				params_ptr->start_altitude.has_value() ? std::to_string(params_ptr->start_altitude.value()) : "默认(底部)",
				params_ptr->clockwise ? "顺时针" : "逆时针");

			// --- 3. 检查依赖项 ---
			auto environment = getEnvironment();
			if (!environment || !path_planner_) {
				result.setStatus(false, "内部错误：SurveyCylinderTaskPlanner 缺少环境或路径规划器。");
				LOG_ERROR("[SurveyCylinderTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}

			// --- 4. 获取圆柱底面中心绝对位置和高度 ---
			LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}]: 步骤 1: 获取圆柱底面中心绝对位置...", task.getId());
			const NSMission::ControlPoint& center_cp = params_ptr->center_bottom;
			std::pair<EcefPoint, bool> center_pos_res = getAbsolutePosition(center_cp, task);
			if (!center_pos_res.second) {
				std::string msg = "无法获取圆柱 [" + task.getId() + "] 底面中心的绝对位置。";
				result.setStatus(false, msg);
				LOG_ERROR("[SurveyCylinderTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
				return result;
			}
			WGS84Point cylinder_base_center = NSUtils::CoordinateConverter::ecefToWGS84(center_pos_res.first);
			double base_altitude = cylinder_base_center.altitude;
			LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}]: 圆柱底面中心绝对位置: {}", task.getId(), cylinder_base_center.toString());

			// --- 5. 生成圆柱扫描几何路径 --- (调用几何工具函数)
			LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}]: 步骤 2: 生成圆柱扫描几何路径...", task.getId());
			// 确定扫描的起始绝对高度
			double start_alt_abs = base_altitude; // 默认从底部开始
			if (params_ptr->start_altitude.has_value()) {
				// 如果指定了起始高度（相对于底部中心），则计算绝对高度
				start_alt_abs = base_altitude + params_ptr->start_altitude.value();
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}]: 使用指定起始高度 {:.2f}m (相对)，计算得到绝对起始高度 {:.2f}m", task.getId(), params_ptr->start_altitude.value(), start_alt_abs);
			}
			else {
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}]: 未指定起始高度，将从圆柱底部 (绝对高度 {:.2f}m) 开始扫描。", task.getId(), start_alt_abs);
			}

			// 调用工具函数生成几何路径 - 使用新的签名!
			// 注意：新的签名不再使用 start_altitude 和 clockwise 参数。
			// 它生成的是一个从底部开始向上螺旋的路径。
			// 如果需要更复杂的起始点或方向控制，需要调整 generateCylinderScanPath 实现或此处的逻辑。
			// --- 获取参数: points_per_circle ---
			int points_per_circle = 36; // 硬编码默认值
			const NSParams::ParamDefine* param_def = NSParams::ParamRegistry::getInstance().getEffectiveParamDefine("TaskPlanner.SurveyCylinder", "planning.cylinder.points_per_circle");
			if (param_def) {
				// 尝试从定义中获取默认值
				const int* default_val_ptr = std::get_if<int>(&(param_def->default_value));
				if (default_val_ptr) {
					points_per_circle = *default_val_ptr;
					LOG_TRACE("参数 'planning.cylinder.points_per_circle' 使用注册的默认值: {}", points_per_circle);
				}
				else {
					LOG_WARN("参数 'planning.cylinder.points_per_circle' 的默认值类型不是 int，将使用硬编码默认值 {}。", points_per_circle);
				}
			}
			else {
				LOG_WARN("参数 'planning.cylinder.points_per_circle' 未在注册表中找到定义，将使用硬编码默认值 {}。建议在配置中注册此参数。", points_per_circle);
				// 注意：这里不应该由 planner 注册参数，注册应在初始化阶段完成。
			}
			// ------------------------------------

			LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}]: 调用 generateCylinderScanPath: 中心=({}), 半径={:.2f}, 高度={:.2f}, 垂直步长={:.2f}, 每圈点数={}",
				task.getId(), cylinder_base_center.toString(),
				params_ptr->radius, params_ptr->height, params_ptr->line_spacing, points_per_circle);

			// 使用 GeometryManager 生成圆柱扫描路径
			auto geometry_manager = environment->getGeometryManager();
			if (!geometry_manager) {
				LOG_ERROR("[SurveyCylinderTaskPlanner] 无法获取 GeometryManager");
				result.setSuccess(false);
				result.addError("无法获取 GeometryManager");
				return result;
			}

			std::vector<WGS84Point> cylinder_scan_path_wgs84 = geometry_manager->generateCylinderScanPath(
				params_ptr->center_bottom.position,  // WGS84 中心点
				params_ptr->radius,
				params_ptr->height,
				params_ptr->line_spacing,
				points_per_circle,
				params_ptr->clockwise
			);

			// 将 WGS84 路径转换为 ECEF 坐标进行后续处理
			std::vector<EcefPoint> cylinder_scan_path;
			cylinder_scan_path.reserve(cylinder_scan_path_wgs84.size());

			for (const auto& wgs84_point : cylinder_scan_path_wgs84) {
				EcefPoint ecef_point = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_point);
				cylinder_scan_path.push_back(ecef_point);
			}

			if (cylinder_scan_path.empty()) {
				// 保留空路径检查，以防参数无效
				result.setStatus(false, "未能为圆柱勘察任务 [" + task.getId() + "] 生成有效的几何路径（可能是参数设置问题或内部错误）。");
				LOG_ERROR("[SurveyCylinderTaskPlanner] 任务 [{}]: {}", task.getId(), result.getMessage());
				return result;
			}
			LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}]: 生成圆柱扫描几何路径 {} 个点。", task.getId(), cylinder_scan_path.size());

			// --- 6. 为每个无人机规划完整路径 ---
			bool overall_success = true;
			for (const auto& uav : assigned_uavs) {
				if (!uav) {
					LOG_WARN("[SurveyCylinderTaskPlanner] 任务 [{}]: 跳过空的无人机指针。", task.getId());
					continue;
				}
				const ObjectID& uav_id = uav->getId();
				LOG_INFO("[SurveyCylinderTaskPlanner] 任务 [{}]: 开始为无人机 [{}] 规划圆柱勘察路径...", task.getId(), uav_id);

				auto start_state_it = start_states.find(uav_id);
				if (start_state_it == start_states.end()) {
					std::string msg = "缺少无人机 [" + uav_id + "] 的起始状态。";
					result.addWarning(WarningEvent{ WarningType::INVALID_STATE, msg, 0.0, {}, uav_id, task.getId() });
					LOG_ERROR("[SurveyCylinderTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					overall_success = false;
					LOG_INFO("[SurveyCylinderTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (缺少状态)。", task.getId(), uav_id);
					continue;
				}
				const NSUav::UavState& start_state = start_state_it->second;
				const NSUav::IDynamicModel* dynamics = uav->getDynamicsModel().get();
				if (!dynamics) {
					std::string msg = "无人机 [" + uav_id + "] 缺少动力学模型。";
					result.addWarning(WarningEvent{ WarningType::INVALID_STATE, msg, 0.0, {}, uav_id, task.getId() });
					LOG_ERROR("[SurveyCylinderTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					overall_success = false;
					LOG_INFO("[SurveyCylinderTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (缺少动力学模型)。", task.getId(), uav_id);
					continue;
				}
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}], 无人机 [{}]: 起始状态 Pos=({}), Time={:.3f}",
					task.getId(), uav_id, start_state.position.toString(), start_state.time_stamp);


				// --- 6.1 规划进入扫描起点的路径 ---
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 3: 规划进入扫描起点的路径...", task.getId(), uav_id);
				EcefPoint scan_start_point = cylinder_scan_path.front(); // 扫描几何路径的第一个点
				std::vector<EcefPoint> entry_geom_path;
				auto path_constraints_opt = task.getStrategyByType<NSMission::PathConstraintStrategy>();
				const NSMission::PathConstraintStrategy* path_constraints_ptr = path_constraints_opt ? path_constraints_opt.get() : nullptr;
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}], 无人机 [{}]: 调用路径规划器: 从 {} 到 {}",
					task.getId(), uav_id, start_state.position.toString(), scan_start_point.toString());
				// 转换为ECEF坐标进行路径规划
				EcefPoint start_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(start_state.position);
				bool entry_found = path_planner_->findPath(start_ecef, scan_start_point, dynamics, path_constraints_ptr, entry_geom_path);
				if (!entry_found || entry_geom_path.size() < 2) {
					std::ostringstream oss_msg;
					oss_msg << "未能找到无人机 [" << uav_id << "] 进入圆柱扫描起点 ("
						<< std::fixed << std::setprecision(1) << scan_start_point.x() << ","
						<< std::fixed << std::setprecision(1) << scan_start_point.y() << ","
						<< std::fixed << std::setprecision(1) << scan_start_point.z() << ") 的路径。";
					std::string msg_fmt = oss_msg.str();
					result.addWarning(WarningEvent{ WarningType::PLANNING_FAILURE, msg_fmt, start_state.time_stamp, start_state.position, uav_id, task.getId() });
					LOG_ERROR("[SurveyCylinderTaskPlanner] 任务 [{}]: {}", task.getId(), msg_fmt);
					overall_success = false;
					LOG_INFO("[SurveyCylinderTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (无法规划进入路径)。", task.getId(), uav_id);
					continue;
				}
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}], 无人机 [{}]: 进入路径找到 {} 个点。", task.getId(), uav_id, entry_geom_path.size());

				// --- 6.2 合并进入路径和扫描路径 ---
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 4: 合并进入路径和扫描路径...", task.getId(), uav_id);
				std::vector<WGS84Point> full_geom_path = entry_geom_path;
				// 检查并移除可能的重合点
				if (!full_geom_path.empty() && (full_geom_path.back() - cylinder_scan_path.front()).norm() < Constants::GEOMETRY_EPSILON) {
					LOG_TRACE("[SurveyCylinderTaskPlanner] 任务 [{}], 无人机 [{}]: 合并几何路径：移除入口路径终点与扫描路径起点的重合点。", task.getId(), uav_id);
					full_geom_path.pop_back();
				}
				full_geom_path.insert(full_geom_path.end(), cylinder_scan_path.begin(), cylinder_scan_path.end());
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}], 无人机 [{}]: 合并后完整几何路径 {} 个点。", task.getId(), uav_id, full_geom_path.size());

				// --- 6.3 平滑和时间参数化 ---
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 5: 平滑和时间参数化完整路径...", task.getId(), uav_id);
				RouteSegment final_segment;
				double desired_speed = task.getDesiredSpeed(8.0); // 获取任务期望速度
				if (full_geom_path.size() < 2) {
					std::string msg = "合并后的几何路径点数不足 (UAV: " + uav_id + ")，无法进行平滑和参数化。";
					LOG_ERROR("[SurveyCylinderTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					result.addWarning(WarningEvent{ WarningType::PLANNING_FAILURE, msg, start_state.time_stamp, start_state.position, uav_id, task.getId() });
					overall_success = false;
					LOG_INFO("[SurveyCylinderTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (几何路径点数不足)。", task.getId(), uav_id);
					continue;
				}
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}], 无人机 [{}]: 开始平滑和时间参数化, 速度 {:.1f} m/s, {} 个几何点...",
					task.getId(), uav_id, desired_speed, full_geom_path.size());
				if (!smoothAndTimeParameterize(full_geom_path, uav, start_state, desired_speed, final_segment, &result, task.getStrategies())) {
					LOG_ERROR("[SurveyCylinderTaskPlanner] 任务 [{}], 无人机 [{}] 圆柱勘察路径处理失败 (平滑/参数化过程出错)。", task.getId(), uav_id);
					overall_success = false;
					LOG_INFO("[SurveyCylinderTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (平滑/参数化过程出错)。", task.getId(), uav_id);
					continue;
				}
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}], 无人机 [{}]: 平滑和时间参数化完成，生成 {} 个航点。", task.getId(), uav_id, final_segment.size());

				// --- 6.4 添加结果 ---
				LOG_DEBUG("[SurveyCylinderTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 6: 添加最终航线到结果...", task.getId(), uav_id);
				if (final_segment.empty()) {
					std::string msg = "无人机 [" + uav_id + "] 规划流程完成，但最终生成的航段为空。";
					LOG_WARN("[SurveyCylinderTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					result.addWarning(WarningEvent{ WarningType::PLANNING_FAILURE, msg, 0.0, {}, uav_id, task.getId() });
				}
				else {
					// 调用 checkSegmentWarnings 时，需要传入 PlannedRoute
					PlannedRoute temp_route_for_check(uav_id);
					temp_route_for_check.addWaypoints(final_segment);
					checkSegmentWarnings(temp_route_for_check, uav_id, result, task.getId()); // 传递临时 Route
					PlannedRoute route(uav_id);
					route.addWaypoints(final_segment);
					result.addRoute(std::move(route));
					LOG_INFO("[SurveyCylinderTaskPlanner] 任务 [{}]: 为无人机 [{}] 生成圆柱勘察航线成功，{} 个点。", task.getId(), uav_id, final_segment.size());
				}
			}

			// --- 7. 设置最终状态 ---
			if (!overall_success) {
				if (result.wasSuccessful()) { // 使用 wasSuccessful() 检查
					result.setStatus(false, "部分或全部无人机的圆柱勘察任务规划失败。");
				}
				LOG_ERROR("[SurveyCylinderTaskPlanner] 任务 [{}] 规划存在失败情况。", task.getId());
			}
			else {
				LOG_INFO("[SurveyCylinderTaskPlanner] 任务 [{}] 所有分配的无人机规划流程完成。", task.getId());
				if (result.getAllRoutes().empty() && !assigned_uavs.empty()) {
					LOG_WARN("[SurveyCylinderTaskPlanner] 任务 [{}]: 规划流程成功完成，但最终未生成任何有效航线。", task.getId());
				}
				else if (result.getAllRoutes().size() != assigned_uavs.size()) {
					LOG_WARN("[SurveyCylinderTaskPlanner] 任务 [{}]: 最终生成的航线数量 ({}) 与成功完成规划的无人机数量 ({}) 不完全匹配。", task.getId(), result.getAllRoutes().size(), assigned_uavs.size());
				}
				else {
					LOG_INFO("[SurveyCylinderTaskPlanner] 任务 [{}] 规划成功完成，为所有 {} 架无人机生成了航线。", task.getId(), result.getAllRoutes().size());
				}
			}
			LOG_INFO("[SurveyCylinderTaskPlanner] 任务 [{}] 规划结束，最终状态: {}", task.getId(), result.wasSuccessful() ? "成功" : "失败"); // 使用 wasSuccessful() 判断最终状态
			return result;
		}

	} // namespace NSPlanning
} // namespace NSDrones