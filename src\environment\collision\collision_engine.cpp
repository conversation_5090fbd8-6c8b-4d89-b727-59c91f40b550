#include "environment/collision/collision_engine.h"
#include "environment/collision/collision_types.h"
#include "core/geometry/ishape.h"
#include "core/geometry/shapes/sphere_shape.h"
#include "utils/object_id.h"
#include "utils/logging.h"
#include "core/types.h"
#include <fcl/fcl.h>
#include <algorithm> 

namespace NSDrones {
	namespace NSEnvironment {

		// 使用ISpatialIndex命名空间中的类型
		using SpatialQueryFilter = SpatialQueryFilter;
		using SpatialObjectData = SpatialObjectData;

		// 实现 initialize 方法
		template <typename ObjectContainer>
		bool CollisionEngine<ObjectContainer>::initialize(std::shared_ptr<NSParams::ParamValues> global_params) {
			LOG_INFO("CollisionEngine::initialize - 开始初始化碰撞引擎");

			if (!global_params) {
				LOG_ERROR("CollisionEngine::initialize - 全局参数为空");
				return false;
			}

			try {
				// 从全局参数读取碰撞检测配置
				auto collision_enabled = global_params->getValueOrDefault<bool>("collision_detection.enabled", true);
				auto collision_margin = global_params->getValueOrDefault<double>("collision_detection.margin", 0.1);
				auto continuous_detection = global_params->getValueOrDefault<bool>("collision_detection.continuous", false);
				auto max_checks = global_params->getValueOrDefault<int>("collision_detection.max_checks_per_frame", 1000);

				LOG_DEBUG("CollisionEngine: 配置参数 - 启用: {}, 安全边距: {:.3f}, 连续检测: {}, 最大检查数: {}",
						 collision_enabled, collision_margin, continuous_detection, max_checks);

				if (!collision_enabled) {
					LOG_INFO("CollisionEngine: 碰撞检测已在配置中禁用");
					return true; // 仍然返回成功，但功能被禁用
				}

				// 这里可以根据参数配置碰撞引擎的行为
				// 目前 CollisionEngine 主要在构造时完成初始化，这里主要是参数验证和日志记录

				LOG_INFO("CollisionEngine: 初始化成功");
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("CollisionEngine::initialize - 初始化失败: {}", e.what());
				return false;
			}
		}

		template <typename ObjectContainer>
		std::vector<CollisionResult> CollisionEngine<ObjectContainer>::checkGeometryAgainstEnvironment(
			const IShape& temp_shape,
			const EcefPoint& ecef_position,
			const Orientation& orientation,
			const CollisionOptions& options) {

			LOG_DEBUG("碰撞引擎: 开始检测临时几何体 {} 在ECEF位置 {} 与环境的碰撞",
				temp_shape.toString(), ecef_position.toString());

			std::vector<CollisionResult> results;

			// 1. 为临时几何体创建 FCL 对象（直接使用ECEF坐标）
			auto temp_fcl_object = createFCLObjectFromECEF(&temp_shape, ecef_position, orientation);
			if (!temp_fcl_object) {
				LOG_ERROR("碰撞引擎::checkGeometryAgainstEnvironment: 无法为临时形状 {} 创建FCL对象", temp_shape.toString());
				return results;
			}
			ShapeType temp_shape_type = temp_shape.getType();

			// 2. 使用空间索引进行粗筛（基于WGS84坐标系）
			fcl::AABBd shape_aabb = temp_shape.getAABB();

			// 基于WGS84位置和形状边界框创建查询边界框
			WGS84BoundingBox wgs84_query_bounds;

			// 将ECEF坐标转换为WGS84坐标用于空间索引查询
			WGS84Point wgs84_position = NSUtils::CoordinateConverter::ecefToWGS84(ecef_position);

			// 将形状的AABB转换为WGS84边界框（简化处理：假设形状相对较小）
			double margin_deg = (options.safetyMargin + std::max({shape_aabb.width(), shape_aabb.height(), shape_aabb.depth()})) / 111000.0; // 粗略转换米到度

			wgs84_query_bounds.minLongitude = wgs84_position.longitude - margin_deg;
			wgs84_query_bounds.maxLongitude = wgs84_position.longitude + margin_deg;
			wgs84_query_bounds.minLatitude = wgs84_position.latitude - margin_deg;
			wgs84_query_bounds.maxLatitude = wgs84_position.latitude + margin_deg;
			wgs84_query_bounds.minAltitude = wgs84_position.altitude - (options.safetyMargin + shape_aabb.depth() / 2.0);
			wgs84_query_bounds.maxAltitude = wgs84_position.altitude + (options.safetyMargin + shape_aabb.depth() / 2.0);

			LOG_DEBUG("碰撞引擎::checkGeometryAgainstEnvironment: 临时几何体使用WGS84查询边界 [{:.6f},{:.6f},{:.1f}] - [{:.6f},{:.6f},{:.1f}] 进行空间索引查询",
				wgs84_query_bounds.minLongitude, wgs84_query_bounds.minLatitude, wgs84_query_bounds.minAltitude,
				wgs84_query_bounds.maxLongitude, wgs84_query_bounds.maxLatitude, wgs84_query_bounds.maxAltitude);

			// 使用注入的空间索引查找潜在碰撞对象
			SpatialQueryFilter filter;

			// 设置自定义过滤函数来处理动态对象和忽略列表
			filter.customFilter = [&options](const ObjectID& id, const SpatialObjectData& data) -> bool {
				// 检查是否在忽略列表中
				if (options.ignoredObjectIds.count(id) > 0) {
					return false;
				}

				// 如果不检查动态对象，则过滤掉动态对象
				// 注意：SpatialObjectData没有type字段，这里需要通过其他方式判断
				// 暂时跳过这个检查，在后续的对象获取阶段进行过滤
				// if (!options.checkDynamicObjects && data.type == "movable") {
				//     return false;
				// }

				return true;
			};

			std::vector<ObjectID> potential_colliders = spatial_index_.findObjectsInRegion(wgs84_query_bounds, filter);

			LOG_DEBUG("碰撞引擎::checkGeometryAgainstEnvironment: 临时几何体通过空间索引找到 {} 个潜在碰撞对象",
				potential_colliders.size());

			// 3. 对每个潜在碰撞对象进行精确检测
			for (ObjectID other_id : potential_colliders) {
				// 跳过被忽略的对象 (如果临时对象有ID且被忽略，也应处理，但这里临时对象无ID)
				if (options.ignoredObjectIds.count(other_id)) {
					LOG_TRACE("碰撞引擎::checkGeometryAgainstEnvironment: 跳过被忽略的对象 ID {}", other_id);
					continue;
				}

				// 通过Environment单例获取对象
				auto environment = Environment::getInstance();
				auto other_object = environment->getMutableObjectById(other_id);
				if (!other_object) {
					LOG_WARN("碰撞引擎::checkGeometryAgainstEnvironment: 空间索引返回了对象 ID {}，但在环境中未找到或为空。", other_id);
					continue;
				}

				// 根据选项过滤动态/静态对象
				bool is_other_dynamic = object_storage_.isObjectDynamic(other_id); // Assuming ObjectStorage provides this
				if ((is_other_dynamic && !options.checkDynamicObjects) ||
					(!is_other_dynamic && !options.checkStaticObjects)) {
					LOG_TRACE("碰撞引擎::checkGeometryAgainstEnvironment: 根据动态/静态选项跳过对象 ID {}", other_id);
					continue;
				}

				const auto other_shape_ptr = other_object->getShape().get();
				if (!other_shape_ptr) {
					LOG_WARN("碰撞引擎::checkGeometryAgainstEnvironment: 对象 ID {} 没有形状。", other_id);
					continue;
				}
				// const IShape& other_shape = *other_shape_ptr; // 获取引用 (不再需要，直接用 ptr)
				ShapeType other_shape_type = other_shape_ptr->getType();

				// 创建其他对象的 FCL 碰撞对象（使用WGS84坐标）
				WGS84Point other_wgs84_pos = other_object->getWGS84Position();
				auto other_fcl_object = createFCLObjectFromWGS84(other_shape_ptr, other_wgs84_pos, other_object->getOrientation());
				if (!other_fcl_object) {
					LOG_WARN("碰撞引擎::checkGeometryAgainstEnvironment: 无法为对象 ID {} (类型 {}) 创建FCL对象。", other_id, other_shape_ptr->toString());
					continue;
				}

				// 执行碰撞检测
				CollisionResult result = detectorRegistry_.detectCollision(
					temp_fcl_object.get(), other_fcl_object.get(),
					temp_shape_type, other_shape_type,
					options
				);

				// 设置对象ID
				result.object1Id = NSUtils::INVALID_OBJECT_ID; //或 TEMPORARY_OBJECT_ID; // 代表临时几何体
				result.object2Id = other_id;

				if (result.hasCollision) {
					LOG_DEBUG("碰撞引擎::checkGeometryAgainstEnvironment: 临时几何体与对象 ID {} 碰撞，穿透深度 {:.3f}",
						other_id, result.penetrationDepth);
					results.push_back(std::move(result));
				}
				else if (options.enableDistance && result.distance < std::numeric_limits<double>::infinity()) {
					LOG_TRACE("碰撞引擎::checkGeometryAgainstEnvironment: 临时几何体与对象 ID {} 之间距离 {:.3f}",
						other_id, result.distance);
					// 检查是否在安全边距内
					if (result.distance <= options.safetyMargin + Constants::GEOMETRY_EPSILON) {
						LOG_DEBUG("碰撞引擎::checkGeometryAgainstEnvironment: 临时几何体与对象 ID {} 距离 {:.3f} 在安全边距 {:.2f} 内。",
							other_id, result.distance, options.safetyMargin);
						results.push_back(std::move(result));
					}
				}
			}

			LOG_DEBUG("碰撞引擎::checkGeometryAgainstEnvironment: 检测完成，发现 {} 个相关碰撞/近距离事件。", results.size());
			return results;
		}

		template <typename ObjectContainer>
		std::vector<CollisionResult> CollisionEngine<ObjectContainer>::checkObjectCollisions(
			ObjectID objectId, const CollisionOptions& options) {
			std::vector<CollisionResult> results;

			try {
				// 1. 获取要检查的对象
				auto object = object_storage_.getObject(objectId);
				if (!object) {
					LOG_WARN("CollisionEngine::checkObjectCollisions: 未找到对象 ID {}", objectId);
					return results;
				}

				// 2. 获取对象的形状
				const auto shape = object->getShape();
				if (!shape) {
					LOG_DEBUG("CollisionEngine::checkObjectCollisions: 对象 ID {} 没有形状", objectId);
					return results;
				}

				// 3. 创建对象的 FCL 碰撞对象（使用WGS84坐标）
				WGS84Point object_wgs84_pos = object->getWGS84Position();
				auto fcl_object = createFCLObjectFromWGS84(shape.get(), object_wgs84_pos, object->getOrientation());
				if (!fcl_object) {
					LOG_WARN("CollisionEngine::checkObjectCollisions: 无法为对象 ID {} 创建FCL对象", objectId);
					return results;
				}

				// 4. 创建查询边界框
				fcl::AABBd shape_bbox = shape->getAABB();

				// 基于WGS84位置和形状边界框创建查询边界框
				WGS84BoundingBox wgs84_bbox;
				double margin_deg = (options.safetyMargin + std::max({shape_bbox.width(), shape_bbox.height(), shape_bbox.depth()})) / 111000.0; // 粗略转换米到度

				wgs84_bbox.minLongitude = object_wgs84_pos.longitude - margin_deg;
				wgs84_bbox.maxLongitude = object_wgs84_pos.longitude + margin_deg;
				wgs84_bbox.minLatitude = object_wgs84_pos.latitude - margin_deg;
				wgs84_bbox.maxLatitude = object_wgs84_pos.latitude + margin_deg;
				wgs84_bbox.minAltitude = object_wgs84_pos.altitude - (options.safetyMargin + shape_bbox.depth() / 2.0);
				wgs84_bbox.maxAltitude = object_wgs84_pos.altitude + (options.safetyMargin + shape_bbox.depth() / 2.0);

				// 5. 查询潜在碰撞对象（使用WGS84边界框）
				auto potential_colliders = spatial_index_.findObjectsInRegion(wgs84_bbox, std::nullopt);
				LOG_DEBUG("CollisionEngine::checkObjectCollisions: 对象 ID {} 找到 {} 个潜在碰撞对象",
					objectId, potential_colliders.size());

				// 6. 对每个潜在碰撞对象进行检测
				for (const auto& other_id : potential_colliders) {
					// 跳过自己
					if (other_id == objectId) continue;

					// 跳过被忽略的对象
					if (options.ignoredObjectIds.count(other_id) > 0) {
						LOG_TRACE("CollisionEngine::checkObjectCollisions: 跳过被忽略的对象 ID {}", other_id);
						continue;
					}

					// 获取潜在碰撞对象
					auto other_object = object_storage_.getObject(other_id);
					if (!other_object) {
						LOG_WARN("CollisionEngine::checkObjectCollisions: 未找到潜在碰撞对象 ID {}", other_id);
						continue;
					}

					// 检查动态/静态对象过滤
					bool is_other_dynamic = object_storage_.isObjectDynamic(other_id);
					if ((is_other_dynamic && !options.checkDynamicObjects) ||
						(!is_other_dynamic && !options.checkStaticObjects)) {
						continue;
					}

					// 获取潜在碰撞对象的形状
					const auto other_shape = other_object->getShape();
					if (!other_shape) {
						LOG_DEBUG("CollisionEngine::checkObjectCollisions: 对象 ID {} 没有形状", other_id);
						continue;
					}

					// 创建潜在碰撞对象的 FCL 碰撞对象（使用WGS84坐标）
					WGS84Point other_wgs84_pos = other_object->getWGS84Position();
					auto other_fcl_object = createFCLObjectFromWGS84(
						other_shape.get(),
						other_wgs84_pos,
						other_object->getOrientation()
					);

					if (!other_fcl_object) {
						LOG_WARN("CollisionEngine::checkObjectCollisions: 无法为对象 ID {} 创建FCL对象", other_id);
						continue;
					}

					// 执行碰撞检测
					CollisionResult result = detectorRegistry_.detectCollision(
						fcl_object.get(), other_fcl_object.get(),
						shape->getType(), other_shape->getType(),
						options
					);

					// 设置对象ID
					result.object1Id = objectId;
					result.object2Id = other_id;

					if (result.hasCollision ||
						(options.enableDistance && result.distance <= options.safetyMargin + Constants::GEOMETRY_EPSILON)) {
						results.push_back(std::move(result));
					}
				}

			} catch (const std::exception& e) {
				LOG_ERROR("CollisionEngine::checkObjectCollisions 发生异常: {}", e.what());
			}

			return results;
		}

		template <typename ObjectContainer>
		std::vector<CollisionResult> CollisionEngine<ObjectContainer>::checkAllCollisions(const CollisionOptions& options) {
			std::vector<CollisionResult> results;

			try {
				// 1. 获取所有对象ID
				auto all_objects = object_storage_.getAllObjects();
				LOG_DEBUG("CollisionEngine::checkAllCollisions: 检查 {} 个对象之间的碰撞", all_objects.size());

				// 2. 对每对对象进行碰撞检测
				for (size_t i = 0; i < all_objects.size(); ++i) {
					for (size_t j = i + 1; j < all_objects.size(); ++j) {
						const auto& obj1 = all_objects[i];
						const auto& obj2 = all_objects[j];

						if (!obj1 || !obj2) continue;

						// 跳过被忽略的对象
						if (options.ignoredObjectIds.count(obj1->getId()) > 0 ||
							options.ignoredObjectIds.count(obj2->getId()) > 0) {
							continue;
						}

						// 检查动态/静态对象过滤
						bool is_obj1_dynamic = object_storage_.isObjectDynamic(obj1->getId());
						bool is_obj2_dynamic = object_storage_.isObjectDynamic(obj2->getId());

						if ((is_obj1_dynamic && !options.checkDynamicObjects) ||
							(!is_obj1_dynamic && !options.checkStaticObjects) ||
							(is_obj2_dynamic && !options.checkDynamicObjects) ||
							(!is_obj2_dynamic && !options.checkStaticObjects)) {
							continue;
						}

						// 获取形状
						const auto shape1 = obj1->getShape();
						const auto shape2 = obj2->getShape();
						if (!shape1 || !shape2) continue;

						// 创建 FCL 碰撞对象（使用WGS84坐标）
						WGS84Point obj1_wgs84_pos = obj1->getWGS84Position();
						WGS84Point obj2_wgs84_pos = obj2->getWGS84Position();
						auto fcl_obj1 = createFCLObjectFromWGS84(shape1.get(), obj1_wgs84_pos, obj1->getOrientation());
						auto fcl_obj2 = createFCLObjectFromWGS84(shape2.get(), obj2_wgs84_pos, obj2->getOrientation());

						if (!fcl_obj1 || !fcl_obj2) continue;

						// 执行碰撞检测
						CollisionResult result = detectorRegistry_.detectCollision(
							fcl_obj1.get(), fcl_obj2.get(),
							shape1->getType(), shape2->getType(),
							options
						);

						// 设置对象ID
						result.object1Id = obj1->getId();
						result.object2Id = obj2->getId();

						if (result.hasCollision ||
							(options.enableDistance && result.distance <= options.safetyMargin + Constants::GEOMETRY_EPSILON)) {
							results.push_back(std::move(result));
						}
					}
				}

			} catch (const std::exception& e) {
				LOG_ERROR("CollisionEngine::checkAllCollisions 发生异常: {}", e.what());
			}

			return results;
		}

		template <typename ObjectContainer>
		std::shared_ptr<EntityObject> CollisionEngine<ObjectContainer>::getObject(ObjectID id) const {
			try {
				return object_storage_.getObject(id);
			} catch (const std::exception& e) {
				LOG_ERROR("CollisionEngine::getObject 获取对象 ID {} 时发生异常: {}", id, e.what());
				return nullptr;
			}
		}

		template <typename ObjectContainer>
		CollisionResult CollisionEngine<ObjectContainer>::checkObjectCollision(
			const EntityObject& obj1,
			const EntityObject& obj2,
			const CollisionOptions& options) {

			try {
				// 获取对象形状
				const auto shape1 = obj1.getShape();
				const auto shape2 = obj2.getShape();

				if (!shape1 || !shape2) {
					LOG_DEBUG("CollisionEngine::checkObjectCollision: 对象缺少形状");
					return {};
				}

				// 创建 FCL 碰撞对象（使用WGS84坐标）
				WGS84Point obj1_wgs84_pos = obj1.getWGS84Position();
				WGS84Point obj2_wgs84_pos = obj2.getWGS84Position();
				auto fcl_obj1 = createFCLObjectFromWGS84(shape1.get(), obj1_wgs84_pos, obj1.getOrientation());
				auto fcl_obj2 = createFCLObjectFromWGS84(shape2.get(), obj2_wgs84_pos, obj2.getOrientation());

				if (!fcl_obj1 || !fcl_obj2) {
					LOG_WARN("CollisionEngine::checkObjectCollision: 无法创建FCL对象");
					return {};
				}

				// 执行碰撞检测
				CollisionResult result = detectorRegistry_.detectCollision(
					fcl_obj1.get(), fcl_obj2.get(),
					shape1->getType(), shape2->getType(),
					options
				);

				// 设置对象ID
				result.object1Id = obj1.getId();
				result.object2Id = obj2.getId();

				return result;

			} catch (const std::exception& e) {
				LOG_ERROR("CollisionEngine::checkObjectCollision 发生异常: {}", e.what());
				return {};
			}
		}

		template <typename ObjectContainer>
		std::vector<CollisionResult> CollisionEngine<ObjectContainer>::checkObjectAgainstEnvironment(
			std::shared_ptr<EntityObject> obj,
			const CollisionOptions& options) {

			if (!obj) {
				LOG_WARN("CollisionEngine::checkObjectAgainstEnvironment: 对象为空");
				return {};
			}

			return checkObjectCollisions(obj->getId(), options);
		}

		template <typename ObjectContainer>
		std::vector<CollisionResult> CollisionEngine<ObjectContainer>::checkObjectAgainstObject(
			std::shared_ptr<EntityObject> obj1,
			std::shared_ptr<EntityObject> obj2,
			const CollisionOptions& options) {

			if (!obj1 || !obj2) {
				LOG_WARN("CollisionEngine::checkObjectAgainstObject: 对象为空");
				return {};
			}

			auto result = checkObjectCollision(*obj1, *obj2, options);
			if (result.hasCollision) {
				return { result };
			}
			return {};
		}

		template <typename ObjectContainer>
		std::vector<CollisionResult> CollisionEngine<ObjectContainer>::checkPointAgainstEnvironment(
			const EcefPoint& ecef_point,
			double safety_radius,
			const CollisionOptions& options) {

			LOG_DEBUG("碰撞引擎: 开始检测ECEF点 {} 与环境的碰撞，安全半径: {:.2f}",
				ecef_point.toString(), safety_radius);

			// 创建球形形状
			SphereShape sphere_shape(safety_radius);

			// 使用几何体检测方法
			return checkGeometryAgainstEnvironment(sphere_shape, ecef_point, Orientation::Identity(), options);
		}

		template <typename ObjectContainer>
		std::vector<CollisionResult> CollisionEngine<ObjectContainer>::checkSegmentAgainstEnvironment(
			const EcefPoint& ecef_start,
			const EcefPoint& ecef_end,
			double safety_radius,
			const CollisionOptions& options) {

			LOG_DEBUG("碰撞引擎: 开始检测ECEF线段 {} - {} 与环境的碰撞，安全半径: {:.2f}",
				ecef_start.toString(), ecef_end.toString(), safety_radius);

			std::vector<CollisionResult> all_results;

			// 将线段离散化为多个点进行检测
			const int num_samples = 10;
			for (int i = 0; i <= num_samples; ++i) {
				double t = static_cast<double>(i) / static_cast<double>(num_samples);

				// 在ECEF坐标系中进行线性插值
				double x = ecef_start.x() * (1.0 - t) + ecef_end.x() * t;
				double y = ecef_start.y() * (1.0 - t) + ecef_end.y() * t;
				double z = ecef_start.z() * (1.0 - t) + ecef_end.z() * t;
				EcefPoint sample_ecef(x, y, z);

				// 检测该点的碰撞（使用ECEF坐标进行内部计算）
				auto point_results = checkPointAgainstEnvironment(sample_ecef, safety_radius, options);

				// 合并结果
				all_results.insert(all_results.end(), point_results.begin(), point_results.end());
			}

			LOG_DEBUG("碰撞引擎: ECEF线段碰撞检测完成，找到 {} 个碰撞", all_results.size());
			return all_results;
		}

		// 显式实例化模板
		template class CollisionEngine<std::unordered_map<ObjectID, std::shared_ptr<EntityObject>>>;

	} // namespace NSEnvironment
} // namespace NSDrones