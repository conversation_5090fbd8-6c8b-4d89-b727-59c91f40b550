// include/mission/task_targets.h
#pragma once

#include "core/types.h"
#include "core/geometry/ishape.h"  // 新形状系统
#include <vector>
#include <string>
#include <variant>
#include <memory> 
#include <sstream> 
#include <iomanip> 
#include "utils/object_id.h"

namespace NSDrones {
	namespace NSMission {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		// --- 形状类型别名 ---
		using IShapePtr = std::shared_ptr<IShape>;
		using ConstIShapePtr = std::shared_ptr<const IShape>;

		// --- 任务目标基类 ---
		/**
		 * @class ITaskTarget
		 * @brief 任务目标对象的抽象基类。
		 */
		class ITaskTarget {
		public:
			enum class TargetType {
				POINT, LINE, AREA, VOLUME, OBJECT, UNKNOWN // 添加 UNKNOWN
			};
			virtual ~ITaskTarget() = default;
			/** @brief 获取目标类别 */
			virtual TargetType getType() const = 0;
			/** @brief 获取目标的描述 */
			virtual std::string getDescription() const = 0;
		};
		using ITaskTargetPtr = std::shared_ptr<ITaskTarget>;
		using ConstITaskTargetPtr = std::shared_ptr<const ITaskTarget>;


		// --- 具体目标类型 ---

		/** @brief 点目标 */
		class PointTarget : public ITaskTarget {
		public:
			PointTarget(WGS84Point position) : position_(position) {}
			TargetType getType() const override { return TargetType::POINT; }
			std::string getDescription() const override {
				std::stringstream ss;
				ss << "点目标 (" << position_.toString() << ")";
				return ss.str();
			}
			const WGS84Point& getPosition() const { return position_; }
		private:
			WGS84Point position_;
		};

		/** @brief 线目标 */
		class LineTarget : public ITaskTarget {
		public:
			LineTarget(std::vector<WGS84Point> points) : points_(std::move(points)) {}
			TargetType getType() const override { return TargetType::LINE; }
			std::string getDescription() const override {
				return "线目标 (" + std::to_string(points_.size()) + " 点)";
			}
			const std::vector<WGS84Point>& getPoints() const { return points_; }
		private:
			std::vector<WGS84Point> points_;
		};

		/** @brief 区域目标 */
		class AreaTarget : public ITaskTarget {
		public:
			AreaTarget(std::vector<WGS84Point> boundary) : boundary_(std::move(boundary)) {}
			TargetType getType() const override { return TargetType::AREA; }
			std::string getDescription() const override {
				return "区域目标 (" + std::to_string(boundary_.size()) + " 顶点)";
			}
			const std::vector<WGS84Point>& getBoundary() const { return boundary_; }
		private:
			std::vector<WGS84Point> boundary_;
		};

		/** @brief 体积目标  */
		class VolumeTarget : public ITaskTarget {
		public:
			// 构造函数需要形状和变换信息
			VolumeTarget(IShapePtr shape, WGS84Point center, Orientation orientation)
				: shape_(shape), center_(center), orientation_(orientation) {}
			TargetType getType() const override { return TargetType::VOLUME; }
			std::string getDescription() const override {
				return "体积目标 (形状: " + (shape_ ? shape_->toString() : "空") + ")";
			}
			std::shared_ptr<const IShape> getShape() const { return shape_; }
			const WGS84Point& getCenter() const { return center_; }
			const Orientation& getOrientation() const { return orientation_; }
		private:
			IShapePtr shape_;
			WGS84Point center_;
			Orientation orientation_;
		};

		/** @brief 特定对象目标 */
		class ObjectTarget : public ITaskTarget {
		public:
			ObjectTarget(ObjectID target_id) : target_id_(target_id) {}
			TargetType getType() const override { return TargetType::OBJECT; }
			std::string getDescription() const override { 
				return "特定目标 (ID: " + target_id_ + ")"; 
			}
			const ObjectID& getTargetId() const { return target_id_; }
		private:
			ObjectID target_id_;
		};

		// --- 任务目标 Variant ---
		// 使用 std::variant 存储不同类型的目标对象指针
		using TaskTargetVariant = std::variant<
			std::shared_ptr<PointTarget>,
			std::shared_ptr<LineTarget>,
			std::shared_ptr<AreaTarget>,
			std::shared_ptr<VolumeTarget>,
			std::shared_ptr<ObjectTarget>
		>;


	} // namespace NSMission
} // namespace NSDrones