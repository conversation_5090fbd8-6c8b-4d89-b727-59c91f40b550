// include/mission/control_point.h
#pragma once

#include "core/types.h" 
#include "params/parameters.h"
#include <vector>
#include <memory>   
#include <string>
#include <optional> 
#include <map>      
#include <algorithm> 

namespace NSDrones {
	namespace NSMission {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSParams;

		/**
		 * @struct PayloadActionCommand
		 * @brief 定义一个简单的载荷动作命令。
		 */
		struct PayloadActionCommand {
			std::string payload_id = ""; // 目标载荷 ID (可选，如果 UAV 只有一个特定载荷)
			std::string command_name = ""; // 命令名称 (例如 "TakePhoto", "SetZoom", "Drop")
			std::map<std::string, ParamValue> parameters; // 命令参数 (键值对)

			PayloadActionCommand() = default;
			PayloadActionCommand(std::string cmd, std::map<std::string, ParamValue> params = {}, std::string p_id = "")
				: payload_id(std::move(p_id)), command_name(std::move(cmd)), parameters(std::move(params)) {}

			// 添加比较操作符，以便在 vector 中比较
			bool operator==(const PayloadActionCommand& other) const {
				return payload_id == other.payload_id &&
					command_name == other.command_name &&
					parameters == other.parameters; // map 默认有 operator==
			}
			bool operator!=(const PayloadActionCommand& other) const {
				return !(*this == other);
			}
		};


		/**
		 * @class PayloadAction
		 * @brief 载荷动作的基类 (抽象类或接口)。
		 *        具体的动作（如拍照、投放、扫描）需要从此类派生。
		 *        **注意:** 由于无法从 JSON 反序列化具体派生类，
		 *                 在任务定义中通常只保存动作描述或类型。
		 *        **将来可以考虑:** 定义更复杂的动作接口或使用命令模式。
		 */
		class PayloadAction {
		public:
			virtual ~PayloadAction() = default; // 虚析构函数
			/** @brief 获取动作的描述性字符串。 */
			virtual std::string getDescription() const = 0; // 纯虚函数，子类必须实现
			/** @brief (可选) 获取此动作对应的简单命令。 */
			virtual std::optional<PayloadActionCommand> getCommand() const { return std::nullopt; }
		};
		// 定义载荷动作的智能指针类型别名
		using PayloadActionPtr = std::shared_ptr<PayloadAction>;

		/**
		 * @struct ControlPoint
		 * @brief 定义一个任务控制点（例如航路点、盘旋中心、区域顶点）及其相关约束。
		 *
		 * 坐标系说明：
		 * - position 使用 WGS84 坐标系（纬度/经度/高度）
		 * - 这是外部接口标准，内部计算时会转换为 NED 局部坐标
		 */
		struct ControlPoint {
			WGS84Point position;                            // 控制点目标位置 (WGS84坐标系: 纬度/经度/高度)
			ControlPointType type = ControlPointType::WAYPOINT_MUST_PASS; // 控制点类型 (默认为必须通过的航路点)

			// --- 高度要求 ---
			double required_altitude = 100.0;              // 要求的高度值 (米)
			AltitudeType height_type = AltitudeType::ABOVE_GROUND_LEVEL; // 高度类型 (默认为相对地面)

			// --- 速度要求 (可选) ---
			// 如果设置，表示到达此点时或经过此点时的期望速度向量
			std::optional<Vector3D> required_speed = std::nullopt; // 速度向量 (m/s, 世界坐标系, ENU)

			// --- 航向/姿态要求 (可选) ---
			// 通常用于拍照、传感器指向等
			std::optional<double> required_heading_deg = std::nullopt; // 要求航向角 (度, 0-360, 相对于正北)
			std::optional<Orientation> required_attitude = std::nullopt;  // 要求姿态 (四元数)

			// --- 停留时间 (可选) ---
			Time required_loiter_time = 0.0;               // 在此点停留时间 (秒)，默认为 0

			// --- 载荷动作 (可选) ---
			// **注意:** 此指针通常在运行时设置，难以从静态配置（如 JSON）完全恢复
			PayloadActionPtr action = nullptr;              // 在此点执行的载荷动作 (默认为空)

			// --- 构造函数 ---
			ControlPoint() = default; // 默认构造函数

			/**
			 * @brief 构造函数 (常用参数)。
			 * @param pos 控制点WGS84坐标位置。
			 * @param cp_type 控制点类型。
			 * @param alt 要求的高度值。
			 * @param ht 高度类型。
			 */
			ControlPoint(const WGS84Point& pos,
				ControlPointType cp_type = ControlPointType::WAYPOINT_MUST_PASS,
				double alt = 100.0,
				AltitudeType ht = AltitudeType::ABOVE_GROUND_LEVEL)
				: position(pos), type(cp_type), required_altitude(alt), height_type(ht) {}

			/**
			 * @brief 便利构造函数 (从经纬度高度构造)。
			 * @param lat 纬度（度）
			 * @param lon 经度（度）
			 * @param alt_msl 海拔高度（米，MSL）
			 * @param cp_type 控制点类型。
			 */
			ControlPoint(double lat, double lon, double alt_msl,
				ControlPointType cp_type = ControlPointType::WAYPOINT_MUST_PASS)
				: position(lon, lat, alt_msl), type(cp_type), required_altitude(alt_msl), height_type(AltitudeType::ABSOLUTE_ALTITUDE) {} // WGS84Point构造函数: (经度, 纬度, 高度)

			// --- 链式设置函数 (返回引用以支持链式调用) ---
			/** @brief 设置要求的速度向量。 */
			ControlPoint& setRequiredSpeed(const Vector3D& speed) { required_speed = speed; return *this; }
			/** @brief 清除速度要求。 */
			ControlPoint& clearRequiredSpeed() { required_speed.reset(); return *this; }
			/** @brief 设置要求的航向角 (度)。 */
			ControlPoint& setRequiredHeading(double heading_deg) { required_heading_deg = heading_deg; return *this; }
			/** @brief 清除航向要求。 */
			ControlPoint& clearRequiredHeading() { required_heading_deg.reset(); return *this; }
			/** @brief 设置要求的姿态。 */
			ControlPoint& setRequiredAttitude(const Orientation& attitude) { required_attitude = attitude; return *this; }
			/** @brief 清除姿态要求。 */
			ControlPoint& clearRequiredAttitude() { required_attitude.reset(); return *this; }
			/** @brief 设置停留时间 (秒)。 */
			ControlPoint& setLoiterTime(Time duration) { required_loiter_time = std::max(0.0, duration); return *this; }
			/** @brief 设置载荷动作。 */
			ControlPoint& setPayloadAction(PayloadActionPtr act) { action = std::move(act); return *this; }

			// --- 辅助检查函数 ---
			/** @brief 检查是否有速度要求。 */
			bool hasSpeedRequirement() const { return required_speed.has_value(); }
			/** @brief 检查是否有航向要求。 */
			bool hasHeadingRequirement() const { return required_heading_deg.has_value(); }
			/** @brief 检查是否有姿态要求。 */
			bool hasAttitudeRequirement() const { return required_attitude.has_value(); }
			/** @brief 检查是否有停留时间要求。 */
			bool hasLoiterTime() const { return required_loiter_time > Constants::TIME_EPSILON; }
			/** @brief 检查是否有载荷动作。 */
			bool hasPayloadAction() const { return action != nullptr; }

			/** @brief 获取要求的速度，如果无要求则返回零向量。 */
			Vector3D getRequiredSpeedOrZero() const { return required_speed.value_or(Vector3D::Zero()); }
		};

		// 控制点列表类型别名
		using ControlPointList = std::vector<ControlPoint>;

	} // namespace NSMission
} // namespace NSDrones