// include/planning/planning_result.h
#pragma once

#include "planning/planning_types.h" 
#include "core/types.h"           
#include <vector>
#include <map>
#include <string>
#include <memory>                   
#include <utility>                  

namespace NSDrones {
	namespace NSPlanning {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class PlanningResult
		 * @brief 存储任务规划或路径规划的输出结果。
		 *
		 * 包含规划是否成功、相关消息、生成的无人机航线 (PlannedRoute) 列表，
		 * 以及规划过程中产生的告警事件 (WarningEvent)。
		 */
		class PlanningResult {
		public:
			/** @brief 默认构造函数，初始化为失败状态。 */
			PlanningResult() : success_(false), message_("规划未执行或未设置状态") {}

			/**
			 * @brief 设置规划结果的状态和消息。
			 * @param success 规划是否成功。
			 * @param message (可选) 相关消息或错误描述。
			 */
			void setStatus(bool success, std::string message = "");

			/**
			 * @brief 设置规划成功状态。
			 * @param success 规划是否成功。
			 */
			void setSuccess(bool success);

			/**
			 * @brief 添加错误消息。
			 * @param error_message 错误消息。
			 */
			void addError(const std::string& error_message);

			/** @brief 检查规划是否总体成功。 */
			bool wasSuccessful() const;

			/** @brief 获取与规划结果相关的消息（通常是失败原因或总体状态）。 */
			const std::string& getMessage() const;

			/**
			 * @brief 添加或更新指定无人机的规划航线。
			 *        如果该无人机的航线已存在，则会被新航线覆盖。
			 * @param route 要添加的规划航线对象 (通过移动语义获取所有权)。
			 */
			void addRoute(PlannedRoute route);

			/** @brief 获取所有规划航线的映射。 */
			const std::map<ObjectID, PlannedRoute>& getAllRoutes() const;

			/**
			 * @brief 获取指定无人机的规划航线指针 (const 版本)。
			 * @param uav_id 无人机 ID。
			 * @return 指向 PlannedRoute 的 const 指针，如果未找到则返回 nullptr。
			 */
			const PlannedRoute* getRouteForUav(const ObjectID& uav_id) const;

			/**
			 * @brief 获取指定无人机的规划航线指针 (可修改版本)。
			 * @param uav_id 无人机 ID。
			 * @return 指向 PlannedRoute 的指针，如果未找到则返回 nullptr。
			 */
			PlannedRoute* getRouteForUav(const ObjectID& uav_id);

			/**
			 * @brief 添加一条告警事件。
			 * @param warning 要添加的告警事件对象 (通过移动语义获取所有权)。
			 */
			void addWarning(WarningEvent warning);

			/** @brief 获取所有告警事件的列表 (const 引用)。 */
			const std::vector<WarningEvent>& getWarnings() const;

			/** @brief 清空所有结果（航线、告警），并将状态重置为失败。 */
			void clear();

			/**
			 * @brief 获取指定无人机的规划航线引用。
			 * @param uav_id 无人机 ID。
			 * @return 对 PlannedRoute 的 const 引用。
			 * @throws DroneException 如果未找到指定 ID 的航线。
			 */
			const PlannedRoute& getRouteOrThrow(const ObjectID& uav_id) const;

			/**
			 * @brief 检查规划结果是否有效（成功且至少有一条非空航线）。
			 * @return 如果结果有效返回 true。
			 */
			bool isEmpty() const;

		private:
			bool success_ = false;                           // 规划是否成功
			std::string message_ = "规划未执行或未设置状态";   // 相关消息
			std::map<ObjectID, PlannedRoute> routes_;       // 无人机 ID 到规划航线的映射
			std::vector<WarningEvent> warnings_;            // 告警事件列表
		};

	} // namespace NSPlanning
} // namespace NSDrones