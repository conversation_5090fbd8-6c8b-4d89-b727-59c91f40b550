// include/environment/maps/igridmap.h
#pragma once

#include "core/types.h"
#include "params/parameters.h"
#include <utility>
#include <string>
#include <optional>   // 用于返回可能不存在的值
#include <vector>     // 用于图层名称列表
#include <ctime>      // 用于时间戳
#include <utility>    // 用于 std::pair
#include <nlohmann/json.hpp>

namespace NSDrones {
	namespace NSEnvironment {
		using namespace NSDrones::NSUtils; // 引入工具命名空间，可能包含坐标转换等
		using namespace NSDrones::NSCore;   // 引入核心类型命名空间，如 Vector3D, WGS84Point 等

		/**
		 * @brief 地图元数据结构
		 */
		struct MapMetadata {
			// 基本地图信息
			std::string file_path;           		///< 源文件路径
			std::string file_format;                ///< 文件格式（geotiff、tiff 等）
			std::string map_id;                     ///< 地图唯一标识符
			std::string map_name;                   ///< 地图名称

			// 坐标系统信息（从 GeoTIFF 文件中提取）
			///< GeoTIFF 文件的四个角点坐标，顺序为：左上（为局部坐标系坐标原点）-右上-右下-左下
			std::array<WGS84Point, 4> wgs84_corners;
			std::pair<double,double> resolution;    ///< 分辨率（米/像素）(X, Y)
			std::pair<int, int> grid_size;          ///< 网格大小 (width, height)

			// 最高最低点坐标
			WGS84Point min_elevation_point; ///< 最低高程点的 WGS84 坐标
			WGS84Point max_elevation_point; ///< 最高高程点的 WGS84 坐标

			// 边界信息
			WGS84BoundingBox wgs84_bounds;  ///< WGS84 边界框
			BoundingBox world_bounds;       ///< 世界坐标边界框

			// 图层信息（GridMap 中的标准图层）
			std::vector<std::string> available_layers;      			///< 可用的标准图层名称列表
			inline static const std::string elevation_layer = "elevation";      		///< 高程图层的标准名称
			inline static const std::string feature_layer = "feature";          		///< 地物类型图层的标准名称
			inline static const std::string feature_height_layer = "feature_height"; 	///< 地物高度图层的标准名称

			/**
			 * @brief 默认构造函数
			 */
			MapMetadata() : resolution(0.0, 0.0), grid_size(0, 0) {}

			/**
			 * @brief 检查元数据是否有效
			 */
			bool isValid() const {
				return !file_path.empty() && resolution.first > 0.0 && resolution.second > 0.0 &&
					   grid_size.first > 0 && grid_size.second > 0 &&
					   !available_layers.empty(); // 至少要有一个可用图层
			}

			/**
			 * @brief 转换为字符串表示
			 */
			std::string toString() const {
				std::string result = fmt::format(
					"MapMetadata[id='{}', file='{}', format='{}', resolution=({:.3f},{:.3f}), size={}x{}, layers={}",
					map_id, file_path, file_format, resolution.first, resolution.second,
					grid_size.first, grid_size.second, available_layers.size());

				result += ", corners=4";
				result += "]";
				return result;
			}
		};

		/**
		 * @class IGridMap
		 * @brief 网格地图接口 (Interface Grid Map) - 纯虚基类。
		 *
		 * 定义了一套标准方法来查询地理空间信息，例如地形高程、地表覆盖特征（地物）等。
		 * 所有接受或返回地理坐标的方法都应使用 WGS84 (经纬度) 坐标系。
		 * 实现此接口的类需要负责处理其内部数据源的特定坐标系与 WGS84 之间的转换。
		 * 该接口旨在解耦地图数据的使用方与具体的地图数据格式或来源。
		 */
		class IGridMap {
		public:
			/**
			 * @brief 虚析构函数。
			 * 确保通过基类指针删除派生类对象时能够正确调用派生类的析构函数。
			 */
			virtual ~IGridMap() = default;

			//=== 初始化接口 ===//

			/**
			 * @brief 初始化地图数据源
			 * @param global_params 全局参数对象，用于获取地图相关配置
			 * @return 如果成功初始化则返回 true，否则返回 false
			 */
			virtual bool initialize(std::shared_ptr<NSParams::ParamValues> global_params) = 0;

			//=== 数据查询接口 ===//

			/**
			 * @brief 获取指定WGS84经纬度位置的地形高程（海拔高度）。
			 * @param latitude WGS84纬度 (单位：度)。
			 * @param longitude WGS84经度 (单位：度)。
			 * @return std::optional<double> 如果成功获取就返回海拔高度(单位：米);
			 *         如果查询点超出数据范围或未找到数据，则返回空值。
			 */
			virtual std::optional<double> getElevation(double latitude, double longitude) const = 0;

			/**
			 * @brief 获取指定WGS84经纬度位置的地表覆盖特征类型 (地物类型)。
			 * @param latitude WGS84纬度 (单位：度)。
			 * @param longitude WGS84经度 (单位：度)。
			 * @return std::optional<FeatureType> 如果成功获取就返回地物类型;
			 *         如果查询点超出数据范围或未找到数据，则返回空值。
			 */
			virtual std::optional<FeatureType> getFeature(double latitude, double longitude) const = 0;

			/**
			 * @brief 获取指定WGS84经纬度位置的地表覆盖特征的高度（例如建筑物高度、树木高度）。
			 * 这个高度通常是相对于局部地形表面的高度。
			 * @param latitude WGS84纬度 (单位：度)。
			 * @param longitude WGS84经度 (单位：度)。
			 * @return std::optional<double> 如果成功获取就返回特征高度(单位：米);
			 *         如果查询点超出数据范围或未找到数据，则返回空值。
			 */
			virtual std::optional<double> getFeatureHeight(double latitude, double longitude) const = 0;

			/**
			 * @brief 检查数据源是否已成功初始化并准备好提供数据。
			 * @return 如果已初始化则为 true，否则为 false。
			 */
			virtual bool isInitialized() const = 0;

			/**
			 * @brief 获取地图元数据
			 * @return MapMetadata 包含地图的详细元数据信息
			 * @note 对于TiledGridMap，这个方法返回一个汇总的元数据，具体的瓦片信息请使用getTileMetadataList()
			 */
			virtual MapMetadata getMetadata() const { return metadata_;}

			/**
			 * @brief 获取所有瓦片的元数据列表（仅对TiledGridMap有效）
			 * @return 瓦片元数据列表，对于SingleGridMap返回包含自身的单元素向量
			 */
			virtual std::vector<MapMetadata> getTileMetadataList() const {
				return {getMetadata()};  // 默认实现：返回自身元数据
			}

			//=== 数据覆盖检查接口 ===//

			/**
			 * @brief 检查指定WGS84坐标是否在地图数据覆盖范围内
			 * @param latitude WGS84纬度 (单位：度)
			 * @param longitude WGS84经度 (单位：度)
			 * @return 如果坐标在数据覆盖范围内返回true，否则返回false
			 */
			virtual bool isCovered(double latitude, double longitude) const = 0;

		protected:
			MapMetadata metadata_; ///< 地图元数据
			/**
			 * @brief 智能检测图层名称（配置优先 + 自动回退）
			 * @param available_layers 可用的图层名称列表
			 * @param global_params 全局参数对象
			 * @param elevation_layer 输出：检测到的高程图层名称
			 * @param feature_layer 输出：检测到的地物图层名称（可选）
			 * @param feature_height_layer 输出：检测到的地物高度图层名称（可选）
			 * @return 检测成功返回 true（至少找到高程图层）
			 */
			static bool detectLayers(const std::vector<std::string>& available_layers,
									 std::shared_ptr<NSParams::ParamValues> global_params,
									 std::string& elevation_layer,
									 std::string& feature_layer,
									 std::string& feature_height_layer);

			/**
			 * @brief 从候选列表中检测图层
			 * @param candidates 候选图层名称列表
			 * @param available_layers 可用的图层名称列表
			 * @return 找到的图层名称，如果没有找到则返回空字符串
			 */
			static std::string detectLayerFromCandidates(const std::vector<std::string>& candidates,
														 const std::vector<std::string>& available_layers);

		};

	} // namespace NSEnvironment
} // namespace NSDrones
