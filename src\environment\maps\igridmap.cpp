// src/environment/maps/igridmap.cpp
#include "environment/maps/igridmap.h"
#include "utils/logging.h"
#include "spdlog/fmt/ranges.h"
#include <algorithm>
#include <cctype>

namespace NSDrones {
	namespace NSEnvironment {
		bool IGridMap::detectLayers(const std::vector<std::string>& available_layers,
									std::shared_ptr<NSParams::ParamValues> global_params,
									std::string& elevation_layer,
									std::string& feature_layer,
									std::string& feature_height_layer) {
			LOG_DEBUG("IGridMap::detectLayers - 开始智能检测图层名称");

			if (!global_params) {
				LOG_ERROR("IGridMap::detectLayers - 全局参数为空，无法检测图层");
				return false;
			}

			if (available_layers.empty()) {
				LOG_WARN("IGridMap::detectLayers - 没有可用的图层");
				return false;
			}

			LOG_DEBUG("IGridMap::detectLayers - 可用图层: [{}]", fmt::join(available_layers, ", "));

			// 获取图层映射配置
			auto layer_mapping = global_params->getValueOrDefault<std::vector<std::vector<std::string>>>(
				"map.layer_mapping",
				std::vector<std::vector<std::string>>{
					{"elevation", "height", "dem", "dtm", "altitude", "z"},
					{"feature_type", "landuse", "classification", "class", "type"},
					{"feature_height", "building_height", "height_above_ground", "structure_height", "object_height"}
				}
			);

			// 1. 检测高程图层（配置优先 + 自动回退）
			auto config_elevation = global_params->getValueOrDefault<std::string>("map.layer_config.elevation_layer", "");
			if (!config_elevation.empty() && std::find(available_layers.begin(), available_layers.end(), config_elevation) != available_layers.end()) {
				elevation_layer = config_elevation;
				LOG_INFO("IGridMap::detectLayers - 使用配置的高程图层: '{}'", elevation_layer);
			} else {
				if (layer_mapping.size() > 0) {
					elevation_layer = detectLayerFromCandidates(layer_mapping[0], available_layers);
				}
				if (elevation_layer.empty()) {
					LOG_ERROR("IGridMap::detectLayers - 无法找到高程图层");
					return false;
				}
				LOG_INFO("IGridMap::detectLayers - 自动检测到高程图层: '{}'", elevation_layer);
			}

			// 2. 检测地物图层（可选）
			auto config_feature = global_params->getValueOrDefault<std::string>("map.layer_config.feature_layer", "");
			if (!config_feature.empty() && std::find(available_layers.begin(), available_layers.end(), config_feature) != available_layers.end()) {
				feature_layer = config_feature;
				LOG_INFO("IGridMap::detectLayers - 使用配置的地物图层: '{}'", feature_layer);
			} else {
				if (layer_mapping.size() > 1) {
					feature_layer = detectLayerFromCandidates(layer_mapping[1], available_layers);
				}
				if (!feature_layer.empty()) {
					LOG_INFO("IGridMap::detectLayers - 自动检测到地物图层: '{}'", feature_layer);
				} else {
					LOG_DEBUG("IGridMap::detectLayers - 未找到地物图层，将跳过地物类型查询");
				}
			}

			// 3. 检测地物高度图层（可选）
			auto config_feature_height = global_params->getValueOrDefault<std::string>("map.layer_config.feature_height_layer", "");
			if (!config_feature_height.empty() && std::find(available_layers.begin(), available_layers.end(), config_feature_height) != available_layers.end()) {
				feature_height_layer = config_feature_height;
				LOG_INFO("IGridMap::detectLayers - 使用配置的地物高度图层: '{}'", feature_height_layer);
			} else {
				if (layer_mapping.size() > 2) {
					feature_height_layer = detectLayerFromCandidates(layer_mapping[2], available_layers);
				}
				if (!feature_height_layer.empty()) {
					LOG_INFO("IGridMap::detectLayers - 自动检测到地物高度图层: '{}'", feature_height_layer);
				} else {
					LOG_DEBUG("IGridMap::detectLayers - 未找到地物高度图层，将跳过地物高度查询");
				}
			}

			LOG_INFO("IGridMap::detectLayers - 图层检测完成 - 高程: '{}', 地物: '{}', 地物高度: '{}'",
					 elevation_layer, feature_layer, feature_height_layer);
			return true;
		}

		std::string IGridMap::detectLayerFromCandidates(const std::vector<std::string>& candidates,
														const std::vector<std::string>& available_layers) {
			for (const auto& candidate : candidates) {
				for (const auto& available : available_layers) {
					// 精确匹配
					if (available == candidate) {
						LOG_DEBUG("IGridMap::detectLayerFromCandidates - 精确匹配找到图层: '{}'", available);
						return available;
					}
					// 包含匹配（不区分大小写）
					std::string available_lower = available;
					std::string candidate_lower = candidate;
					std::transform(available_lower.begin(), available_lower.end(), available_lower.begin(), ::tolower);
					std::transform(candidate_lower.begin(), candidate_lower.end(), candidate_lower.begin(), ::tolower);

					if (available_lower.find(candidate_lower) != std::string::npos) {
						LOG_DEBUG("IGridMap::detectLayerFromCandidates - 包含匹配找到图层: '{}' (候选: '{}')", available, candidate);
						return available;
					}
				}
			}
			LOG_DEBUG("IGridMap::detectLayerFromCandidates - 未找到匹配的图层，候选: [{}]", fmt::join(candidates, ", "));
			return "";
		}



	} // namespace NSEnvironment
} // namespace NSDrones
