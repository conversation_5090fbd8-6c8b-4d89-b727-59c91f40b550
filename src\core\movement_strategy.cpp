// src/core/movement_strategy.cpp
#include "core/movement_strategy.h"
#include "core/entity_object.h"
#include "environment/coordinate/coordinate_converter.h"
#include "utils/object_id.h"

// 前向声明，避免循环依赖
namespace NSDrones {
	namespace NSEnvironment {
		class Environment;
	}
}

namespace NSDrones {
	namespace NSCore {
		IMovementStrategy::IMovementStrategy(const EntityObject& owner)
			:owner_(owner) {}
		EcefPoint IMovementStrategy::predictEcefPosition(const EntityObject& owner, Time dt) const {
				// 默认实现：通过WGS84位置转换得到ECEF位置
				auto wgs84_pos = predictWGS84Position(owner, dt);
				return CoordinateConverter::wgs84ToECEF(wgs84_pos);
			}
		// --- StillMovementStrategy ---
		bool StillMovementStrategy::initialize(const nlohmann::json& params, EntityObject& owner) {
			LOG_DEBUG("StillMovementStrategy: 初始化对象 '{}' 的静止移动策略...", owner.getId());
			if (params.is_object() && !params.empty()) {
				LOG_WARN("StillMovementStrategy: 对象 '{}' 的静止策略收到了非空参数，但会被忽略: {}", owner.getId(), params.dump(2));
			}
			// 静止策略通常不需要参数
			LOG_INFO("StillMovementStrategy: 对象 '{}' 的静止移动策略初始化完成。", owner.getId());
			return true;
		}

		void StillMovementStrategy::updateState(EntityObject& object, Time /*dt*/) {
			LOG_DEBUG("StillMovementStrategy: 对象 '{}' 状态更新被调用，无操作。", object.getId());
			// 静止策略不改变对象状态
		}

		WGS84Point StillMovementStrategy::predictWGS84Position(const EntityObject& object, Time /*dt*/) const {
			LOG_DEBUG("StillMovementStrategy: 对象 '{}' WGS84位置预测被调用，返回当前WGS84位置。", object.getId());

			// 静态策略：位置不变，直接返回当前WGS84位置
			WGS84Point current_wgs84 = object.getWGS84Position();

			LOG_TRACE("StillMovementStrategy: 对象 '{}' 预测位置: {}", object.getId(), current_wgs84.toString());
			return current_wgs84;
		}


		// --- LinearMovementStrategy ---
		bool LinearMovementStrategy::initialize(const nlohmann::json& params, EntityObject& owner) {
			LOG_DEBUG("LinearMovementStrategy: 初始化对象 '{}' 的线性移动策略...", owner.getId());
			if (!params.is_object()) {
				LOG_WARN("LinearMovementStrategy: 初始化对象 '{}' 时策略参数不是一个JSON对象。将尝试使用对象当前速度或零速度。", owner.getId());
				// 即使参数不是对象，也尝试从owner获取速度
				if (owner.getVelocity().squaredNorm() > Constants::VELOCITY_EPSILON_SQ) {
					velocity_ = owner.getVelocity();
					LOG_INFO("LinearMovementStrategy: 对象 '{}' 策略速度从对象当前速度初始化为: ({:.2f}, {:.2f}, {:.2f})",
						owner.getId(), velocity_.x(), velocity_.y(), velocity_.z());
				}
				else {
					LOG_INFO("LinearMovementStrategy: 对象 '{}' 策略速度未在参数中指定，且对象当前速度为零。使用默认零速度。", owner.getId());
					velocity_ = Vector3D::Zero();
				}
				return true; // 允许在没有参数的情况下初始化
			}

			try {
				// 尝试从参数中读取速度
				if (params.contains("velocity") && params["velocity"].is_array() && params["velocity"].size() == 3) {
					velocity_ = Vector3D(params["velocity"][0].get<double>(),
						params["velocity"][1].get<double>(),
						params["velocity"][2].get<double>());
					LOG_INFO("LinearMovementStrategy: 对象 '{}' 策略速度从参数初始化为: ({:.2f}, {:.2f}, {:.2f})",
						owner.getId(), velocity_.x(), velocity_.y(), velocity_.z());
				}
				else {
					// 如果参数中没有提供速度，则尝试使用对象当前的速度（如果对象已经有速度）
					// 或者保持默认的零速度
					if (owner.getVelocity().squaredNorm() > Constants::VELOCITY_EPSILON_SQ) {
						velocity_ = owner.getVelocity();
						LOG_INFO("LinearMovementStrategy: 对象 '{}' 策略速度从对象当前速度初始化为: ({:.2f}, {:.2f}, {:.2f})",
							owner.getId(), velocity_.x(), velocity_.y(), velocity_.z());
					}
					else {
						LOG_INFO("LinearMovementStrategy: 对象 '{}' 策略速度未在参数中指定，且对象当前速度为零。使用默认零速度。", owner.getId());
						velocity_ = Vector3D::Zero(); // 确保设置为零
					}
				}
			}
			catch (const nlohmann::json::exception& e) {
				LOG_ERROR("LinearMovementStrategy: 初始化对象 '{}' 时解析参数 'velocity' 失败: {}. 将使用对象当前速度或零速度。", owner.getId(), e.what());
				// 回退到使用对象当前速度或零速度
				if (owner.getVelocity().squaredNorm() > Constants::VELOCITY_EPSILON_SQ) {
					velocity_ = owner.getVelocity();
				}
				else {
					velocity_ = Vector3D::Zero();
				}
				// 即使解析失败，也认为初始化是成功的，因为它有回退机制
			}
			LOG_INFO("LinearMovementStrategy: 对象 '{}' 的线性移动策略初始化完成。最终速度: ({:.2f}, {:.2f}, {:.2f})", owner.getId(), velocity_.x(), velocity_.y(), velocity_.z());
			return true;
		}

		void LinearMovementStrategy::updateState(EntityObject& object, Time dt) {
			// 使用ECEF坐标系进行精确的位置计算
			WGS84Point current_wgs84 = object.getWGS84Position();
			EcefPoint current_ecef = CoordinateConverter::wgs84ToECEF(current_wgs84);

			// 在ECEF坐标系中进行位置更新（假设velocity_是ECEF坐标系中的速度）
			EcefPoint new_ecef(current_ecef.toVector3D() + velocity_ * dt);
			WGS84Point new_wgs84 = CoordinateConverter::ecefToWGS84(new_ecef);

			// 更新对象位置
			object.setWGS84Position(new_wgs84);

			LOG_TRACE("LinearMovementStrategy: 对象 '{}' 位置已更新到 {}", object.getId(), new_wgs84.toString());
		}

		WGS84Point LinearMovementStrategy::predictWGS84Position(const EntityObject& object, Time dt) const {
			// 使用ECEF坐标系进行精确的位置预测，返回WGS84坐标（人类可理解）
			WGS84Point current_wgs84 = object.getWGS84Position();
			EcefPoint current_ecef = CoordinateConverter::wgs84ToECEF(current_wgs84);

			// 在ECEF坐标系中进行位置预测（假设velocity_是ECEF坐标系中的速度）
			EcefPoint future_ecef(current_ecef.toVector3D() + velocity_ * dt);

			// 转换回WGS84坐标
			WGS84Point future_wgs84 = CoordinateConverter::ecefToWGS84(future_ecef);

			// 返回WGS84坐标（人类可理解的地理位置）
			LOG_TRACE("LinearMovementStrategy: 对象 '{}' 预测WGS84位置: {}", object.getId(), future_wgs84.toString());
			return future_wgs84;
		}


		// --- AttachedMovementStrategy ---
		bool AttachedMovementStrategy::initialize(const nlohmann::json& params, EntityObject& owner) {
			LOG_DEBUG("AttachedMovementStrategy: 初始化对象 '{}' 的附着移动策略...", owner.getId());
			if (!params.is_object()) {
				LOG_ERROR("AttachedMovementStrategy: 初始化失败，对象 '{}' 的策略参数不是一个JSON对象。", owner.getId());
				return false;
			}

			try {
				// 读取 target_parent_id
				this->target_parent_id_ = params.value("target_parent_id", ""); // 使用 json::value
				if (!NSUtils::isValidObjectID(this->target_parent_id_)) {
					this->target_parent_id_ = owner.getParentId();
					if (NSUtils::isValidObjectID(this->target_parent_id_)) {
						LOG_INFO("AttachedMovementStrategy: 对象 '{}' 策略参数未提供有效 'target_parent_id'，使用对象自身的父ID: '{}'", owner.getId(), this->target_parent_id_);
					}
					else {
						LOG_ERROR("AttachedMovementStrategy: 初始化失败，对象 '{}' 策略参数 'target_parent_id' ('{}') 无效，且对象自身无父ID。", owner.getId(), this->target_parent_id_);
						return false;
					}
				}

				// 读取 offset
				if (params.contains("offset") && params["offset"].is_array() && params["offset"].size() == 3) {
					this->offset_ = Vector3D(params["offset"][0].get<double>(),
						params["offset"][1].get<double>(),
						params["offset"][2].get<double>());
				}
				else {
					LOG_INFO("AttachedMovementStrategy: 对象 '{}' 策略参数 'offset' 未提供或格式不正确，使用默认零偏移。", owner.getId());
					this->offset_ = Vector3D::Zero();
				}

				// 读取 use_parent_orientation
				this->use_parent_orientation_ = params.value("use_parent_orientation", false); // 使用 json::value

				// 读取 target_object_id (这是你添加的)
				if (params.contains("target_object_id") && params["target_object_id"].is_string()) {
					this->target_object_id_ = params.value("target_object_id", "");
					LOG_INFO("AttachedMovementStrategy for object '{}': 目标对象ID设置为 '{}'", owner.getId(), this->target_object_id_);
				}
				else {
					// Consider if this is an error or if target_object_id_ can be optional
					// For now, let's assume it might be optional or might use target_parent_id_ if empty.
					// If it's mandatory, you should return false and log an error.
					LOG_WARN("AttachedMovementStrategy for object '{}': JSON参数中未找到 'target_object_id' (string)。将使用 target_parent_id ('{}') 作为回退或保持为空。", owner.getId(), this->target_parent_id_);
					if (this->target_object_id_.empty()) this->target_object_id_ = this->target_parent_id_; // Example fallback
				}

				// 读取 relative_position (这是你添加的)
				if (params.contains("relative_position") && params["relative_position"].is_array() && params["relative_position"].size() == 3) {
					try {
						this->relative_position_ = Vector3D(params["relative_position"][0].get<double>(),
							params["relative_position"][1].get<double>(),
							params["relative_position"][2].get<double>());
						LOG_INFO("AttachedMovementStrategy for object '{}': 相对位置设置为 ({}, {}, {})",
							owner.getId(), this->relative_position_.x(), this->relative_position_.y(), this->relative_position_.z());
					}
					catch (const nlohmann::json::exception& e) {
						LOG_ERROR("AttachedMovementStrategy for object '{}': 初始化错误 - 解析 'relative_position' 失败: {}. 使用默认值 (0,0,0).", owner.getId(), e.what());
						this->relative_position_ = Vector3D(0, 0, 0);
					}
				}
				else {
					LOG_WARN("AttachedMovementStrategy for object '{}': JSON参数中未找到 'relative_position' (array)。将使用默认值 (0,0,0).", owner.getId());
					this->relative_position_ = Vector3D(0, 0, 0); // Default if not specified
				}

				LOG_INFO("AttachedMovementStrategy: 对象 '{}' 的附着移动策略初始化完成。目标父ID: '{}', 偏移: ({:.2f},{:.2f},{:.2f}), 使用父姿态: {}, 目标对象ID(新): '{}', 相对位置(新): ({:.2f}, {:.2f}, {:.2f})",
					owner.getId(), this->target_parent_id_, this->offset_.x(), this->offset_.y(), this->offset_.z(), this->use_parent_orientation_, this->target_object_id_, this->relative_position_.x(), this->relative_position_.y(), this->relative_position_.z());

			}
			catch (const nlohmann::json::exception& e) {
				LOG_ERROR("AttachedMovementStrategy: 初始化对象 '{}' 时解析参数失败: {}. 策略可能无法正常工作。", owner.getId(), e.what());
				return false; // 初始化失败
			}
			return true;
		}

		void AttachedMovementStrategy::updateState(EntityObject& object, Time /*dt*/) {
			if (!NSUtils::isValidObjectID(this->target_parent_id_)) {
				LOG_WARN("AttachedMovementStrategy: 对象 '{}' 的目标父对象 ID ('{}') 无效，无法更新位置。", object.getId(), this->target_parent_id_);
				return;
			}

			// 使用单例模式获取环境
			auto env = Environment::getInstance();
			if (!env) {
				LOG_ERROR("AttachedMovementStrategy: 对象 '{}' 无法获取环境实例，无法更新位置。", object.getId());
				return;
			}

			auto parent_object_ptr = env->getObjectById(this->target_parent_id_);
			if (!parent_object_ptr) {
				LOG_WARN("AttachedMovementStrategy: 对象 '{}' 无法在环境中找到目标父对象 ID '{}'，无法更新位置。", object.getId(), this->target_parent_id_);
				return;
			}

			auto parent_pos_opt = parent_object_ptr->getNedPosition();
			if (!parent_pos_opt.has_value()) {
				LOG_WARN("AttachedMovementStrategy: 对象 '{}' 无法获取父对象 '{}' 的局部位置，跳过位置更新", object.getId(), this->target_parent_id_);
				return;
			}
			NedPoint parent_pos = parent_pos_opt.value();
			Vector3D final_offset = this->offset_;

			if (this->use_parent_orientation_) {
				Orientation parent_orientation = parent_object_ptr->getOrientation();
				final_offset = parent_orientation * this->offset_; // 旋转偏移量
			}

			NedPoint new_pos = parent_pos + final_offset;
			object.setNedPosition(new_pos);

			// 可选：如果需要，也可以同步姿态
			// object.setOrientation(parent_object_ptr->getCurrentOrientation());
		}

		WGS84Point AttachedMovementStrategy::predictWGS84Position(const EntityObject& object, Time dt) const {
			// 使用ECEF坐标系进行精确计算，返回WGS84坐标（人类可理解）
			WGS84Point current_wgs84 = object.getWGS84Position();

			if (!NSUtils::isValidObjectID(this->target_parent_id_)) {
				LOG_WARN("AttachedMovementStrategy: 对象 '{}' 预测WGS84位置失败，目标父对象 ID ('{}') 无效。", object.getId(), this->target_parent_id_);
				return current_wgs84; // 返回当前WGS84位置作为回退
			}

			// 使用单例模式获取环境
			auto env = Environment::getInstance();
			if (!env) {
				LOG_ERROR("AttachedMovementStrategy: 对象 '{}' 无法获取环境实例，无法预测位置。", object.getId());
				return current_wgs84; // 返回当前WGS84位置作为回退
			}

			auto parent_object_ptr = env->getObjectById(this->target_parent_id_);
			if (!parent_object_ptr) {
				LOG_WARN("AttachedMovementStrategy: 对象 '{}' 预测WGS84位置失败，无法在环境中找到目标父对象 ID '{}'。", object.getId(), this->target_parent_id_);
				return current_wgs84; // 返回当前WGS84位置作为回退
			}

			// 获取父对象的预测WGS84位置
			WGS84Point predicted_parent_wgs84 = parent_object_ptr->predictWGS84Position(dt);

			// 计算偏移（在ECEF坐标系中进行几何运算）
			Vector3D final_offset = this->offset_;
			if (this->use_parent_orientation_) {
				Orientation parent_orientation = parent_object_ptr->getOrientation();
				final_offset = parent_orientation * this->offset_;
			}

			// 将偏移应用到预测的父对象位置（在ECEF坐标系中计算）
			EcefPoint predicted_parent_ecef = CoordinateConverter::wgs84ToECEF(predicted_parent_wgs84);
			EcefPoint predicted_ecef(predicted_parent_ecef + final_offset);
			WGS84Point predicted_wgs84 = CoordinateConverter::ecefToWGS84(predicted_ecef);

			LOG_TRACE("AttachedMovementStrategy: 对象 '{}' 预测WGS84位置: {}", object.getId(), predicted_wgs84.toString());
			return predicted_wgs84;
		}

	} // namespace NSCore
} // namespace NSDrones

// 在文件末尾包含Environment的完整定义，以支持getObjectById调用
#include "environment/environment.h"