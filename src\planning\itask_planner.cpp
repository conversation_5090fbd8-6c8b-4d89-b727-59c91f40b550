// src/planning/itask_planner.cpp
#include "planning/itask_planner.h"
#include "environment/entities/zone.h"
#include "utils/logging.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include "mission/mission.h"
#include "mission/task.h"
#include "planning/planning_types.h"
#include "core/types.h"
#include "environment/environment.h"
#include "utils/coordinate_converter.h"
#include "uav/uav.h"
#include "utils/stopwatch.h"
#include "utils/object_id.h"
#include "mission/control_point.h"
#include "environment/collision/collision_engine.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "planning/planning_result.h"
#include <utility>
#include <cmath>
#include "uav/idynamic_model.h"
#include "mission/task_strategies.h"
#include <stdexcept>
#include <map>
#include <vector>
#include <optional>
#include <algorithm>
#include <unordered_map>

namespace NSDrones {
namespace NSPlanning {

	// --- 构造函数实现 ---
    /**
     * @brief ITaskPlanner 构造函数。
     * @param env 环境指针。
     * @param path_planner 路径规划器指针。
     * @param traj_optimizer 轨迹优化器指针 (可选)。
     * @param collision_engine 碰撞引擎指针。
     * @throws DroneException 如果必要组件为空。
     */
	ITaskPlanner::ITaskPlanner(IPathPlannerPtr path_planner,
							   ITrajectoryOptimizerPtr traj_optimizer)
		: path_planner_(std::move(path_planner)),       // 存储路径规划器指针
		  trajectory_optimizer_(std::move(traj_optimizer))     // 存储轨迹优化器指针 (可能为空)
	{
		// 参数检查
		if (!path_planner_) throw DroneException("任务规划器：路径规划器不能为空。", ErrorCode::InvalidArgument);

		// 检查环境是否可用
		auto environment = Environment::getInstance();
		if (!environment) throw DroneException("任务规划器：环境实例不存在。", ErrorCode::InvalidArgument);

		// 检查碰撞引擎是否可从环境获取
		auto collision_engine = environment->getCollisionEngine();
		if (!collision_engine) throw DroneException("任务规划器：无法从环境获取碰撞引擎。", ErrorCode::InvalidArgument);

		// 轨迹优化器可以为空，只记录警告
        if (!trajectory_optimizer_) {
            LOG_WARN("任务规划器初始化时未使用轨迹优化器，路径将不会被优化。");
        } else {
             LOG_DEBUG("任务规划器基类：轨迹优化器已设置。");
        }
		LOG_DEBUG("任务规划器基类对象已创建。");
	}

	// --- 环境访问方法实现 ---
	std::shared_ptr<Environment> ITaskPlanner::getEnvironment() const {
		return Environment::getInstance();
	}

	// --- 辅助函数实现 ---
	/**
	 * @brief 获取控制点的绝对ECEF坐标位置（考虑任务策略）。
	 * @param cp 控制点。
	 * @param task 包含此控制点的任务对象，用于获取高度策略。
	 * @return pair，包含绝对ECEF位置和操作是否成功的布尔值。
	 */
	std::pair<EcefPoint, bool> ITaskPlanner::getAbsolutePosition(
		const NSMission::ControlPoint& cp,
		const NSMission::Task& task) const // **修改：传入 Task**
	{
		auto environment = getEnvironment();
		if (!environment) {
			LOG_ERROR("无法获取控制点绝对位置，环境实例不存在。");
			return {EcefPoint(0, 0, 0), false};
		}
		// 注意：坐标转换现在由 CoordinateManager 统一管理，不再需要单独的 CoordinateConverter
		// 内部几何计算使用ECEF坐标系

		EcefPoint absolute_position;
		bool success = false;

		// 将 WGS84 坐标转换为 ECEF 坐标进行内部计算
		WGS84Point wgs84_position = cp.position;
		LOG_TRACE("  控制点请求的 WGS84 位置: ({})", wgs84_position.toString());

		// 直接转换为ECEF坐标进行几何计算
		absolute_position = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_position);
		success = true;

		// ECEF坐标已经在上面设置完成，无需额外的坐标转换
		LOG_TRACE("  ECEF 位置: ({:.2f}, {:.2f}, {:.2f})",
			absolute_position.x(), absolute_position.y(), absolute_position.z());

		// 2. 根据任务高度策略调整 Z 值
		AltitudeType height_strategy = task.getDesiredHeightType(); // 修正: 从 Task 获取策略
		LOG_TRACE("  使用任务的高度策略: {}", NSUtils::enumToString(height_strategy));

		auto adjusted_height = environment->getAdjustedHeightAtPoint(wgs84_position, height_strategy, wgs84_position.altitude);
		if (adjusted_height.has_value()) {
			absolute_position.setZ(*adjusted_height);
			success = true;
			LOG_DEBUG("控制点的绝对位置计算成功: ({:.1f},{:.1f},{:.1f})", absolute_position.x(), absolute_position.y(), absolute_position.z());
		} else {
			// 高度调整失败时的回退策略
			if (height_strategy == AltitudeType::ABOVE_GROUND_LEVEL) {
				LOG_WARN("无法根据高度策略 {} 调整控制点的 Z 值，可能是地图数据不可用。使用绝对高度策略作为回退方案", NSUtils::enumToString(height_strategy));
				// 对于AGL策略失败，使用原始高度作为绝对高度
				absolute_position.setZ(wgs84_position.altitude);
			} else {
				LOG_WARN("无法根据高度策略 {} 调整控制点的 Z 值，使用原始高度", NSUtils::enumToString(height_strategy));
				absolute_position.setZ(wgs84_position.altitude);
			}
			success = true; // 改为成功，使用原始高度作为回退方案
		}

		return {absolute_position, success};
	}

    /**
     * @brief 生成简单的直线航段。
     * @param start_point 起始航路点。
     * @param end_wgs84_pos 目标WGS84位置。
     * @param speed 期望速度。
     * @param dynamics 动力学模型。
     * @return 包含起点和终点的航段。如果速度无效则为空。
     */
	PlannedRoute ITaskPlanner::generateLinearSegment(const RoutePoint& start_point,
													 const WGS84Point& end_wgs84_pos,
													 double speed,
													 const NSUav::IDynamicModel& dynamics,
                                                     const ObjectID& uav_id) const // 添加 uav_id 参数，修改返回类型
	{
        LOG_TRACE("为无人机 {} 生成从 {}@t={:.2f} 到 {} 的线性航段，速度 {:.2f} m/s",
                  uav_id, start_point.position.toString(), start_point.time_stamp,
                  end_wgs84_pos.toString(), speed);

		PlannedRoute segment(uav_id); // 创建 PlannedRoute 对象

		if (speed <= Constants::VELOCITY_EPSILON) { // 修正: 使用 VELOCITY_EPSILON
			LOG_WARN("生成线性航段失败：请求的速度 ({:.3f}) 过小或无效。", speed);
			return segment; // 返回空的 PlannedRoute
		}

		segment.addWaypoint(start_point); // 添加起点

		// 计算距离和飞行时间（使用ECEF坐标进行几何计算）
		EcefPoint start_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(start_point.position);
		EcefPoint end_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(end_wgs84_pos);
		Vector3D displacement = end_ecef.toVector3D() - start_ecef.toVector3D();
		double distance = displacement.norm();
		LOG_TRACE("  距离: {:.2f} m", distance);

		if (distance < Constants::GEOMETRY_EPSILON) { // 修正: 使用 GEOMETRY_EPSILON
			LOG_WARN("生成线性航段：起点和终点距离过近 ({:.3e} m)，只包含起点。", distance);
			return segment; // 只包含起点的 PlannedRoute
		}

		double duration = distance / speed;
		LOG_TRACE("  预计持续时间: {:.2f} s", duration);

		RoutePoint end_waypoint;
		end_waypoint.position = end_wgs84_pos;
		end_waypoint.velocity = displacement.normalized() * speed; // 匀速直线运动
		// acceleration 不需要设置
		end_waypoint.time_stamp = start_point.time_stamp + duration;
		// 姿态可以保持与速度方向一致或根据动力学模型计算
		// 确保速度向量非零
		if (end_waypoint.velocity.squaredNorm() > Constants::VELOCITY_EPSILON * Constants::VELOCITY_EPSILON) {
             end_waypoint.orientation = Eigen::Quaterniond::FromTwoVectors(Vector3D::UnitX(), end_waypoint.velocity.normalized()); // 修正: 设置 attitude
        } else {
             end_waypoint.orientation = start_point.orientation; // 如果速度为零，保持起始姿态
             LOG_TRACE("  速度接近零，保持起始姿态。");
        }

		segment.addWaypoint(end_waypoint); // 添加终点
		// is_valid, start_time, end_time, id, getDuration 等成员不属于 PlannedRoute，移除相关访问

		LOG_DEBUG("成功为无人机 {} 生成线性航段，包含 {} 个航路点，耗时 {:.2f}s",
                  uav_id, segment.getWaypoints().size(), segment.getTotalTime()); // 使用 getTotalTime
		return segment;
	}


    /**
     * @brief 检查航段中可能产生的告警。
     * @param segment 要检查的航段。
     * @param uav_id 无人机 ID。
     * @param result PlanningResult 对象引用。
     * @param task_id (可选) 关联的任务 ID。
     */
	void ITaskPlanner::checkSegmentWarnings(const PlannedRoute& segment, const ObjectID& uav_id, PlanningResult& result, const ObjectID& task_id) const { // 修正: segment 类型
		auto environment = getEnvironment();
		if (!environment || segment.getWaypoints().size() < 2) { // 修正: 使用 getWaypoints()
			return; // 无环境或航段无效
		}
		// 移除对 segment.id 的访问
		LOG_TRACE("检查航段 (UAV: {}) 的告警...", uav_id);

		const auto& zones = environment->getAllZones(); // 获取所有区域
		if (zones.empty()) {
			 LOG_TRACE("  环境中没有区域，无需检查。");
			return;
		}

		const auto& waypoints = segment.getWaypoints(); // 获取航点引用
		for (size_t i = 0; i < waypoints.size() - 1; ++i) {
			const auto& p1 = waypoints[i].position; // 修正: 访问 waypoints
			const auto& p2 = waypoints[i + 1].position; // 修正: 访问 waypoints
			const auto t1 = waypoints[i].time_stamp; // 修正: 访问 waypoints
			const auto t2 = waypoints[i + 1].time_stamp; // 修正: 访问 waypoints
			LOG_TRACE("  检查子航段 {} -> {} (t={:.2f} -> t={:.2f})", i, i + 1, t1, t2);

			// 获取子航段开始和结束时所处的区域（需要转换为ECEF坐标）
			EcefPoint p1_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(p1);
			EcefPoint p2_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(p2);
			std::vector<ConstZonePtr> zones_at_p1 = environment->getViolatedZones(p1_ecef);
			std::vector<ConstZonePtr> zones_at_p2 = environment->getViolatedZones(p2_ecef);
			std::vector<ConstZonePtr> zones_intersecting = environment->getIntersectingZones(p1_ecef, p2_ecef);

			std::unordered_map<ObjectID, ConstZonePtr> involved_zones;
            // 使用 range-based for 循环填充 map
            for(const auto& z_ptr : zones_at_p1) {
                if(z_ptr) involved_zones[z_ptr->getId()] = z_ptr;
            }
            for(const auto& z_ptr : zones_at_p2) {
                if(z_ptr) involved_zones[z_ptr->getId()] = z_ptr;
            }
            for(const auto& z_ptr : zones_intersecting) {
                if(z_ptr) involved_zones[z_ptr->getId()] = z_ptr;
            }


			LOG_TRACE("  子航段涉及 {} 个区域。", involved_zones.size());

			for (const auto& pair : involved_zones) {
				const auto& zone = pair.second; // zone 是 ConstZonePtr
                if (!zone) continue; // 防御性编程

				// 检查起点是否在区域内
				// 修正: lambda 参数类型
				bool was_inside = std::find_if(zones_at_p1.begin(), zones_at_p1.end(),
											   [&](const ConstZonePtr& z_ptr){ return z_ptr && z_ptr->getId() == zone->getId(); }) != zones_at_p1.end();
				// 检查终点是否在区域内
				// 修正: lambda 参数类型
				bool is_inside = std::find_if(zones_at_p2.begin(), zones_at_p2.end(),
											  [&](const ConstZonePtr& z_ptr){ return z_ptr && z_ptr->getId() == zone->getId(); }) != zones_at_p2.end();

				WarningType wtype = WarningType::UNKNOWN; // 修正：之前是 INFO，改为 OTHER
				std::string description;
				WGS84Point location = p1; // 默认告警位置为子航段起点
				Time time = t1;        // 默认告警时间为子航段开始

				bool generate_warning = false;

				// **简化：直接使用ZoneType判断，而不是getEntry/ExitWarningType**
				// 需要在 zone.h 中添加 getEntryWarningType/getExitWarningType 或类似方法，
				// 或者在这里根据 zone->getType() 来决定 WarningType。
				// 暂时根据进入/离开和类型来判断。
				ZoneType zone_type = zone->getType();
				if (zone_type == ZoneType::ENTER_WARNING || zone_type == ZoneType::LEAVE_WARNING) {
					if (!was_inside && is_inside) {         // 进入告警区
						wtype = WarningType::ENTERED_WARNING_ZONE;
						description = "进入告警区域 " + NSUtils::toString(zone->getId());
						location = p2;
						time = t2;
						generate_warning = true;
						LOG_DEBUG("  UAV {} 进入告警区域 {}", NSUtils::toString(uav_id), NSUtils::toString(zone->getId()));
					} else if (was_inside && !is_inside) { // 离开告警区
						wtype = WarningType::LEFT_WARNING_ZONE;
						description = "离开告警区域 " + NSUtils::toString(zone->getId());
						location = p2;
						time = t2;
						generate_warning = true;
						LOG_DEBUG("  UAV {} 离开告警区域 {}", NSUtils::toString(uav_id), NSUtils::toString(zone->getId()));
					}
				} else if (zone_type == ZoneType::KEEPOUT) {
					if (!was_inside && is_inside) { // 进入禁飞区
						wtype = WarningType::ENTERED_KEEPOUT_ZONE;
						description = "进入禁飞区域 " + NSUtils::toString(zone->getId());
						location = p2; time = t2; generate_warning = true;
						LOG_WARN("  UAV {} 进入禁飞区域 {}", NSUtils::toString(uav_id), NSUtils::toString(zone->getId()));
					} else if (zone->intersects(p1_ecef, p2_ecef)) { // 穿越禁飞区边界
						wtype = WarningType::CROSS_KEEPOUT_BOUNDARY;
						description = "穿越禁飞区域 " + NSUtils::toString(zone->getId()) + " 边界";
						// location 和 time 可以是交点和时间，简化为起点
						generate_warning = true;
						LOG_WARN("  UAV {} 穿越禁飞区域 {} 边界", NSUtils::toString(uav_id), NSUtils::toString(zone->getId()));
					}
				} else if (zone_type == ZoneType::THREAT) {
					if (!was_inside && is_inside) { // 进入威胁区
						wtype = WarningType::ENTERED_THREAT_ZONE;
						description = "进入威胁区域 " + NSUtils::toString(zone->getId());
						location = p2; time = t2; generate_warning = true;
						LOG_WARN("  UAV {} 进入威胁区域 {}", NSUtils::toString(uav_id), NSUtils::toString(zone->getId()));
					} else if (zone->intersects(p1_ecef, p2_ecef)) { // 穿越威胁区边界
						wtype = WarningType::CROSS_THREAT_BOUNDARY;
						description = "穿越威胁区域 " + NSUtils::toString(zone->getId()) + " 边界";
						generate_warning = true;
						LOG_WARN("  UAV {} 穿越威胁区域 {} 边界", NSUtils::toString(uav_id), NSUtils::toString(zone->getId()));
					}
				}
				// 可以为其他 ZoneType 添加类似逻辑

				if (generate_warning && wtype != WarningType::UNKNOWN) {
					result.addWarning(WarningEvent(wtype, description, time, location, uav_id, zone->getId(), task_id));
				}
			}
		}
        LOG_TRACE("航段 (UAV: {}) 告警检查完成。", uav_id);
	}

    /**
     * @brief 平滑几何路径并进行时间参数化。
     * @param geometric_path 输入几何路径。
     * @param uav 无人机指针。
     * @param start_state 起始状态。
     * @param desired_speed 期望速度。
     * @param optimized_segment 输出优化后的轨迹段。
     * @param result_ptr (可选) 用于添加告警。
     * @param strategies (可选) 应用于此轨迹段的策略。
     * @return 如果成功生成有效轨迹返回 true。
     */
	bool ITaskPlanner::smoothAndTimeParameterize(const std::vector<WGS84Point>& geometric_path,
												 const NSUav::UavPtr& uav,
												 const NSUav::UavState& start_state,
												 double desired_speed,
												 RouteSegment& optimized_segment,
												 PlanningResult* result_ptr,
												 const NSMission::ITaskStrategyMap& strategies) // 添加 strategies 参数
	{
		if (!uav) {
			LOG_ERROR("轨迹平滑和时间参数化失败：UAV 指针无效。" );
			if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::CRITICAL, "内部错误：UAV 指针无效", start_state.time_stamp)); // 使用构造函数，移除 ID
			return false;
		}
		if (geometric_path.size() < 2) {
			 LOG_WARN("轨迹平滑和时间参数化：几何路径点数 ({}) 不足，无法处理。", geometric_path.size());
			 if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::PLANNING_ERROR, "几何路径点数不足", start_state.time_stamp, {}, uav->getId())); // 使用构造函数，移除 ID
			 return false;
		}
		// 修正: 使用 VELOCITY_EPSILON
		if (desired_speed <= Constants::VELOCITY_EPSILON) {
			LOG_ERROR("轨迹平滑和时间参数化失败：期望速度 ({:.3f}) 无效。", desired_speed);
			if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::PLANNING_ERROR, "期望速度无效", start_state.time_stamp, {}, uav->getId())); // 使用构造函数，移除 ID
			return false;
		}

		LOG_DEBUG("开始为 UAV {} 平滑和时间参数化包含 {} 个点的几何路径，期望速度 {:.2f} m/s", uav->getId(), geometric_path.size(), desired_speed);

		// 1. 匀速时间参数化 (基础)
		RouteSegment initial_segment; // 这是 std::vector<RoutePoint>
		// 移除对 id, is_valid, start_time 的访问

		// 修正: 使用 getDynamicsModel() 并检查返回值
		const auto dynamic_model = uav->getDynamicsModel(); // 返回 ConstIDynamicModelPtr
		if (!dynamic_model) {
			LOG_ERROR("无法为 UAV {} 获取动力学模型，无法进行时间参数化。", uav->getId());
			if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::CRITICAL, "无法获取动力学模型", start_state.time_stamp, {}, uav->getId())); // 使用构造函数，移除 ID
            return false;
        }

		// 修正: 调用 timeParameterizeConstantSpeed，传递 dynamic_model.get()
		if (!timeParameterizeConstantSpeed(geometric_path, dynamic_model.get(), start_state.time_stamp, start_state.velocity, desired_speed, initial_segment)) {
			LOG_ERROR("UAV {} 匀速时间参数化失败。", uav->getId());
			if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::PLANNING_ERROR, "匀速时间参数化失败", start_state.time_stamp, {}, uav->getId())); // 使用构造函数，移除 ID
			return false;
		}

		// 移除对 id, getDuration 的访问
        if (initial_segment.empty()) {
            LOG_ERROR("UAV {} 匀速时间参数化结果为空。", uav->getId());
            return false;
        } else {
             LOG_DEBUG("UAV {} 匀速时间参数化完成，生成 {} 个航点，耗时 {:.2f}s",
                      uav->getId(), initial_segment.size(), initial_segment.back().time_stamp - initial_segment.front().time_stamp);
        }

		optimized_segment = initial_segment; // 默认使用匀速结果

		// 2. 轨迹优化 (如果配置了优化器)
		if (trajectory_optimizer_) {
			LOG_DEBUG("检测到轨迹优化器，尝试优化包含 {} 个点的航段...", initial_segment.size());
			// 移除 TrajectoryOptimizerInput 相关代码
            RouteSegment optimized_result_segment;
			PlanningResult optimization_warnings; // 存储优化过程中的告警

            // 检查环境实例是否有效
            auto environment = getEnvironment();
            if (!environment) {
                LOG_ERROR("优化失败：环境实例不存在。");
                if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::CRITICAL, "内部错误：环境实例不存在", start_state.time_stamp, {}, uav->getId()));
                // 继续使用未优化的结果
            } else {
                // 修正: 直接调用 optimizeTrajectory
                optimized_result_segment = trajectory_optimizer_->optimizer(
                    initial_segment,      // 初始航段 (const ref)
                    *dynamic_model,       // 动力学模型 (const ref)
                    strategies          // 策略 (const ref)
                );

                // 检查优化结果是否有效（例如，点数 > 1）
                if (optimized_result_segment.size() >= 2) {
                    LOG_INFO("UAV {} 的轨迹优化成功，使用优化后的航段 ({} 个点)，耗时 {:.2f}s",
                             uav->getId(), optimized_result_segment.size(),
                             optimized_result_segment.back().time_stamp - optimized_result_segment.front().time_stamp);
                    optimized_segment = optimized_result_segment; // 使用优化结果
                } else {
                    LOG_WARN("UAV {} 的轨迹优化结果无效 (点数 {} < 2)，将使用原始匀速航段。",
                             uav->getId(), optimized_result_segment.size());
                    // 添加优化失败的告警到主结果中
                    if (result_ptr) {
                        // 可以根据需要从 trajectory_optimizer_ 获取更详细的失败原因
                        result_ptr->addWarning(WarningEvent(WarningType::OPTIMIZATION_FAILED, "轨迹优化结果无效", start_state.time_stamp, {}, uav->getId())); // 使用构造函数，移除 ID
                    }
                }
            }
		} else {
			 LOG_DEBUG("未配置轨迹优化器，跳过优化步骤。");
		}

		// 3. 最终检查
        // 移除 checkSegmentWarnings 调用

		if (optimized_segment.empty()) { // 检查最终航段是否为空
			 LOG_ERROR("最终生成的航段为空。" );
			 return false;
		}

		LOG_INFO("成功为 UAV {} 平滑和时间参数化路径，最终生成 {} 个航点，耗时 {:.2f}s", uav->getId(), optimized_segment.size(),
			optimized_segment.back().time_stamp - optimized_segment.front().time_stamp);
		return true;
	}

	// isZoneConstraintSatisfied 实现
	bool ITaskPlanner::isZoneConstraintSatisfied(const RouteSegment& segment, const ConstZonePtr& zone) const
	{
		if (!zone) {
			LOG_WARN("检查区域约束时传入了无效的 Zone 指针。" );
			return true; // 无效区域无法违反，视为满足
		}
		if (segment.size() < 2) {
			// 修正: 使用 getId()
			LOG_TRACE("检查区域 {} 约束：航段点数不足 ({})，视为满足。", zone->getId(), segment.size());
			return true; // 无效航段不违反约束
		}

		LOG_TRACE("检查包含 {} 个点的航段是否满足区域 {} (ID:{}, 类型:{}) 的约束...",
				  segment.size(), zone->getId(), zone->getId(), NSUtils::enumToString(zone->getType()));

		ZoneType zone_type = zone->getType();
		// 移除 getTolerance() 调用，Zone 类没有此方法，暂时硬编码或使用常量
        double tolerance = Constants::GEOMETRY_EPSILON;

		if (zone_type == ZoneType::KEEPOUT || zone_type == ZoneType::THREAT /*RESTRICTED_FLIGHT_ZONE*/) { // 假设类型名称
			LOG_TRACE("  区域类型为禁飞/威胁区，检查航段是否进入或穿越..." );
			// 检查航段的任何部分是否在区域内（考虑容差）
			for (size_t i = 0; i < segment.size(); ++i) {
				const auto& pt = segment[i].position; // 直接访问 vector 元素
				EcefPoint pt_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(pt);
				if (zone->isInside(pt_ecef, tolerance)) { // 使用 isInside 检查点
					LOG_WARN("航段的航路点 {} ({}) 位于禁飞/威胁区 {} (容差 {:.1e}m) 内部，约束违反！",
							 i, pt.toString(), zone->getId(), tolerance);
					return false;
				}
				if (i < segment.size() - 1) {
					const auto& next_pt = segment[i + 1].position;
					EcefPoint next_pt_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(next_pt);
					if (zone->intersects(pt_ecef, next_pt_ecef, tolerance)) { // 使用 intersects 检查段
						LOG_WARN("航段的子航段 {}->{} 与禁飞/威胁区 {} (容差 {:.1e}m) 相交，约束违反！",
								 i, i+1, zone->getId(), tolerance);
						return false;
					}
				}
			}
			 // 修正: 使用 getId()
			 LOG_TRACE("  航段未进入或穿越禁飞/威胁区 {}。", zone->getId());
		} else if (zone_type == ZoneType::OPERATIONAL/*_AREA*/
			|| zone_type == ZoneType::ENTER_WARNING /*GEOFENCE*/) { // 假设类型名称
			LOG_TRACE("  区域类型为作业区/进入告警区，检查航段是否完全位于内部..." );
			// 检查航段的所有点是否都在区域内
			for (size_t i = 0; i < segment.size(); ++i) {
				const auto& pt = segment[i].position;
				EcefPoint pt_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(pt);
				 // 对于作业区/围栏，通常用负容差或零容差确保在边界内
				if (!zone->isInside(pt_ecef, -tolerance)) {
					LOG_WARN("航段的航路点 {} ({}) 位于作业区/进入告警区 {} (容差 {:.1e}m) 外部，约束违反！",
							 i, pt.toString(), zone->getId(), -tolerance);
					return false;
				}
			}
			 LOG_TRACE("  航段完全位于作业区/进入告警区 {} 内部。", zone->getId());
		} else {
			LOG_TRACE("  区域类型 {} 无特定约束检查逻辑，视为满足。", NSUtils::enumToString(zone_type));
			// 其他区域类型可能没有航段约束，或者需要特定逻辑
		}

		// 移除对 segment.id 的访问
		LOG_TRACE("航段满足区域 {} 的约束。", zone->getId());
		return true; // 如果没有检测到违反，则满足约束
	}

	bool ITaskPlanner::checkSafetyConstraints(const RoutePoint& point, const ObjectID& uav_id, const ObjectID& task_id, PlanningResult& result, const std::vector<ZoneType>& zone_types_to_check) const
	{
		auto environment = getEnvironment();
		if (!environment) {
			LOG_ERROR("ITaskPlanner checkSafetyConstraints(WGS84Point): Environment not set! Cannot check safety.");
			return false; // Or throw, depending on desired behavior
		}
		if (zone_types_to_check.empty()) {
			return true; // No specific zone types to check against, point is considered safe from this perspective
		}

		const auto& all_zones = environment->getAllZones(); // 修正 all_zones 的迭代问题
		for (const auto& zone_ptr : all_zones) { // 使用 auto&
			if (!zone_ptr) continue;

			ZoneType current_zone_logic_type = zone_ptr->getType();
			// 移除复杂的类型推断，直接使用 getType()
			// if (current_zone_logic_type == ZoneType::UNKNOWN) { ... } // 移除这部分逻辑

			if (current_zone_logic_type != ZoneType::UNKNOWN) { // 只检查已知类型的区域
				bool should_check_this_zone_type = false;
				for (ZoneType target_type_to_check_from_task : zone_types_to_check) {
					if (current_zone_logic_type == target_type_to_check_from_task) {
						should_check_this_zone_type = true;
						break;
					}
				}

				if (should_check_this_zone_type) {
					LOG_TRACE("ITaskPlanner checkSafetyConstraints(WGS84Point): Checking point against zone '{}' (Type: {})...", zone_ptr->getId(), NSUtils::enumToString(current_zone_logic_type));
					EcefPoint point_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(point.position);
					if (zone_ptr->isInside(point_ecef, 0.0)) { // tolerance 0.0 for exact check, GEOMETRY_EPSILON for slight tolerance
						LOG_WARN("ITaskPlanner checkSafetyConstraints(WGS84Point): Point {} is INSIDE zone '{}' (Type: {}). Constraint violated.", point.position.toString(), zone_ptr->getId(), NSUtils::enumToString(current_zone_logic_type));
						return false; // Point is inside a prohibited zone type
					}
				}
			}
		}
		// 根据策略，如果无法获取地面高程，可能也视为不安全

		LOG_INFO("ITaskPlanner：点 ({})@t={:.2f} 安全约束检查通过 (UAV:{}, Task:{})",
				 point.position.toString(), point.time_stamp, toString(uav_id), toString(task_id));
		return true;
	}

	// 确保函数签名与 itask_planner.h 中的声明一致
	bool ITaskPlanner::isPathSegmentSafe(const WGS84Point& wgs84_p1, const WGS84Point& wgs84_p2, Time t1, Time t2, const ObjectID& uav_id, const ObjectID& task_id, PlanningResult& result, const std::vector<ZoneType>& zone_types_to_check) const
	{
		LOG_TRACE("ITaskPlanner 检查航段 {}@t={:.2f} -> {}@t={:.2f} 的安全约束 (UAV:{}, Task:{})",
				  wgs84_p1.toString(), t1, wgs84_p2.toString(), t2, toString(uav_id), toString(task_id));

		auto environment = getEnvironment();
		if (!environment) {
			LOG_ERROR("  检查航段安全失败: 环境实例不存在。");
			// result.addWarning(WarningType::PLANNING_ERROR, "环境数据不可用，无法检查航段安全。", uav_id, task_id, t1, p1); // 修正 addWarning 调用
			result.addWarning(WarningEvent(WarningType::PLANNING_ERROR, "环境数据不可用，无法检查航段安全。", 
				t1, wgs84_p1, uav_id, INVALID_OBJECT_ID, task_id));
			return false;
		}

		// 转换为ECEF坐标进行内部几何计算
		EcefPoint ecef_p1 = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_p1);
		EcefPoint ecef_p2 = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_p2);

		// 1. 离散化检查：沿航段采样多个点进行检查
		//    更鲁棒的检查应该使用环境的 isSegmentValid 方法，但这里我们先实现一个基础版本
		int num_samples = 5; // 可以根据航段长度和时间动态调整
		Vector3D segment_vec = ecef_p2 - ecef_p1;
		double segment_len = segment_vec.norm();
		if (segment_len < Constants::GEOMETRY_EPSILON) { // 如果是点，直接检查该点
			LOG_TRACE("  航段长度接近零，检查单点 {}@t={:.2f}", wgs84_p1.toString(), t1);
			RoutePoint rp;
			rp.position = wgs84_p1;
			rp.time_stamp = t1;
			// rp.velocity, rp.attitude 等可以不设置，因为 checkSafetyConstraints 主要关注位置和时间
			return checkSafetyConstraints(rp, uav_id, task_id, result, zone_types_to_check);
		}

		for (int i = 0; i <= num_samples; ++i) {
			double ratio = static_cast<double>(i) / num_samples;
			EcefPoint current_ecef = EcefPoint(ecef_p1.toVector3D() + segment_vec * ratio);
			WGS84Point current_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(current_ecef);
			Time current_t = t1 + (t2 - t1) * ratio;
			RoutePoint current_rp;
			current_rp.position = current_wgs84;
			current_rp.time_stamp = current_t;

			if (!checkSafetyConstraints(current_rp, uav_id, task_id, result, zone_types_to_check)) {
				LOG_WARN("  航段在采样点 {} {}@t={:.2f} 处不安全。",
						 i, current_wgs84.toString(), current_t);
				// 告警已在 checkSafetyConstraints 中添加
				return false;
			}
		}
		LOG_TRACE("  航段所有采样点均满足安全约束。");

		// 2. (更优) 直接检查整个航段与区域的相交情况
		const auto& all_zones = environment->getAllZones();
		if (all_zones.empty()) {
			LOG_TRACE("  环境中无区域，跳过航段区域相交检查。");
		} else {
			LOG_TRACE("  环境中有 {} 个区域，开始检查航段与禁飞区和危险区的相交...", all_zones.size());
			for (const auto& zone_ptr : all_zones) { // zone_ptr 现在是 const ZonePtr&
				if (!zone_ptr) continue;

				if (zone_ptr->getType() == ZoneType::KEEPOUT || zone_ptr->getType() == ZoneType::THREAT) {
					EcefPoint wgs84_p1_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_p1);
					EcefPoint wgs84_p2_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_p2);
					if (zone_ptr->intersects(wgs84_p1_ecef, wgs84_p2_ecef)) {
						LOG_WARN("  航段 {}->{} 与 {} 区域 [{}] 相交。",
								 wgs84_p1.toString(), wgs84_p2.toString(),
								 NSUtils::enumToString(zone_ptr->getType()), zone_ptr->getId());
						WarningType wt = (zone_ptr->getType() == ZoneType::KEEPOUT) ?
							WarningType::ENTERED_KEEPOUT_ZONE : WarningType::ENTERED_THREAT_ZONE;
						std::string msg = "航段与" + NSUtils::enumToString(zone_ptr->getType()) + "区域 [" + zone_ptr->getId() + "] 相交。";
						result.addWarning(WarningEvent(wt, msg, t1, wgs84_p1, uav_id, zone_ptr->getId(), task_id));

						return false; // 一旦发现不安全，立即返回
					}
				}
			}
			LOG_TRACE("  航段未与任何禁飞区或危险区相交。");
		}


		// 3. (更优) 使用环境的 isSegmentValid
		// double safety_margin = 0.5; // 获取无人机安全边距
		// if (!environment_->isSegmentValid(p1, p2, t1, t2, safety_margin, true, true, 0.5, {uav_id})) {
		//    LOG_WARN("  环境报告航段不安全。");
		//    result.addWarning(WarningType::PLANNING_ERROR, "航段验证失败 (环境报告)。", uav_id, task_id, t1, p1);
		//    return false;
		// }
		// LOG_TRACE("  环境报告航段安全。");


		LOG_INFO("ITaskPlanner：航段 {}@t={:.2f} -> {}@t={:.2f} 安全检查通过 (UAV:{}, Task:{})",
				 wgs84_p1.toString(), t1, wgs84_p2.toString(), t2, uav_id, task_id);
		return true;
	}

	bool ITaskPlanner::smoothAndTimeParameterizeECEF(const std::vector<EcefPoint>& geometric_path,
													  const NSUav::UavPtr& uav,
													  const NSUav::UavState& start_state,
													  double desired_speed,
													  RouteSegment& optimized_segment,
													  PlanningResult* result_ptr,
													  const NSMission::ITaskStrategyMap& strategies)
	{
		if (!uav) {
			LOG_ERROR("轨迹平滑和时间参数化失败：UAV 指针无效。");
			if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::CRITICAL, "内部错误：UAV 指针无效", start_state.time_stamp));
			return false;
		}

		if (geometric_path.size() < 2) {
			LOG_ERROR("轨迹平滑和时间参数化失败：几何路径点数不足 (需要至少2个点，实际{}个)。", geometric_path.size());
			if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::PLANNING_FAILURE, "几何路径点数不足", start_state.time_stamp));
			return false;
		}

		// 将ECEF路径转换为WGS84路径进行处理（RouteSegment使用WGS84坐标）
		std::vector<WGS84Point> wgs84_path;
		wgs84_path.reserve(geometric_path.size());
		for (const auto& ecef_point : geometric_path) {
			wgs84_path.push_back(NSUtils::CoordinateConverter::ecefToWGS84(ecef_point));
		}

		// 简化实现：直接生成基于时间的航路点
		optimized_segment.clear();
		optimized_segment.reserve(wgs84_path.size());

		Time current_time = start_state.time_stamp;
		WGS84Point current_wgs84_pos = start_state.position;

		for (size_t i = 0; i < wgs84_path.size(); ++i) {
			RoutePoint rp;
			rp.position = wgs84_path[i];
			rp.time_stamp = current_time;
			rp.velocity = (i < wgs84_path.size() - 1) ?
				Vector3D(desired_speed, 0, 0) : Vector3D::Zero(); // 简化速度设置
			rp.orientation = start_state.orientation; // 简化姿态设置

			optimized_segment.push_back(rp);

			// 计算到下一个点的时间
			if (i < wgs84_path.size() - 1) {
				// 使用ECEF坐标计算距离（更精确）
				double distance = (geometric_path[i + 1] - geometric_path[i]).norm();
				double time_increment = distance / desired_speed;
				current_time += time_increment;
			}
		}

		LOG_DEBUG("ECEF轨迹平滑和时间参数化完成，生成 {} 个航路点。", optimized_segment.size());
		return true;
	}

} // namespace NSPlanning
} // namespace NSDrones