// include/algorithm/evaluator/itrajectory_evaluator.h
#pragma once

#include "core/types.h"        
#include "planning/planning_types.h" 
#include "uav/uav_fwd.h"           
#include "uav/uav_types.h"         
#include <vector>
#include <map>
#include <memory>               
#include "mission/mission_fwd.h" 
#include <string>
#include <optional>
#include "nlohmann/json.hpp" 

namespace NSDrones { 
	namespace NSUav { class Uav; class UavState; class IEnergyModel; }
	namespace NSParams { class ParamValues; }
	namespace NSMission { class Mission; }
	namespace NSPlanning{ using RouteID = std::string; }
}

namespace NSDrones {
	namespace NSAlgorithm {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSPlanning;

		struct TrajectorySegment {
			Time duration;
			std::vector<NSUav::UavState> states; // 离散状态点
		};
		using Trajectory = std::vector<TrajectorySegment>; // 简化表示

		/**
		 * @struct TrajectoryEvaluationResult
		 * @brief 存储轨迹评估的结果。
		 */
		struct TrajectoryCost {
			bool is_feasible = false;     // 轨迹是否可行
			std::string message = "";     // 评估消息或失败原因
			std::optional<double> estimated_energy_consumption = std::nullopt; // 预计能量消耗 (Wh)
			std::optional<Time> estimated_endurance_remaining = std::nullopt; // 预计剩余续航 (秒)
			std::optional<double> risk_score = std::nullopt;             // 风险评分 (例如 0-1)
		};

		/**
		 * @class ITrajectoryEvaluator
		 * @brief 轨迹评估器接口 (抽象基类)。
		 *
		 * 负责评估给定轨迹的可行性和成本。
		 * 评估标准可以是时间、能量消耗、风险等。
		 */
		class ITrajectoryEvaluator {
		public:
			ITrajectoryEvaluator() = default; // Default constructor
			virtual ~ITrajectoryEvaluator() = default;

			ITrajectoryEvaluator(const ITrajectoryEvaluator&) = default;
			ITrajectoryEvaluator& operator=(const ITrajectoryEvaluator&) = default;
			ITrajectoryEvaluator(ITrajectoryEvaluator&&) = default;
			ITrajectoryEvaluator& operator=(ITrajectoryEvaluator&&) = default;

		public:
			/**
			 * @brief 评估单条轨迹的成本。
			 * @param uav 无人机对象常量共享指针。
			 * @param trajectory 要评估的轨迹。
			 * @param mission (可选) 相关任务信息。
			 * @return 轨迹的成本。成本越高通常越差。返回负无穷大或特定错误值表示无效轨迹。
			 */
			virtual TrajectoryCost evaluate(
				NSUav::ConstUavPtr uav,         // 使用正确的 UAV 指针类型
				const Trajectory& trajectory,
				const NSMission::Mission* mission = nullptr) const = 0; // 纯虚函数声明，需要 const 和 = 0

			/**
			 * @brief 批量评估多条路径或轨迹的成本。
			 * @param uav 无人机对象常量共享指针。
			 * @param routes 包含多条路径/轨迹的容器。
			 * @param mission (可选) 相关任务信息。
			 * @return 一个映射，包含每条路径 ID 及其对应的成本。
			 */
			 // 定义路径映射类型，确保 RouteID 和 Trajectory 在 planning_types.h 中定义
			using RouteMap = std::map<RouteID, Trajectory>;
			virtual std::map<RouteID, TrajectoryCost> evaluate(
				NSUav::ConstUavPtr uav,       // 使用正确的 UAV 指针类型
				const RouteMap& routes,
				const NSMission::Mission* mission = nullptr) const = 0; // 纯虚函数声明，需要 const 和 = 0

		};

		using ITrajectoryEvaluatorPtr = std::shared_ptr<ITrajectoryEvaluator>;
		using ConstITrajectoryEvaluatorPtr = std::shared_ptr<const ITrajectoryEvaluator>;

	} // namespace NSAlgorithm
} // namespace NSDrones