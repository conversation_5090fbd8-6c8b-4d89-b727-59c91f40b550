// include/algorithm/trajectory_optimizer/itrajectory_optimizer.h
#pragma once

#include "core/types.h"          
#include "planning/planning_types.h" 
#include "uav/idynamic_model.h"   
#include "mission/task_strategies.h"
#include <vector>
#include <map>
#include <string>
#include <memory>                   
#include <optional>
#include "nlohmann/json.hpp" // 包含完整头文件

namespace NSDrones { namespace NSParams { class ParamValues; } }

namespace NSDrones {
	namespace NSAlgorithm {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSPlanning;

		/**
		 * @class ITrajectoryOptimizer
		 * @brief 轨迹优化算法接口 (抽象基类)。
		 *
		 * 负责将几何路径或粗略轨迹优化为平滑、动态可行且避开障碍物的最终轨迹。
		 * 输出是带有精确时间戳、速度和姿态的 RouteSegment。
		 * 现在应继承自 AlgorithmObject (间接通过具体实现类)。
		 */
		class ITrajectoryOptimizer {
		public:
			/** @brief 默认构造函数 */
			explicit ITrajectoryOptimizer(){}

			/** @brief 虚析构函数。 */
			virtual ~ITrajectoryOptimizer() = default;

			// --- 禁止拷贝和移动 ---
			ITrajectoryOptimizer(const ITrajectoryOptimizer&) = delete;
			ITrajectoryOptimizer& operator=(const ITrajectoryOptimizer&) = delete;
			ITrajectoryOptimizer(ITrajectoryOptimizer&&) = delete;
			ITrajectoryOptimizer& operator=(ITrajectoryOptimizer&&) = delete;
		public:
			/**
			 * @brief (纯虚函数) 优化轨迹段。
			 * @param initial_segment 初始轨迹段 (带有粗略或恒定速度时间戳)。
			 * @param dynamics 相关无人机的动力学模型引用 (const)。
			 * @param strategies (可选) 应用于此轨迹段的策略集合。
			 * @return 优化后的轨迹段 (RouteSegment)。如果优化失败或不可行，应返回空段或原始段。
			 * @note 实现应使用内部存储的 environment_ 成员。
			 */
			virtual RouteSegment optimizer(
				const RouteSegment& initial_segment,
				const NSUav::IDynamicModel& dynamics,
				const NSMission::ITaskStrategyMap& strategies = NSMission::ITaskStrategyMap{}) = 0;

			/**
			 * @brief (纯虚函数) 检查轨迹段是否满足动力学和环境约束。
			 * @param trajectory 要检查的轨迹段。
			 * @param dynamics 相关无人机的动力学模型引用 (const)。
			 * @param strategies (可选) 应用于此轨迹段的策略集合。
			 * @return 如果轨迹可行，返回 true。
			 * @note 实现应使用内部存储的 environment_ 成员。
			 */
			virtual bool isFeasible(
				const RouteSegment& trajectory,
				const NSUav::IDynamicModel& dynamics,
				const NSMission::ITaskStrategyMap& strategies = NSMission::ITaskStrategyMap{}) const = 0;

			/**
			 * @brief (纯虚函数) 设置优化器的参数。
			 *        **注意:** 参数主要通过 initialize 方法传递。此方法可能已过时或用于特定场景。
			 */
			virtual void setParameters(const std::map<std::string, double>& parameters) = 0;
		};

		using ITrajectoryOptimizerPtr = std::shared_ptr<ITrajectoryOptimizer>;
		using ConstITrajectoryOptimizerPtr = std::shared_ptr<const ITrajectoryOptimizer>;

	} // namespace NSAlgorithm
} // namespace NSDrones