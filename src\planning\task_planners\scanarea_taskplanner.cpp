// src/planning/task_planners/task_planner_scanarea.cpp
#include "planning/task_planners/scanarea_taskplanner.h"
#include "planning/planning_result.h"
#include "planning/planning_types.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/task_strategies.h"
#include "uav/uav.h"
#include "environment/environment.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"
#include "environment/geometry/geometry_manager.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include <vector>
#include <cmath>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <utility>

namespace NSDrones {
	namespace NSPlanning {

		ScanAreaTaskPlanner::ScanAreaTaskPlanner(
			IPathPlannerPtr path_planner,
			ITrajectoryOptimizerPtr traj_optimizer)
			: ITaskPlanner(path_planner, traj_optimizer) {}

		// 初始化方法实现
		bool ScanAreaTaskPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) {
			LOG_DEBUG("[ScanAreaTaskPlanner] 开始初始化扫描区域任务规划器");

			if (params) {
				// 加载扫描区域特定参数
				scan_altitude_ = params->getValueOrDefault<double>("scan.altitude", 50.0);
				scan_speed_ = params->getValueOrDefault<double>("scan.speed", 10.0);
				overlap_ratio_ = params->getValueOrDefault<double>("scan.overlap_ratio", 0.3);

				// 使用正确的枚举获取方法处理 task_planner.scan.pattern 参数
				auto pattern_opt = params->getEnumValue<ScanPatternType>("task_planner.scan.pattern");
				if (!pattern_opt.has_value()) {
					LOG_WARN("[ScanAreaTaskPlanner] 无法获取 task_planner.scan.pattern 枚举值，使用默认值 ZIGZAG。");
					pattern_opt = ScanPatternType::ZIGZAG;
				}
				scan_pattern_ = pattern_opt.value();
				std::string pattern_name = NSUtils::enumToString(scan_pattern_);

				LOG_INFO("[ScanAreaTaskPlanner] 加载参数: 扫描高度={:.1f}m, 速度={:.1f}m/s, 重叠率={:.2f}, 模式={}",
						scan_altitude_, scan_speed_, overlap_ratio_, pattern_name);
			}

			// 从原始配置中加载额外参数
			if (!raw_config.empty()) {
				if (raw_config.contains("camera_fov")) {
					camera_fov_ = raw_config["camera_fov"].get<double>();
					LOG_DEBUG("[ScanAreaTaskPlanner] 相机视场角: {:.1f}度", camera_fov_);
				}
			}

			LOG_DEBUG("[ScanAreaTaskPlanner] 初始化完成");
			return true;
		}

		/**
		 * @brief 规划 SCAN_AREA 任务，支持扫描任意（倾斜）平面。
		 */
		PlanningResult ScanAreaTaskPlanner::planTask(
			const NSMission::Task& task,
			const std::vector<NSUav::UavPtr>& assigned_uavs,
			const std::map<ObjectID, NSUav::UavState>& start_states)
		{
			LOG_INFO("[ScanAreaTaskPlanner] 任务 [{}] 开始规划 (支持斜面)...", task.getId());
			PlanningResult result;
			result.setStatus(true);

			// --- 1. 验证任务类型和基本参数 ---
			if (task.getType() != TaskType::SCAN_AREA) {
				result.setStatus(false, "内部错误：ScanAreaTaskPlanner 接收到非 SCAN_AREA 类型的任务 (" + NSUtils::enumToString(task.getType()) + ")");
				LOG_ERROR("[ScanAreaTaskPlanner] 任务 [{}] 类型错误: {}", task.getId(), result.getMessage());
				return result;
			}
			if (assigned_uavs.empty()) {
				result.setStatus(false, "无无人机分配给 ScanAreaTask [" + task.getId() + "]");
				LOG_ERROR("[ScanAreaTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}
			LOG_INFO("[ScanAreaTaskPlanner] 任务 [{}] 分配给 {} 架无人机...", task.getId(), assigned_uavs.size());

			// --- 2. 获取并验证详细参数 ---
			auto params_ptr = task.getTaskParameters<NSMission::ScanAreaTaskParams>();
			if (!params_ptr) {
				result.setStatus(false, "无法获取 ScanAreaTask [" + task.getId() + "] 的有效参数结构体。");
				LOG_ERROR("[ScanAreaTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}
			// 验证区域边界点数量
			if (params_ptr->area_boundary.size() < 3) {
				result.setStatus(false, "扫描区域 [" + task.getId() + "] 边界顶点数 (" + std::to_string(params_ptr->area_boundary.size()) + ") 少于 3。无法构成区域。");
				LOG_ERROR("[ScanAreaTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}
			// 验证条带宽度
			if (params_ptr->strip_width <= Constants::GEOMETRY_EPSILON) {
				result.setStatus(false, "扫描区域 [" + task.getId() + "] 的条带宽度 ({:.3f}) 必须为正。" + std::to_string(params_ptr->strip_width));
				LOG_ERROR("[ScanAreaTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}
			// 验证重叠率
			if (params_ptr->overlap_ratio < 0.0 || params_ptr->overlap_ratio >= 1.0) {
				result.setStatus(false, "扫描区域 [" + task.getId() + "] 的扫描重叠率 ({:.3f}) 必须在 [0, 1) 范围内。" + std::to_string(params_ptr->overlap_ratio));
				LOG_ERROR("[ScanAreaTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}
			LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}] 参数: 边界点数={}, 条带宽度={:.2f}, 重叠率={:.2f}, 扫描角度={:.1f}°",
				task.getId(), params_ptr->area_boundary.size(), params_ptr->strip_width,
				params_ptr->overlap_ratio, params_ptr->scan_angle_deg);
			for (size_t i = 0; i < std::min(params_ptr->area_boundary.size(), (size_t)3); ++i) {
				LOG_TRACE("[ScanAreaTaskPlanner] 任务 [{}]: 边界点 #{}: ({})", task.getId(), i,	params_ptr->area_boundary[i].toString());
			}

			// --- 3. 检查依赖项 ---
			auto environment = getEnvironment();
			if (!environment || !path_planner_) {
				result.setStatus(false, "内部错误：ScanAreaTaskPlanner 缺少必要的依赖项 (环境/路径规划器)。");
				LOG_ERROR("[ScanAreaTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}

			// --- 4. 验证输入边界点是否共面 --- (关键步骤)
			LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}]: 步骤 1: 检查边界点共面性...", task.getId());
			double max_deviation = 0.0;
			// 使用 GeometryManager 检查点是否共面
			auto geometry_manager = environment->getGeometryManager();
			if (!geometry_manager || !geometry_manager->checkPointsCoplanar(params_ptr->area_boundary, max_deviation)) {
				std::ostringstream oss_msg;
				oss_msg << "扫描区域 [" << task.getId() << "]: 输入的边界点 (" << params_ptr->area_boundary.size()
					<< "个) 不共面。最大偏差: " << std::fixed << std::setprecision(4) << max_deviation
					<< " 米。无法规划斜面扫描。";
				std::string msg = oss_msg.str();
				result.setStatus(false, msg);
				LOG_ERROR("[ScanAreaTaskPlanner] {}", msg);
				return result;
			}
			LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}]: 边界点共面性检查通过 (最大偏差: {:.4f} 米)。", task.getId(), max_deviation);


			// --- 5. 生成扫描路径几何点 --- (调用能够处理三维边界和离面高度的几何工具函数)
			LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}]: 步骤 2: 生成扫描几何路径 (斜面)...", task.getId());
			// 获取离面扫描高度，如果未指定则默认为 0
			double height_above_plane = params_ptr->scan_height_above_plane.value_or(0.0);
			LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}]: 指定离面扫描高度: {:.2f} 米。", task.getId(), height_above_plane);

			// 使用 GeometryManager 生成扫描路径
			std::vector<WGS84Point> scan_path_wgs84 = geometry_manager->generateScanPath(
				params_ptr->area_boundary,
				params_ptr->strip_width,
				params_ptr->overlap_ratio,
				params_ptr->scan_angle_deg,
				height_above_plane
			);

			// 将 WGS84 路径转换为 ECEF 坐标进行几何运算
			std::vector<EcefPoint> scan_path_ecef;
			scan_path_ecef.reserve(scan_path_wgs84.size());

			for (const auto& wgs84_point : scan_path_wgs84) {
				EcefPoint ecef_point = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_point);
				scan_path_ecef.push_back(ecef_point);
			}

			if (scan_path_ecef.size() < 2) {
				// 如果生成的点少于2个，无法构成路径
				result.setStatus(false, "未能为扫描区域 [" + task.getId() + "] 生成有效的几何路径（可能是区域过小、参数设置问题或内部错误）。");
				LOG_ERROR("[ScanAreaTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}
			LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}]: 生成扫描几何路径 {} 个点。", task.getId(), scan_path_ecef.size());
			if (!scan_path_ecef.empty()) {
				LOG_TRACE("[ScanAreaTaskPlanner] 任务 [{}]: 生成路径首点: ECEF({:.1f},{:.1f},{:.1f}), 末点: ECEF({:.1f},{:.1f},{:.1f})", task.getId(),
					scan_path_ecef.front().x(), scan_path_ecef.front().y(), scan_path_ecef.front().z(),
					scan_path_ecef.back().x(), scan_path_ecef.back().y(), scan_path_ecef.back().z());
			}

			// --- 6. 为每个无人机规划完整路径 ---
			bool overall_success = true;
			for (const auto& uav : assigned_uavs) {
				if (!uav) {
					LOG_WARN("[ScanAreaTaskPlanner] 任务 [{}]: 跳过空的无人机指针。", task.getId());
					continue;
				}
				const ObjectID& uav_id = uav->getId();
				LOG_INFO("[ScanAreaTaskPlanner] 任务 [{}]: 开始为无人机 [{}] 规划扫描路径...", task.getId(), uav_id);

				auto start_state_it = start_states.find(uav_id);
				if (start_state_it == start_states.end()) {
					std::string msg = "缺少无人机 [" + uav_id + "] 的起始状态。";
					result.addWarning(WarningEvent{ WarningType::INVALID_STATE, msg, 0.0, {}, uav_id, task.getId() });
					LOG_ERROR("[ScanAreaTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					overall_success = false;
					LOG_INFO("[ScanAreaTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (缺少状态)。", task.getId(), uav_id);
					continue;
				}
				const NSUav::UavState& start_state = start_state_it->second;
				const NSUav::IDynamicModel* dynamics = uav->getDynamicsModel().get();
				if (!dynamics) {
					std::string msg = "无人机 [" + uav_id + "] 缺少有效的动力学模型。";
					result.addWarning(WarningEvent{ WarningType::INVALID_STATE, msg, 0.0, {}, uav_id, task.getId() });
					LOG_ERROR("[ScanAreaTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					overall_success = false;
					LOG_INFO("[ScanAreaTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (缺少动力学模型)。", task.getId(), uav_id);
					continue;
				}
				LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}], 无人机 [{}]: 起始状态 Pos=({}), Time={:.3f}",
					task.getId(), uav_id, start_state.position.toString(), start_state.time_stamp);

				// --- 6.1 规划进入扫描起点的路径 ---
				LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 3: 规划进入扫描起点的路径...", task.getId(), uav_id);
				EcefPoint scan_start_ecef = scan_path_geom.front(); // 扫描几何路径的第一个点
				// 转换为WGS84坐标用于路径规划接口
				WGS84Point scan_start_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(scan_start_ecef);
				std::vector<EcefPoint> entry_geom_path;
				auto path_constraints_opt = task.getStrategyByType<NSMission::PathConstraintStrategy>();
				const NSMission::PathConstraintStrategy* path_constraints_ptr = path_constraints_opt ? path_constraints_opt.get() : nullptr;
				LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}], 无人机 [{}]: 调用路径规划器: 从 {} 到 {}",
					task.getId(), uav_id, start_state.position.toString(), scan_start_wgs84.toString());
				bool entry_found = path_planner_->findPath(start_state.position, scan_start_wgs84, dynamics, path_constraints_ptr, entry_geom_path);
				if (!entry_found || entry_geom_path.size() < 2) {
					std::ostringstream oss_msg;
					oss_msg << "未能找到无人机 [" << uav_id << "] 进入扫描区域起点 "
						<< scan_start_wgs84.toString() << " 的路径。";
					std::string msg_fmt = oss_msg.str();
					result.addWarning(WarningEvent{ WarningType::PLANNING_FAILURE, msg_fmt, start_state.time_stamp, start_state.position, uav_id, task.getId() });
					LOG_ERROR("[ScanAreaTaskPlanner] 任务 [{}]: {}", task.getId(), msg_fmt);
					overall_success = false;
					LOG_INFO("[ScanAreaTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (无法规划进入路径)。", task.getId(), uav_id);
					continue;
				}
				LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}], 无人机 [{}]: 进入路径找到 {} 个点。", task.getId(), uav_id, entry_geom_path.size());

				// --- 6.2 合并进入路径和扫描路径 ---
				LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 4: 合并进入路径和扫描路径...", task.getId(), uav_id);
				std::vector<EcefPoint> full_geom_path = entry_geom_path;
				// 检查并移除可能的重合点
				if (!full_geom_path.empty() && (full_geom_path.back() - scan_path_geom.front()).norm() < Constants::GEOMETRY_EPSILON) {
					LOG_TRACE("[ScanAreaTaskPlanner] 任务 [{}], 无人机 [{}]: 合并几何路径：移除入口路径终点与扫描路径起点的重合点。", task.getId(), uav_id);
					full_geom_path.pop_back();
				}
				full_geom_path.insert(full_geom_path.end(), scan_path_geom.begin(), scan_path_geom.end());
				LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}], 无人机 [{}]: 合并后完整几何路径 {} 个点。", task.getId(), uav_id, full_geom_path.size());

				// --- 6.3 平滑和时间参数化 ---
				LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 5: 平滑和时间参数化完整路径...", task.getId(), uav_id);
				RouteSegment final_segment;
				double desired_speed = task.getDesiredSpeed(7.0); // 获取任务期望速度
				if (full_geom_path.size() < 2) {
					std::string msg = "合并后的几何路径点数不足 (UAV: " + uav_id + ")，无法进行平滑和参数化。";
					LOG_ERROR("[ScanAreaTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					result.addWarning(WarningEvent{ WarningType::PLANNING_FAILURE, msg, start_state.time_stamp, start_state.position, uav_id, task.getId() });
					overall_success = false;
					LOG_INFO("[ScanAreaTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (几何路径点数不足)。", task.getId(), uav_id);
					continue;
				}
				LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}], 无人机 [{}]: 开始平滑和时间参数化, 速度 {:.1f} m/s, {} 个几何点...",
					task.getId(), uav_id, desired_speed, full_geom_path.size());
				if (!smoothAndTimeParameterizeECEF(full_geom_path, uav, start_state, desired_speed, final_segment, &result, task.getStrategies())) {
					LOG_ERROR("[ScanAreaTaskPlanner] 任务 [{}], 无人机 [{}] 扫描路径处理失败 (平滑/参数化过程出错)。", task.getId(), uav_id);
					overall_success = false;
					LOG_INFO("[ScanAreaTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (平滑/参数化过程出错)。", task.getId(), uav_id);
					continue;
				}
				LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}], 无人机 [{}]: 平滑和时间参数化完成，生成 {} 个航点。", task.getId(), uav_id, final_segment.size());

				// --- 6.4 添加结果 ---
				LOG_DEBUG("[ScanAreaTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 6: 添加最终航线到结果...", task.getId(), uav_id);
				if (final_segment.empty()) {
					std::string msg = "无人机 [" + uav_id + "] 规划流程完成，但最终生成的航段为空。";
					LOG_WARN("[ScanAreaTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					result.addWarning(WarningEvent{ WarningType::PLANNING_FAILURE, msg, 0, {}, uav_id, task.getId() });
				}
				else {
					PlannedRoute temp_route_for_warning(uav_id);
					temp_route_for_warning.addWaypoints(final_segment);
					checkSegmentWarnings(temp_route_for_warning, uav_id, result, task.getId());
					PlannedRoute route(uav_id);
					route.addWaypoints(final_segment);
					result.addRoute(std::move(route));
					LOG_INFO("[ScanAreaTaskPlanner] 任务 [{}]: 为无人机 [{}] 生成扫描航线成功，{} 个点。", task.getId(), uav_id, final_segment.size());
				}
			}

			// --- 7. 设置最终状态 ---
			if (!overall_success) {
				if (result.wasSuccessful()) { result.setStatus(false, "部分或全部无人机的扫描任务规划失败。"); }
				LOG_ERROR("[ScanAreaTaskPlanner] 任务 [{}] 规划存在失败情况。", task.getId());
			}
			else {
				LOG_INFO("[ScanAreaTaskPlanner] 任务 [{}] 所有分配的无人机规划流程完成。", task.getId());
				if (result.getAllRoutes().empty() && !assigned_uavs.empty()) {
					LOG_WARN("[ScanAreaTaskPlanner] 任务 [{}]: 规划流程成功完成，但最终未生成任何有效航线。", task.getId());
				}
				else if (result.getAllRoutes().size() != assigned_uavs.size()) {
					LOG_WARN("[ScanAreaTaskPlanner] 任务 [{}]: 最终生成的航线数量 ({}) 与成功完成规划的无人机数量 ({}) 不完全匹配。", task.getId(), result.getAllRoutes().size(), assigned_uavs.size());
				}
				else {
					LOG_INFO("[ScanAreaTaskPlanner] 任务 [{}] 规划成功完成，为所有 {} 架无人机生成了航线。", task.getId(), result.getAllRoutes().size());
				}
			}
			LOG_INFO("[ScanAreaTaskPlanner] 任务 [{}] 规划结束，最终状态: {}", task.getId(), result.wasSuccessful() ? "成功" : "失败");
			return result;
		}

	} // namespace NSPlanning
} // namespace NSDrones