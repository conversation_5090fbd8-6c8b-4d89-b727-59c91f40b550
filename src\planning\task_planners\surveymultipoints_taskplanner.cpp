// src/planning/task_planners/task_planner_surveymultipoints.cpp
#include "planning/task_planners/surveymultipoints_taskplanner.h"
#include "planning/planning_result.h"
#include "planning/planning_types.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/task_strategies.h"
#include "mission/control_point.h"
#include "uav/uav.h"
#include "environment/environment.h"
#include "environment/coordinate/coordinate_converter.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"
#include "environment/environment.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include <vector>
#include <map>
#include <string>
#include <algorithm>
#include <utility>
#include <numeric>

namespace NSDrones {
	namespace NSPlanning {

		SurveyMultiPointsTaskPlanner::SurveyMultiPointsTaskPlanner(
			IPathPlannerPtr path_planner,
			ITrajectoryOptimizerPtr traj_optimizer)
			: ITaskPlanner(path_planner, traj_optimizer) {}

		// 新增: 初始化方法实现 (目前为空)
		bool SurveyMultiPointsTaskPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) {
			// TODO: 如果 SurveyMultiPointsTaskPlanner 未来有自己的配置参数，在此处加载和处理
			LOG_DEBUG("[SurveyMultiPointsTaskPlanner] 初始化完成 (无特定配置)。");
			return true;
		}

		PlanningResult SurveyMultiPointsTaskPlanner::planTask(
			const NSMission::Task& task,
			const std::vector<NSUav::UavPtr>& assigned_uavs,
			const std::map<ObjectID, NSUav::UavState>& start_states)
		{
			PlanningResult result;
			result.setStatus(true);

			if (task.getType() != TaskType::SURVEY_MULTIPOINTS) {
				result.setStatus(false, "内部错误：SurveyMultiPointsTaskPlanner 接收到非 SURVEY_MULTI_POINTS 类型的任务 (" + NSUtils::enumToString(task.getType()) + ")");
				LOG_ERROR("[SurveyMultiPointsTaskPlanner] {}", result.getMessage()); return result;
			}
			if (assigned_uavs.empty()) {
				result.setStatus(false, "无无人机分配给 SurveyMultiPointsTask [" + task.getId() + "]");
				LOG_ERROR("[SurveyMultiPointsTaskPlanner] {}", result.getMessage()); return result;
			}
			auto params_ptr = task.getTaskParameters<NSMission::SurveyMultiPointsTaskParams>();
			if (!params_ptr) {
				result.setStatus(false, "无法获取 SurveyMultiPointsTask [" + task.getId() + "] 的有效参数结构体。");
				LOG_ERROR("[SurveyMultiPointsTaskPlanner] {}", result.getMessage()); return result;
			}
			if (params_ptr->survey_points.empty()) {
				result.setStatus(false, "多点勘察任务 [" + task.getId() + "] 的勘察点列表为空。");
				LOG_ERROR("[SurveyMultiPointsTaskPlanner] {}", result.getMessage()); return result;
			}

			LOG_INFO("[SurveyMultiPointsTaskPlanner] 开始规划 SurveyMultiPointsTask [{}], 分配给 {} 架无人机...", task.getId(), assigned_uavs.size());

			// --- 检查依赖项 ---
			auto environment = getEnvironment();
			if (!environment || !path_planner_) {
				result.setStatus(false, "内部错误：SurveyMultiPointsTaskPlanner 缺少必要的依赖项。");
				LOG_ERROR("[SurveyMultiPointsTaskPlanner] {}", result.getMessage()); return result;
			}

			// --- 为每个无人机规划路径 --- (假设单机或多机独立，且每个无人机访问所有点)
			// TODO: 多无人机协同勘察多点需要更复杂的分配逻辑，这里简化为每个都访问
			bool overall_success = true;
			for (const auto& uav : assigned_uavs) {
				if (!uav) {
					LOG_WARN("  跳过空无人机指针。");
					continue;
				}
				const ObjectID& uav_id = uav->getId();
				auto start_state_it = start_states.find(uav_id);
				if (start_state_it == start_states.end()) {
					std::string msg = "缺少无人机 [" + uav_id + "] 的起始状态。";
					result.addWarning(WarningEvent{ WarningType::INVALID_STATE, msg, 0.0, {}, uav_id, task.getId() });
					LOG_ERROR("[SurveyMultiPointsTaskPlanner] {}", msg);
					overall_success = false;
					continue;
				}
				const NSUav::UavState& start_state = start_state_it->second;
				const NSUav::IDynamicModel* dynamics = uav->getDynamicsModel().get();
				if (!dynamics) {
					std::string msg = "缺少无人机 [" + uav_id + "] 的动力学模型。";
					result.addWarning(WarningEvent{ WarningType::INVALID_STATE, msg, 0.0, {}, uav_id, task.getId() });
					LOG_ERROR("[SurveyMultiPointsTaskPlanner] {}", msg);
					overall_success = false;
					continue;
				}

				LOG_INFO("    > 开始为无人机 [{}] 规划多点勘察路径...", uav_id);

				RouteSegment total_route_segment; // 存储当前无人机的完整航线
				NSUav::UavState last_wp_state = start_state; // 跟踪上一个航路点的状态
				bool uav_plan_success = true;

				// --- 依次规划到每个勘察点 --- (按参数列表顺序)
				for (size_t i = 0; i < params_ptr->survey_points.size(); ++i) {
					const NSMission::ControlPoint& cp = params_ptr->survey_points[i];
					LOG_DEBUG("      规划到勘察点 #{} (WGS84=({}))...", i + 1, cp.position.toString());

					// 获取目标点的ECEF位置（内部几何计算使用ECEF坐标系）
					WGS84Point target_wgs84 = cp.position;
					EcefPoint target_ecef = CoordinateConverter::wgs84ToECEF(target_wgs84);
					LOG_DEBUG("        目标ECEF位置: {}", target_ecef.toString());

					// 规划几何路径（使用ECEF坐标系进行内部计算）
					std::vector<EcefPoint> geometric_path;
					auto path_constraints_opt = task.getStrategyByType<NSMission::PathConstraintStrategy>();
					const NSMission::PathConstraintStrategy* path_constraints_ptr = path_constraints_opt ? path_constraints_opt.get() : nullptr;

					// 将起始位置转换为ECEF坐标
					EcefPoint start_ecef = CoordinateConverter::wgs84ToECEF(last_wp_state.position);
					LOG_DEBUG("        调用路径规划器: {} -> {}",
						start_ecef.toString(), target_ecef.toString());
					bool path_found = path_planner_->findPath(start_ecef, target_ecef, dynamics, path_constraints_ptr, geometric_path);
					if (!path_found || geometric_path.size() < 2) {
						std::string msg = "未能找到无人机 [" + uav_id + "] 前往勘察点 #" + std::to_string(i + 1) + " 的路径。";
						result.addWarning(WarningEvent{ WarningType::PLANNING_FAILURE, msg, last_wp_state.time_stamp, last_wp_state.position, uav_id, task.getId() });
						LOG_ERROR("[SurveyMultiPointsTaskPlanner] {}", msg);
						uav_plan_success = false;
						break;
					}
					LOG_DEBUG("        几何路径找到 {} 个点。", geometric_path.size());

					// 平滑和时间参数化（使用ECEF坐标系）
					RouteSegment segment_to_add;
					double desired_speed = cp.hasSpeedRequirement() ? cp.getRequiredSpeedOrZero().norm() : task.getDesiredSpeed();
					if (desired_speed < Constants::VELOCITY_EPSILON) desired_speed = 8.0;
					if (!smoothAndTimeParameterizeECEF(geometric_path, uav, last_wp_state, desired_speed, segment_to_add, &result, task.getStrategies())) {
						LOG_ERROR("无人机 [{}] 路径处理失败 (勘察点 #{})。", uav_id, i + 1);
						uav_plan_success = false;
						break;
					}
					LOG_DEBUG("        路径段平滑和参数化完成，生成 {} 个点。", segment_to_add.size());

					// 处理载荷动作
					if (cp.hasPayloadAction() && cp.action && !segment_to_add.empty()) {
						auto cmd_opt = cp.action->getCommand();
						if (cmd_opt) {
							LOG_DEBUG("        在勘察点 #{} 添加载荷动作: {}", i + 1, cmd_opt->command_name);
							segment_to_add.back().payload_actions.push_back(*cmd_opt);
						}
					}

					// 合并航段 (与 FollowPath 类似)
					if (!total_route_segment.empty() && !segment_to_add.empty()) {
						if ((total_route_segment.back().position - segment_to_add.front().position).norm() < Constants::GEOMETRY_EPSILON &&
							std::abs(total_route_segment.back().time_stamp - segment_to_add.front().time_stamp) < Constants::TIME_EPSILON) {
							if (!segment_to_add.front().payload_actions.empty()) {
								total_route_segment.back().payload_actions.insert(total_route_segment.back().payload_actions.end(),
									segment_to_add.front().payload_actions.begin(),
									segment_to_add.front().payload_actions.end());
							}
							segment_to_add.erase(segment_to_add.begin());
						}
						else if (segment_to_add.front().time_stamp < total_route_segment.back().time_stamp - Constants::TIME_EPSILON) {
							Time time_offset = total_route_segment.back().time_stamp - segment_to_add.front().time_stamp + Constants::TIME_EPSILON;
							for (auto& rp : segment_to_add) { rp.time_stamp += time_offset; }
						}
					}

					// 添加到总路径
					if (!segment_to_add.empty()) {
						total_route_segment.insert(total_route_segment.end(), segment_to_add.begin(), segment_to_add.end());
						// 修正 checkSegmentWarnings 调用
						PlannedRoute temp_route_for_check(uav_id);
						temp_route_for_check.addWaypoints(segment_to_add);
						checkSegmentWarnings(temp_route_for_check, uav_id, result, task.getId());
						last_wp_state = NSUav::stateFromRoutePt(total_route_segment.back());
					}

					// 处理控制点的停留时间
					if (cp.hasLoiterTime() && !total_route_segment.empty()) {
						RoutePoint loiter_end = total_route_segment.back();
						Time loiter_duration = cp.required_loiter_time;
						loiter_end.time_stamp += loiter_duration;
						loiter_end.velocity = Vector3D::Zero();
						loiter_end.payload_actions.clear();
						total_route_segment.push_back(loiter_end);
						last_wp_state = NSUav::stateFromRoutePt(loiter_end);
						LOG_DEBUG("        在勘察点 #{} 停留 {:.2f} 秒。", i + 1, loiter_duration);
					}
				}

				// --- 添加当前无人机的最终结果 ---
				if (uav_plan_success && !total_route_segment.empty()) {
					PlannedRoute route(uav_id);
					route.addWaypoints(total_route_segment);
					result.addRoute(std::move(route));
					LOG_INFO("      为无人机 [{}] 生成多点勘察航线成功，{} 个点。", uav_id, total_route_segment.size());
				}
				else if (uav_plan_success) {
					std::string msg = "成功为无人机 [" + uav_id + "] 完成规划，但未生成有效航线。";
					result.addWarning(WarningEvent{ WarningType::PLANNING_FAILURE, msg, 0.0, {}, uav_id, task.getId() });
					LOG_WARN("[SurveyMultiPointsTaskPlanner] {}", msg);
				}
				else {
					LOG_ERROR("无人机 [{}] 多点勘察规划失败。", uav_id);
					overall_success = false;
				}
			}

			// --- 设置最终状态 ---
			if (!overall_success) {
				if (result.wasSuccessful()) { result.setStatus(false, "部分无人机的多点勘察任务规划失败。"); }
				LOG_ERROR("[SurveyMultiPointsTaskPlanner] SurveyMultiPointsTask [{}] 规划存在失败情况。", task.getId());
			}
			else {
				LOG_INFO("[SurveyMultiPointsTaskPlanner] SurveyMultiPointsTask [{}] 规划完成。", task.getId());
				// 检查航线数量
			}
			return result;
		}

	} // namespace NSPlanning
} // namespace NSDrones