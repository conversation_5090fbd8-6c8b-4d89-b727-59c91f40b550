// include/planning/mission_planner.h
#pragma once

#include "core/types.h"
#include "planning/itask_planner.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "algorithm/evaluator/itrajectory_evaluator.h"
#include "algorithm/allocator/itask_allocator.h"
#include "planning/planning_result.h"
#include "mission/mission.h"
#include "uav/uav.h"
#include <vector>
#include <map>
#include <memory>
#include <mutex>
#include <shared_mutex>

namespace NSDrones {
	namespace NSMission { class Task; class ControlPoint;}
	namespace NSEnvironment {
		class Environment;
		template<typename ObjectContainer> class CollisionEngine;
	}
}

namespace NSDrones {
	namespace NSPlanning {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class MissionPlanner
		 * @brief 顶层的任务规划协调器。
		 *
		 * 负责接收完整的 Mission，调用任务分配器分配无人机，根据任务类型分发给相应的 TaskPlanner，
		 * 并将各个任务的规划结果连接起来，生成最终的完整航线。
		 * 管理可用的无人机资源和规划组件。
		 * **线程安全说明:** 对可用无人机列表的访问 (`getAvailableUAVs`, `addAvailableUAV`, `removeAvailableUAV`)
		 * 是线程安全的。但是，规划过程 (`planMission`, `planSingleTask`) 本身不是线程安全的，不应并发调用。
		 */
		class MissionPlanner {
		public:
			/**
			 * @brief 构造函数。
			 * @param path_planner 默认的路径规划器。
			 * @param traj_optimizer 默认的轨迹优化器 (可以为 nullptr)。
			 * @param evaluator (可选) 轨迹评估器。
			 * @param allocator (可选) 任务分配器。如果为空，将使用内部的简单分配逻辑。
			 * @throws DroneException 如果必要组件 (path_planner) 为空或环境未初始化。
			 * @note 碰撞引擎将从环境中自动获取，无需手动传入。
			 */
			MissionPlanner(IPathPlannerPtr path_planner,
				ITrajectoryOptimizerPtr traj_optimizer,
				ITrajectoryEvaluatorPtr evaluator = nullptr,
				ITaskAllocatorPtr allocator = nullptr);

			// --- 禁止拷贝和移动 ---
			MissionPlanner(const MissionPlanner&) = delete;
			MissionPlanner& operator=(const MissionPlanner&) = delete;
			MissionPlanner(MissionPlanner&&) = delete;
			MissionPlanner& operator=(MissionPlanner&&) = delete;

			/**
			 * @brief 注册一个特定任务类型的规划器实现。
			 *        如果已存在相同类型的规划器，则会覆盖。
			 * @param task_type 要处理的任务类型。
			 * @param planner 能够处理该类型任务的 TaskPlanner 实例的共享指针。
			 */
			void registerTaskPlanner(TaskType task_type, TaskPlannerPtr planner);

			/**
			 * @brief 获取所有可用的无人机列表。
			 * @return 当前可用无人机列表的副本 (线程安全)。
			 */
			std::vector<NSUav::UavPtr> getAvailableUAVs() const;

			/**
			 * @brief 添加一个可用的无人机资源。
			 * @param uav 指向无人机实例的共享指针。如果 UAV 已存在则忽略。线程安全。
			 */
			void addAvailableUAV(NSUav::UavPtr uav);

			/**
			 * @brief 移除一个无人机资源。
			 * @param uav_id 要移除的无人机 ID。线程安全。
			 * @return 如果成功找到并移除返回 true。
			 */
			bool removeAvailableUAV(const ObjectID& uav_id);

			/**
			 * @brief 规划整个任务计划 (Mission)。
			 *        按顺序处理任务，为每个任务选择无人机并调用相应的 TaskPlanner。
			 *        合并所有结果生成最终航线。
			 *        **如果配置了评估器，会对最终航线进行评估，如果评估失败则整体规划失败。**
			 *        **注意：** 此方法非线程安全，不应并发调用。
			 * @param mission 要规划的任务计划对象。
			 * @return 包含所有无人机完整航线和告警的 PlanningResult。
			 */
			PlanningResult planMission(const NSMission::Mission& mission);

			/**
			 * @brief 规划单个任务 (主要用于测试或特殊场景)。
			 *        会尝试从当前可用无人机中选择执行者。
			 *        **如果配置了评估器，会对生成的航线进行评估。**
			 *        **注意：** 此方法非线程安全。
			 * @param task 要规划的单个任务。
			 * @return 包含该任务涉及的无人机航线和告警的 PlanningResult。
			 */
			PlanningResult planSingleTask(const NSMission::Task& task);

			/** @brief 设置默认的轨迹评估器。 */
			void setEvaluator(ITrajectoryEvaluatorPtr evaluator) { trajectory_evaluator_ = evaluator; }
			/** @brief 设置任务分配器。 */
			void setAllocator(ITaskAllocatorPtr allocator) { task_allocator_ = allocator; }

		private:
			/**
			 * @brief 根据任务类型查找合适的 TaskPlanner。
			 *        此方法是 const，不修改内部状态。
			 * @param task 要规划的任务。
			 * @return 指向合适 TaskPlanner 的共享指针，如果找不到则返回 nullptr。
			 */
			TaskPlannerPtr findPlannerForTask(const NSMission::Task& task) const;

			// **添加：私有辅助函数声明**
			void loadParams();
			TrajectoryCost evaluateTaskCost(const NSMission::Task& task,
				const NSUav::UavPtr& uav,
				const PlannedRoute& route,
				PlanningResult& result);

			/**
			 * @brief 计算无人机执行特定任务的适合度分数
			 * @param uav 无人机指针
			 * @param task 任务对象
			 * @param uav_state 无人机当前状态
			 * @return 适合度分数，越高越适合
			 */
			double calculateUavTaskFitness(const NSUav::UavPtr& uav,
										   const NSMission::Task& task,
										   const NSUav::UavState& uav_state) const;

			/**
			 * @brief 从任务中提取位置信息
			 * @param task 任务对象
			 * @return 任务位置，如果无法确定则返回空
			 */
			std::optional<WGS84Point> getTaskPosition(const NSMission::Task& task) const;
			/**
			 * @brief 获取任务的载荷要求
			 * @param task 任务对象
			 * @return 载荷要求值，如果没有要求则返回空
			 */
			std::optional<double> getTaskPayloadRequirement(const NSMission::Task& task) const;

			// --- 环境访问 ---
			/**
			 * @brief 获取环境实例
			 * @return 环境实例的共享指针
			 */
			std::shared_ptr<Environment> getEnvironment() const;

			// --- 核心组件 ---
			IPathPlannerPtr default_path_planner_;             // 默认路径规划器
			ITrajectoryOptimizerPtr default_traj_optimizer_;   // 默认轨迹优化器 (可选)
			ITrajectoryEvaluatorPtr trajectory_evaluator_;     // 轨迹评估器 (可选)
			ITaskAllocatorPtr task_allocator_;                 // 任务分配器 (可选)

			// --- 内部状态 ---
			std::map<TaskType, TaskPlannerPtr> task_planners_; // 任务类型到具体规划器的映射
			std::vector<NSUav::UavPtr> available_uavs_;      // 可用无人机列表
			mutable std::mutex uav_list_mutex_;              // 保护 available_uavs_ 的互斥锁
		};

	} // namespace NSPlanning
} // namespace NSDrones