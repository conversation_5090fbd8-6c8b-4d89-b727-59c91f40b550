#pragma once

#include "environment/maps/igridmap.h"
#include "environment/maps/single_gridmap.h"  // 添加 SingleGridMap 的包含
#include "core/types.h"                 // 包含 WGS84BoundingBox, BoundingBox, WGS84Point, FeatureType 等核心类型
#include "params/parameters.h"          // 添加参数系统
#include "utils/logging.h"
#include "utils/file_utils.h"
#include <nlohmann/json.hpp>            // 添加 JSON 支持
#include <vector>
#include <string>
#include <memory>     // For std::shared_ptr
#include <optional>   // For std::optional
#include <unordered_map>                // 添加 unordered_map

namespace NSDrones {
	namespace NSEnvironment {
		/**
		 * @class TiledGridMap
		 * @brief 管理多个地理配准的地图瓦片 (例如多个GeoTIFF文件)，并作为统一的 IGridMap 提供服务。
		 *
		 * 该类负责加载和组织一系列地图瓦片，每个瓦片都由一个单独的 IGridMap (通常是 SingleGridMap) 表示。
		 * 它能够根据查询的WGS84坐标，将请求路由到正确的瓦片以获取地形高程、地物类型等信息。
		 * 同时，它还计算并提供所有已加载瓦片的组合地理边界。
		 */
		class TiledGridMap : public IGridMap {
		public:
			/**
			 * @struct LayerConfig
			 * @brief 用于配置 TiledGridMap 从瓦片中提取不同类型数据时所使用的图层名称或标识。
			 */
			struct LayerConfig {
				std::string elevationLayer = "elevation";      ///< 高程数据图层的名称/标识。
				std::string featureTypeLayer = "feature_type"; ///< 地物类型数据图层的名称/标识。
				std::string featureHeightLayer = "feature_height";///< 地物高度数据图层的名称/标识。
				double targetResolution = 0.0;             ///< 目标分辨率 (米/像素)。如果为0.0，则尝试使用瓦片的原生分辨率。
			};

			/**
			 * @brief TiledGridMap 的构造函数。
			 * @param config (可选) 图层配置。如果未提供，将使用默认图层名称。
			 */
			explicit TiledGridMap(LayerConfig config = {});

			~TiledGridMap() override;

			/**
			 * @brief 初始化瓦片地图管理器
			 * @param global_params 全局参数对象，用于获取地图相关配置
			 * @return 如果成功初始化则返回 true，否则返回 false
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> global_params) override;

		private:
			/**
			 * @brief 扫描地图数据目录，查找指定格式的地图文件
			 * @param data_directory 数据目录路径
			 * @param load_mode 加载模式（geotiff 或 tiff）
			 * @return 找到的地图文件路径列表
			 */
			std::vector<std::string> scanMapDataDirectory(const std::string& data_directory, const std::string& load_mode);

			/**
			 * @brief 更新合并的元数据
			 */
			void updateCombinedMetadata();

		public:

			// --- 实现 IMapDataSource 接口 ---

			/**
			 * @brief (IGridMap 接口) 获取指定WGS84经纬度位置的地形高程（海拔高度）。
			 *        管理器会将查询路由到覆盖该点的合适瓦片。
			 * @param latitude WGS84纬度 (单位：度)。
			 * @param longitude WGS84经度 (单位：度)。
			 * @return std::optional<double> 如果成功获取就返回海拔高度(单位：米);
			 *         如果查询点超出数据范围或未找到数据，则返回空值。
			 */
			std::optional<double> getElevation(double latitude, double longitude) const override;

			/**
			 * @brief (IGridMap 接口) 获取指定WGS84经纬度位置的地表覆盖特征类型 (地物类型)。
			 *        管理器会将查询路由到覆盖该点的合适瓦片。
			 * @param latitude WGS84纬度 (单位：度)。
			 * @param longitude WGS84经度 (单位：度)。
			 * @return std::optional<FeatureType> 如果成功获取就返回地物类型;
			 *         如果查询点超出数据范围或未找到数据，则返回空值。
			 */
			std::optional<FeatureType> getFeature(double latitude, double longitude) const override;

			/**
			 * @brief (IGridMap 接口) 获取指定WGS84经纬度位置的地表覆盖特征的高度。
			 *        管理器会将查询路由到覆盖该点的合适瓦片。
			 * @param latitude WGS84纬度 (单位：度)。
			 * @param longitude WGS84经度 (单位：度)。
			 * @return std::optional<double> 如果成功获取就返回特征高度(单位：米);
			 *         如果查询点超出数据范围或未找到数据，则返回空值。
			 */
			std::optional<double> getFeatureHeight(double latitude, double longitude) const override;

			/**
			 * @brief (IGridMap 接口) 检查瓦片地图管理器是否已成功初始化（至少一个瓦片已加载）。
			 * @return 如果已初始化则为 true，否则为 false。
			 */
			bool isInitialized() const override;

			// --- TiledGridMap 特有接口 ---
			/**
			 * @brief (旧名: getCombinedDataSourceWGS84Bounds) 获取所有已加载瓦片组合起来的精确WGS84地理总边界。
			 * @return WGS84BoundingBox 表示的组合WGS84边界。
			 *         如果没有任何瓦片或未初始化，返回一个无效的 (isEmpty() == true) WGS84BoundingBox。
			 * @deprecated 已被 getDataSourceWGS84Bounds() 替代，作为 IMapDataSource 接口的一部分。
			 */
			WGS84BoundingBox getCombinedDataSourceWGS84Bounds() const;

			/**
			 * @brief 检查指定的WGS84坐标是否被任何已加载的瓦片覆盖。
			 * @param latitude WGS84纬度 (单位：度)。
			 * @param longitude WGS84经度 (单位：度)。
			 * @return 如果坐标点至少被一个瓦片覆盖，则返回 true；否则返回 false。
			 */
			bool isCovered(double latitude, double longitude) const;

			/**
			 * @brief 获取汇总的地图元数据
			 * @return MapMetadata 包含所有瓦片汇总后的元数据信息
			 */
			MapMetadata getMetadata() const override;

			/**
			 * @brief 获取所有瓦片的元数据列表
			 * @return 瓦片元数据列表，每个元素对应一个瓦片的详细信息
			 */
			std::vector<MapMetadata> getTileMetadataList() const override;

			/**
			 * @brief 获取所有成功加载的瓦片的元数据列表（已弃用，请使用getTileMetadataList）
			 * @return 瓦片元数据列表
			 * @deprecated 请使用getTileMetadataList()方法
			 */
			std::vector<MapMetadata> getTiles() const;


		private:
			WGS84BoundingBox combinedBoundsWGS84_;  ///< 缓存所有已加载瓦片的组合WGS84边界。
			bool initialized_ = false;                      ///< 标记管理器是否已成功初始化。
			LayerConfig layerConfig_;                       ///< 存储图层名称和目标分辨率等配置。

			/// 数据源缓存：map_id -> SingleGridMap 实例
			mutable std::unordered_map<std::string, std::shared_ptr<SingleGridMap>> single_gridmaps_;
			std::shared_ptr<NSParams::ParamValues> globalParams_;  ///< 保存全局参数用于动态加载

			/**
			 * @brief 为单个地图文件创建并初始化一个数据源实例，并获取其元数据。
			 * @param filePath 地图文件的路径。
			 * @param tileId 为此瓦片生成的唯一ID。
			 * @param refOriginWGS84 用于初始化此瓦片对应地图的WGS84参考原点。
			 * @return 如果成功加载和处理瓦片，则返回包含 MapMetadata 的 `std::optional`；否则返回 `std::nullopt`。
			 */
			std::optional<MapMetadata> loadAndProcessTile(
				const std::string& filePath,
				const std::string& tileId,
				const WGS84Point& refOriginWGS84);

			/**
			 * @brief 在添加或移除瓦片后，重新计算并更新所有瓦片的组合WGS84边界 `combinedBoundsWGS84_`。
			 */
			void updateCombinedBounds();
		};

	} // namespace NSEnvironment
} // namespace NSDrones
