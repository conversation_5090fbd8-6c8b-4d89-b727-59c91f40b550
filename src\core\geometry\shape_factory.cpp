#include "core/geometry/shape_factory.h"
#include "utils/enum_utils.h"
#include <stdexcept>

namespace NSDrones{
namespace NSCore {

    // 静态成员初始化
    std::unordered_map<ShapeType, ShapeFactory::ShapeCreator> ShapeFactory::creators_;
    std::unordered_map<std::string, ShapeType> ShapeFactory::type_name_map_;
    bool ShapeFactory::initialized_ = false;

    void ShapeFactory::initialize() {
        if (initialized_) {
            return;
        }

        registerDefaultCreators();
        initialized_ = true;
    }

    std::unique_ptr<IShape> ShapeFactory::createShape(ShapeType type, const ShapeCreationParams& params) {
        ensureInitialized();
        
        auto it = creators_.find(type);
        if (it == creators_.end()) {
            throw std::invalid_argument("不支持的形状类型: " + NSUtils::enumToString(type));
        }
        
        return it->second(params);
    }

    std::unique_ptr<IShape> ShapeFactory::createShape(const std::string& type_name, const ShapeCreationParams& params) {
        ensureInitialized();

        auto type_opt = NSUtils::stringToEnum<ShapeType>(type_name, ShapeType::UNKNOWN);
        if (type_opt == ShapeType::UNKNOWN) {
            throw std::invalid_argument("未知的形状类型名称: " + type_name);
        }

        return createShape(type_opt, params);
    }

    std::unique_ptr<IShape> ShapeFactory::createFromJson(const nlohmann::json& json) {
        ensureInitialized();
        return IShape::createFromJson(json);
    }

    void ShapeFactory::registerCreator(ShapeType type, ShapeCreator creator) {
        creators_[type] = std::move(creator);
    }

    void ShapeFactory::registerTypeName(const std::string& name, ShapeType type) {
        type_name_map_[name] = type;
    }

    bool ShapeFactory::isSupported(ShapeType type) {
        ensureInitialized();
        return creators_.find(type) != creators_.end();
    }

    bool ShapeFactory::isSupported(const std::string& type_name) {
        ensureInitialized();
        auto type_opt = NSUtils::stringToEnum<ShapeType>(type_name, ShapeType::UNKNOWN);
        return type_opt != ShapeType::UNKNOWN && isSupported(type_opt);
    }

    std::vector<ShapeType> ShapeFactory::getSupportedTypes() {
        ensureInitialized();
        std::vector<ShapeType> types;
        types.reserve(creators_.size());
        
        for (const auto& pair : creators_) {
            types.push_back(pair.first);
        }
        
        return types;
    }

    std::vector<std::string> ShapeFactory::getSupportedTypeNames() {
        ensureInitialized();
        std::vector<std::string> names;
        
        for (const auto& pair : creators_) {
            names.push_back(NSUtils::enumToString(pair.first));
        }
        
        return names;
    }

    // 便捷创建方法
    std::unique_ptr<BoxShape> ShapeFactory::createBox(double length, double width, double height) {
        ShapeCreationParams params(ShapeType::BOX);
        params.setParam("length", length)
              .setParam("width", width)
              .setParam("height", height);
        
        auto shape = createShape(ShapeType::BOX, params);
        return std::unique_ptr<BoxShape>(static_cast<BoxShape*>(shape.release()));
    }

    std::unique_ptr<BoxShape> ShapeFactory::createCube(double size) {
        return createBox(size, size, size);
    }

    std::unique_ptr<SphereShape> ShapeFactory::createSphere(double radius) {
        ShapeCreationParams params(ShapeType::SPHERE);
        params.setParam("radius", radius);
        
        auto shape = createShape(ShapeType::SPHERE, params);
        return std::unique_ptr<SphereShape>(static_cast<SphereShape*>(shape.release()));
    }

    std::unique_ptr<CylinderShape> ShapeFactory::createCylinder(double radius, double height) {
        ShapeCreationParams params(ShapeType::CYLINDER);
        params.setParam("radius", radius)
              .setParam("height", height);
        
        auto shape = createShape(ShapeType::CYLINDER, params);
        return std::unique_ptr<CylinderShape>(static_cast<CylinderShape*>(shape.release()));
    }

    std::unique_ptr<ConeShape> ShapeFactory::createCone(double radius, double height) {
        ShapeCreationParams params(ShapeType::CONE);
        params.setParam("radius", radius)
              .setParam("height", height);

        auto shape = createShape(ShapeType::CONE, params);
        return std::unique_ptr<ConeShape>(static_cast<ConeShape*>(shape.release()));
    }

    std::unique_ptr<CapsuleShape> ShapeFactory::createCapsule(double radius, double height) {
        ShapeCreationParams params(ShapeType::CAPSULE);
        params.setParam("radius", radius)
              .setParam("height", height);

        auto shape = createShape(ShapeType::CAPSULE, params);
        return std::unique_ptr<CapsuleShape>(static_cast<CapsuleShape*>(shape.release()));
    }

    std::unique_ptr<EllipsoidShape> ShapeFactory::createEllipsoid(double radius_x, double radius_y, double radius_z) {
        ShapeCreationParams params(ShapeType::ELLIPSOID);
        params.setParam("radius_x", radius_x)
              .setParam("radius_y", radius_y)
              .setParam("radius_z", radius_z);

        auto shape = createShape(ShapeType::ELLIPSOID, params);
        return std::unique_ptr<EllipsoidShape>(static_cast<EllipsoidShape*>(shape.release()));
    }

    std::unique_ptr<ConvexHullShape> ShapeFactory::createConvexHull(const std::vector<fcl::Vector3d>& vertices) {
        ShapeCreationParams params(ShapeType::CONVEX_HULL);
        std::vector<double> vertex_data;
        for (const auto& vertex : vertices) {
            vertex_data.push_back(vertex.x());
            vertex_data.push_back(vertex.y());
            vertex_data.push_back(vertex.z());
        }
        params.setParam("vertices", vertex_data);

        auto shape = createShape(ShapeType::CONVEX_HULL, params);
        return std::unique_ptr<ConvexHullShape>(static_cast<ConvexHullShape*>(shape.release()));
    }

    std::unique_ptr<MeshShape> ShapeFactory::createMesh(const std::vector<fcl::Vector3d>& vertices,
                                                       const std::vector<fcl::Triangle>& triangles) {
        auto mesh = std::make_unique<MeshShape>();
        mesh->setMesh(vertices, triangles);
        return mesh;
    }

    std::unique_ptr<CompoundShape> ShapeFactory::createCompound() {
        ShapeCreationParams params(ShapeType::COMPOUND);
        auto shape = createShape(ShapeType::COMPOUND, params);
        return std::unique_ptr<CompoundShape>(static_cast<CompoundShape*>(shape.release()));
    }

    void ShapeFactory::registerDefaultCreators() {
        // 注册Box创建器
        registerCreator(ShapeType::BOX, [](const ShapeCreationParams& params) -> std::unique_ptr<IShape> {
            double length = params.getNumeric("length", 1.0);
            double width = params.getNumeric("width", 1.0);
            double height = params.getNumeric("height", 1.0);
            return std::make_unique<BoxShape>(length, width, height);
        });

        // 注册Sphere创建器
        registerCreator(ShapeType::SPHERE, [](const ShapeCreationParams& params) -> std::unique_ptr<IShape> {
            double radius = params.getNumeric("radius", 1.0);
            return std::make_unique<SphereShape>(radius);
        });

        // 注册Cylinder创建器
        registerCreator(ShapeType::CYLINDER, [](const ShapeCreationParams& params) -> std::unique_ptr<IShape> {
            double radius = params.getNumeric("radius", 1.0);
            double height = params.getNumeric("height", 2.0);
            return std::make_unique<CylinderShape>(radius, height);
        });

        // 注册Cone创建器
        registerCreator(ShapeType::CONE, [](const ShapeCreationParams& params) -> std::unique_ptr<IShape> {
            double radius = params.getNumeric("radius", 1.0);
            double height = params.getNumeric("height", 2.0);
            return std::make_unique<ConeShape>(radius, height);
        });

        // 注册Capsule创建器
        registerCreator(ShapeType::CAPSULE, [](const ShapeCreationParams& params) -> std::unique_ptr<IShape> {
            double radius = params.getNumeric("radius", 0.5);
            double height = params.getNumeric("height", 1.0);
            return std::make_unique<CapsuleShape>(radius, height);
        });

        // 注册Ellipsoid创建器
        registerCreator(ShapeType::ELLIPSOID, [](const ShapeCreationParams& params) -> std::unique_ptr<IShape> {
            double radius_x = params.getNumeric("radius_x", 1.0);
            double radius_y = params.getNumeric("radius_y", 1.0);
            double radius_z = params.getNumeric("radius_z", 1.0);
            return std::make_unique<EllipsoidShape>(radius_x, radius_y, radius_z);
        });

        // 注册ConvexHull创建器
        registerCreator(ShapeType::CONVEX_HULL, [](const ShapeCreationParams& params) -> std::unique_ptr<IShape> {
            auto vertices = params.getVector("vertices", {});
            std::vector<fcl::Vector3d> vertex_list;
            for (size_t i = 0; i + 2 < vertices.size(); i += 3) {
                vertex_list.emplace_back(vertices[i], vertices[i+1], vertices[i+2]);
            }
            return std::make_unique<ConvexHullShape>(vertex_list);
        });

        // 注册Mesh创建器
        registerCreator(ShapeType::MESH, [](const ShapeCreationParams& params) -> std::unique_ptr<IShape> {
            return std::make_unique<MeshShape>();
        });

        // 注册Compound创建器
        registerCreator(ShapeType::COMPOUND, [](const ShapeCreationParams& params) -> std::unique_ptr<IShape> {
            return std::make_unique<CompoundShape>();
        });

        // 注册类型名称映射（使用magic_enum）
        for (auto type : {ShapeType::BOX, ShapeType::SPHERE, ShapeType::CYLINDER, ShapeType::CONE,
                         ShapeType::CAPSULE, ShapeType::ELLIPSOID, ShapeType::CONVEX_HULL,
                         ShapeType::MESH, ShapeType::COMPOUND}) {
            registerTypeName(NSUtils::enumToString(type), type);
        }
    }

    void ShapeFactory::ensureInitialized() {
        if (!initialized_) {
            initialize();
        }
    }

    // ShapeUtils实现
    double ShapeUtils::computeDistance(const IShape& shape1, const fcl::Transform3d& transform1,
                                      const IShape& shape2, const fcl::Transform3d& transform2) {
        try {
            // 获取FCL几何对象
            auto geom1 = shape1.getFCLGeometry();
            auto geom2 = shape2.getFCLGeometry();
            
            // 创建碰撞对象
            fcl::CollisionObjectd obj1(geom1, transform1);
            fcl::CollisionObjectd obj2(geom2, transform2);
            
            // 计算距离
            fcl::DistanceRequestd request;
            fcl::DistanceResultd result;
            
            fcl::distance(&obj1, &obj2, request, result);
            return result.min_distance;
            
        } catch (const std::exception&) {
            // 如果FCL计算失败，使用AABB近似
            fcl::AABBd aabb1 = shape1.getAABB(transform1);
            fcl::AABBd aabb2 = shape2.getAABB(transform2);
            return aabb1.distance(aabb2);
        }
    }

    bool ShapeUtils::checkIntersection(const IShape& shape1, const fcl::Transform3d& transform1,
                                      const IShape& shape2, const fcl::Transform3d& transform2) {
        try {
            // 获取FCL几何对象
            auto geom1 = shape1.getFCLGeometry();
            auto geom2 = shape2.getFCLGeometry();
            
            // 创建碰撞对象
            fcl::CollisionObjectd obj1(geom1, transform1);
            fcl::CollisionObjectd obj2(geom2, transform2);
            
            // 检查碰撞
            fcl::CollisionRequestd request;
            fcl::CollisionResultd result;
            
            fcl::collide(&obj1, &obj2, request, result);
            return result.isCollision();
            
        } catch (const std::exception&) {
            // 如果FCL计算失败，使用AABB近似
            fcl::AABBd aabb1 = shape1.getAABB(transform1);
            fcl::AABBd aabb2 = shape2.getAABB(transform2);
            return aabb1.overlap(aabb2);
        }
    }

    std::pair<fcl::Vector3d, double> ShapeUtils::computeBoundingSphere(const IShape& shape, 
                                                                      const fcl::Transform3d& transform) {
        fcl::AABBd aabb = shape.getAABB(transform);
        
        fcl::Vector3d center = (aabb.min_ + aabb.max_) * 0.5;
        fcl::Vector3d size = aabb.max_ - aabb.min_;
        double radius = size.norm() * 0.5;
        
        return std::make_pair(center, radius);
    }

    std::unique_ptr<IShape> ShapeUtils::simplifyShape(const IShape& shape, double tolerance) {
        // 简单的简化策略：如果是复合形状，尝试用单个包围盒替代
        if (shape.getType() == ShapeType::COMPOUND) {
            fcl::AABBd aabb = shape.getAABB();
            fcl::Vector3d size = aabb.max_ - aabb.min_;
            
            if (size.norm() < tolerance) {
                // 如果形状很小，用一个点（小球）替代
                return std::make_unique<SphereShape>(tolerance);
            } else {
                // 用包围盒替代
                return std::make_unique<BoxShape>(size.x(), size.y(), size.z());
            }
        }
        
        // 其他形状直接克隆
        return shape.clone();
    }

    bool ShapeUtils::validateShape(const IShape& shape) {
        try {
            // 基本验证
            if (shape.getVolume() <= 0.0) {
                return false;
            }
            
            if (shape.getSurfaceArea() <= 0.0) {
                return false;
            }
            
            if (shape.getCharacteristicSize() <= 0.0) {
                return false;
            }
            
            // 尝试获取AABB
            fcl::AABBd aabb = shape.getAABB();
            fcl::Vector3d size = aabb.max_ - aabb.min_;
            if (size.x() <= 0.0 || size.y() <= 0.0 || size.z() <= 0.0) {
                return false;
            }
            
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

} // namespace NSCore
} // namespace NSDrones