// include/planning/planning_types.h
#pragma once

#include "core/types.h" 
#include "mission/control_point.h"
#include "uav/uav_fwd.h"
#include <vector>
#include <string>
#include <memory>
#include <utility>
#include <stdexcept>
#include <variant>
#include <map>
#include <set>
#include <limits> // Required for std::numeric_limits
#include <optional>

namespace NSDrones { namespace NSUav { class IDynamicModel; } }

namespace NSDrones {
	namespace NSPlanning {
		using namespace NSDrones::NSCore;
		// 注意：不使用 NSUtils 的 using namespace，避免 INVALID_OBJECT_ID 歧义

		// 类型别名
		using RouteID = std::string; // 添加 RouteID 类型别名

		/**
		 * @struct RoutePoint
		 * @brief 表示规划生成的航线上的一个路径点（包含时空和状态信息）。
		 */
		struct RoutePoint {
			WGS84Point position;                     // WGS84位置坐标
			Time time_stamp = 0.0;                  // 时间戳 (秒, 绝对时间或相对任务开始时间)
			Vector3D velocity = Vector3D::Zero();     // 速度 (米/秒, NED坐标系)
			Orientation attitude = Orientation::Identity(); // 姿态 (四元数)
			// **修改：添加载荷动作列表**
			std::vector<NSMission::PayloadActionCommand> payload_actions; // 在此航路点需要执行的载荷动作命令列表

			/** @brief 默认构造函数。 */
			RoutePoint() = default;
			/** @brief 带参数的构造函数。 */
			RoutePoint(const WGS84Point& pos, Time time = 0.0,
				const Vector3D& vel = Vector3D::Zero(),
				const Orientation& att = Orientation::Identity(),
				const std::vector<NSMission::PayloadActionCommand>& actions = {}) // **修改：添加 actions 参数**
				: position(pos), time_stamp(time), velocity(vel), attitude(att), payload_actions(actions) {} // **修改：初始化 actions**
		};

		// 航段类型别名 (一系列航路点)
		using RouteSegment = std::vector<RoutePoint>;

		/**
		 * @class PlannedRoute
		 * @brief 存储特定无人机的完整规划航线（一系列航段或航点）。
		 */
		class PlannedRoute {
		public:
			/** @brief 默认构造函数 (创建无效 ID 的路径)。 */
			PlannedRoute() = default;
			/**
			 * @brief 构造函数。
			 * @param uav_id 关联的无人机 ID。
			 */
			explicit PlannedRoute(ObjectID uav_id);

			/** @brief 获取关联的无人机 ID。 */
			const ObjectID& getUavId() const;
			/** @brief 获取航路点列表 (const 引用)。 */
			const RouteSegment& getWaypoints() const;
			/** @brief 获取航路点列表 (可修改引用)。 */
			RouteSegment& getWaypoints();
			/**
			 * @brief 添加单个航路点。
			 *        会进行时间戳单调性和位置冗余检查。
			 * @param point 要添加的航路点。
			 */
			void addWaypoint(const RoutePoint& point);
			/**
			 * @brief 添加一个航段 (多个航路点)。
			 *        会进行时间戳连续性检查，并可能合并连接点。
			 * @param segment 要添加的航段。
			 */
			void addWaypoints(const RouteSegment& segment);
			/** @brief 清空所有航路点。 */
			void clearWaypoints();
			/** @brief 检查航线是否为空。 */
			bool isEmpty() const;
			/** @brief 计算航线总长度 (米)。 */
			double getTotalLength() const;
			/** @brief 计算航线总时间 (秒)。 */
			Time getTotalTime() const; // 返回 Time 类型
			/**
			 * @brief 获取起始点。
			 * @return 起始航路点的 const 引用。
			 * @throws DroneException 如果航线为空。
			 */
			const RoutePoint& getStartPoint() const;
			/**
			 * @brief 获取结束点。
			 * @return 结束航路点的 const 引用。
			 * @throws DroneException 如果航线为空。
			 */
			const RoutePoint& getEndPoint() const;

		private:
			ObjectID uav_id_ = NSUtils::INVALID_OBJECT_ID; // 关联的无人机 ID
			RouteSegment waypoints_;                       // 航路点序列
		};

		/**
		 * @brief 对几何路径进行简单的匀速时间参数化。
		 * @param path_geometry 输入的 3D 几何路径点 (至少包含 2 个点)。
		 * @param dynamics (可选) 无人机动力学模型，用于检查速度限制。
		 * @param start_time 起始时间戳。
		 * @param start_velocity 起始速度向量。
		 * @param desired_speed 期望的恒定速度 (m/s)。将被限制在动力学范围内（如果提供）。
		 * @param result 输出的时间参数化后的航段。
		 * @return 如果参数化成功且生成了有效航段，返回 true。
		 */
		bool timeParameterizeConstantSpeed(
			const std::vector<WGS84Point>& path_geometry,
			const NSUav::IDynamicModel* dynamics, // 可选
			Time start_time,
			const Vector3D& start_velocity,
			double desired_speed,
			RouteSegment& result);

		/**
		 * @enum PlanningStrategyType
		 * @brief 定义了路径规划或任务分配可能采用的宏观策略类型。
		 */
		enum class PlanningStrategyType {
			DEFAULT,				// 默认策略，通常是最快或最直接
			MINIMIZE_TIME,			// 最小化总时间
			MINIMIZE_ENERGY,		// 最小化总能耗
			MINIMIZE_RISK,			// 最小化风险 (例如，避开高威胁区域)
			MAXIMIZE_COVERAGE,		// 最大化覆盖率 (例如，在勘测任务中)
			CUSTOM					// 用户自定义或其他特定策略
		};

		/**
		 * @enum PlannerType
		 * @brief 标识不同类型的规划器组件。
		 */
		enum class PlannerType {
			UNKNOWN,
			TASK_ALLOCATOR,
			PATH_PLANNER,
			TRAJECTORY_OPTIMIZER,
			MISSION_PLANNER,
			COVERAGE_PLANNER
		};

		/**
		 * @enum WarningType
		 * @brief 用于规划模块内部或与其他模块交互时产生的警告类型。
		 */
		enum class WarningType {
			UNKNOWN,
			CRITICAL,
			PLANNING_ERROR,
			OPTIMIZATION_FAILED,
			ALLOCATION_ERROR,
			INTERNAL_ERROR,
			TRAJECTORY_INFEASIBLE,
			ENTERED_WARNING_ZONE,
			LEFT_WARNING_ZONE,
			ENTERED_KEEPOUT_ZONE,
			CROSS_KEEPOUT_BOUNDARY,
			ENTERED_THREAT_ZONE,
			CROSS_THREAT_BOUNDARY,
			DYNAMICS_VIOLATION,
			PROXIMITY_ALERT,
			RESOURCE_UNAVAILABLE,
			PLANNING_FAILURE,
			OPTIMIZATION_FAILURE,
			PLANNER_NOT_FOUND,
			ENVIRONMENT_QUERY_FAILURE,
			INVALID_STATE,
			PAYLOAD_ERROR
		};

		/**
		 * @struct WarningEvent
		 * @brief 表示规划过程中产生的告警事件。
		 */
		struct WarningEvent {
			WarningType wtype = WarningType::UNKNOWN;			// 告警类型
			std::string description = "";						// 告警的文字描述
			Time time_stamp = 0.0;								// 事件发生时间 (近似)
			WGS84Point location;								// 事件发生位置 (WGS84坐标)
			ObjectID related_uav_id = NSUtils::INVALID_OBJECT_ID;		// 关联的无人机 ID (如果适用)
			ObjectID related_zone_id = NSUtils::INVALID_OBJECT_ID;		// 关联的区域 ID (如果适用)
			ObjectID related_task_id = NSUtils::INVALID_OBJECT_ID;		// 关联的任务 ID (如果适用)

			/** @brief 默认构造函数。 */
			WarningEvent() = default;
			/**
			 * @brief 带参数的构造函数。
			 * @param t 告警类型。
			 * @param desc 描述。
			 * @param time 时间戳。
			 * @param loc 位置。
			 * @param uav_id 关联 UAV ID。
			 * @param zone_id 关联区域 ID。
			 * @param task_id 关联任务 ID。
			 */
			WarningEvent(WarningType t, std::string desc, Time time = 0.0,
				const WGS84Point& loc = WGS84Point(), // 默认无效WGS84坐标
				ObjectID uav_id = NSUtils::INVALID_OBJECT_ID,
				ObjectID zone_id = NSUtils::INVALID_OBJECT_ID,
				ObjectID task_id = NSUtils::INVALID_OBJECT_ID)
				: wtype(t), description(std::move(desc)), time_stamp(time), location(loc),
				related_uav_id(std::move(uav_id)), related_zone_id(std::move(zone_id)), related_task_id(std::move(task_id)) {}
		};

		/**
		 * @enum CalculationFidelityType
		 * @brief 定义了计算的精度级别。
		 */
		enum class CalculationFidelityType {
			SIMPLE,
			DETAILED,
			UNKNOWN
		};

		/**
		 * @enum TaskAllocationStrategyType
		 * @brief 定义了任务分配器可以使用的不同策略。
		 */
		enum class TaskAllocationStrategyType {
			CLOSEST_AVAILABLE,
			LEAST_BUSY,
			UNKNOWN
		};

		/**
		 * @enum ScanPatternType
		 * @brief 定义了区域扫描任务可以使用的不同扫描模式。
		 */
		enum class ScanPatternType {
			ZIGZAG,      // 之字形扫描
			PARALLEL,    // 平行线扫描
			SPIRAL,      // 螺旋扫描
			UNKNOWN      // 未知或未指定
		};

	} // namespace NSPlanning
} // namespace NSDrones

using namespace NSDrones::NSPlanning;
