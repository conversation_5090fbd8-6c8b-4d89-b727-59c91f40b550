// src/core/base_object.cpp
#include "core/entity_object.h"
#include "core/entity_state.h"
#include "environment/coordinate/coordinate_manager.h"
#include "utils/coordinate_converter.h"
#include "utils/logging.h"
// 前向声明，避免循环依赖
namespace NSDrones {
	namespace NSEnvironment {
		class Environment;
		class AdaptiveCoordinate;
	}
}
#include "params/parameters.h"
#include "params/paramregistry.h"
#include "utils/logging.h"
#include "utils/object_id.h"
#include "core/movement_strategy.h"
#include "core/geometry/shape_factory.h"
#include "core/types.h"
#include "utils/orientation_utils.h"
#include <stdexcept>
#include <algorithm>
#include <chrono>
#include <sstream>
#include <typeinfo>
#include <set>

namespace NSDrones {
	namespace NSCore {

		/**
		 * @brief EntityObject 构造函数。
		 */
		EntityObject::EntityObject(ObjectID id,
			const std::string& object_config_type_key,
			const std::string& name,
			const EntityState& initial_state)
			: BaseObject(id, object_config_type_key, name),
			state_(std::make_shared<EntityState>(initial_state))
		{
			// 确保初始状态中的时间戳合理 (如果为0，设为当前时间)
			if (state_->time_stamp <= 0.0) {
				state_->time_stamp = std::chrono::duration_cast<std::chrono::duration<double>>(
					std::chrono::system_clock::now().time_since_epoch()).count();
			}
			// 确保初始状态字符串不为空
			if (state_->status.empty()) {
				state_->status = "CREATED";
			}

			// dynamic_data_ 在其构造函数中初始化时间戳
			LOG_TRACE("已构造: ID='{}', 配置类型键='{}', 名称='{}'. 初始位置: {}",
				id_, type_tag_, name_, state_->position.toString());
		}

		/** @brief 获取完整的动态状态数据指针 (const)。*/
		const EntityStatePtr EntityObject::getState() const { return state_; }
		/** @brief 获取当前WGS84位置。*/
		const WGS84Point& EntityObject::getWGS84Position() const {
		    if (!state_) throw std::runtime_error("EntityObject::getWGS84Position() called on null state_");
		    return state_->position;
		}

		/** @brief 获取当前局部坐标位置（用于碰撞检测和空间索引）。*/
		std::optional<NedPoint> EntityObject::getNedPosition(const std::string& task_space_id) const {
		    if (!state_) {
		        LOG_ERROR("对象 [{}]: getNedPosition() 调用时状态为空", name_);
		        return std::nullopt;
		    }

		    try {
		        // 检查WGS84坐标是否有效
		        if (!std::isfinite(state_->position.latitude) || !std::isfinite(state_->position.longitude) || !std::isfinite(state_->position.altitude)) {
		            LOG_WARN("对象 [{}]: WGS84坐标包含无效值 (NaN或Inf): {}", name_, state_->position.toString());
		            return std::nullopt;
		        }

		        // 获取环境的坐标系统管理器
		        auto env = getEnvironment();
		        auto coordinate_manager = env ? env->getCoordinateManager() : nullptr;
		        if (!coordinate_manager) {
		            LOG_WARN("对象 [{}]: 坐标系统管理器未设置，无法转换为局部坐标", name_);
		            return std::nullopt;
		        }

		        // 获取指定的任务空间
		        auto task_space = coordinate_manager->getTaskSpace(task_space_id);
		        if (!task_space) {
		            LOG_WARN("对象 [{}]: 无法获取任务空间 '{}'", name_, task_space_id);
		            return std::nullopt;
		        }

		        // 将WGS84坐标转换为NED局部坐标
		        NedPoint local_position = task_space->wgs84ToNED(state_->position);

		        // 检查转换结果是否有效
		        if (!std::isfinite(local_position.north()) || !std::isfinite(local_position.east()) || !std::isfinite(local_position.down())) {
		            LOG_WARN("对象 [{}]: 坐标转换结果包含无效值: {}",
		                name_, local_position.toString());
		            return std::nullopt;
		        }

		        LOG_TRACE("对象 [{}]: WGS84坐标 {} 转换为局部坐标 {}",
		            name_, state_->position.toString(), local_position.toString());

		        return local_position;

		    } catch (const std::exception& e) {
		        LOG_ERROR("对象 [{}]: 坐标转换时发生异常: {}", name_, e.what());
		        return std::nullopt;
		    }
		}

		/** @brief 设置局部坐标位置（会转换为WGS84坐标存储）。*/
		void EntityObject::setNedPosition(const NedPoint& local_pos, const std::string& task_space_id) {
		    if (!state_) throw std::runtime_error("EntityObject::setNedPosition() called on null state_");

		    try {
		        // 获取环境的坐标系统管理器
		        auto env = getEnvironment();
		        auto coordinate_manager = env ? env->getCoordinateManager() : nullptr;
		        if (!coordinate_manager) {
		            LOG_WARN("对象 [{}]: 坐标系统管理器未设置，无法转换局部坐标为WGS84，使用默认WGS84坐标", name_);
		            // 回退方案：使用默认的WGS84坐标，而不是错误地将NED坐标当作WGS84坐标
		            WGS84Point default_wgs84_pos(118.80, 32.10, 10.0);  // 使用配置的默认原点
		            setWGS84Position(default_wgs84_pos);
		            return;
		        }

		        // 获取指定的任务空间
		        auto task_space = coordinate_manager->getTaskSpace(task_space_id);
		        if (!task_space) {
		            LOG_WARN("对象 [{}]: 无法获取任务空间 '{}'，使用默认WGS84坐标", name_, task_space_id);
		            // 回退方案：使用默认的WGS84坐标，而不是错误地将NED坐标当作WGS84坐标
		            WGS84Point default_wgs84_pos(118.80, 32.10, 10.0);  // 使用配置的默认原点
		            setWGS84Position(default_wgs84_pos);
		            return;
		        }

		        // 将NED局部坐标转换为WGS84坐标
		        WGS84Point wgs84_position = task_space->nedToWGS84(local_pos);
		        LOG_TRACE("对象 [{}]: 局部坐标 {} 转换为WGS84坐标 {}",
		            name_, local_pos.toString(), wgs84_position.toString());

		        // 调用原有的setWGS84Position方法
		        setWGS84Position(wgs84_position);

		    } catch (const std::exception& e) {
		        LOG_ERROR("对象 [{}]: 局部坐标转换为WGS84时发生异常: {}，使用默认WGS84坐标", name_, e.what());
		        // 回退方案：使用默认的WGS84坐标，而不是错误地将NED坐标当作WGS84坐标
		        WGS84Point default_wgs84_pos(118.80, 32.10, 10.0);  // 使用配置的默认原点
		        setWGS84Position(default_wgs84_pos);
		    }
		}
		/** @brief 获取当前姿态。*/
		const Orientation& EntityObject::getOrientation() const {
		    if (!state_) throw std::runtime_error("EntityObject::getOrientation() called on null state_");
		    return state_->orientation;
		}
		/** @brief 获取当前速度。*/
		const Vector3D& EntityObject::getVelocity() const {
		    if (!state_) throw std::runtime_error("EntityObject::getVelocity() called on null state_");
		    return state_->velocity_wgs84;
		}
		/** @brief 获取当前状态字符串。*/
		const std::string& EntityObject::getStatus() const {
		    if (!state_) throw std::runtime_error("EntityObject::getStatus() called on null state_");
		    return state_->status;
		}
		/** @brief 获取能力列表。*/
		const std::vector<std::string>& EntityObject::getCapabilities() const {
		    if (!state_) throw std::runtime_error("EntityObject::getCapabilities() called on null state_");
		    return state_->capabilities;
		}
		/** @brief 获取当前时间戳。*/
		Time EntityObject::getTimeStamp() const {
		    if (!state_) throw std::runtime_error("EntityObject::getTimeStamp() called on null state_");
		    return state_->time_stamp;
		}

		/**
		 * @brief 获取对象当前的轴对齐包围盒 (AABB)。
		 */
		BoundingBox EntityObject::getBoundingBox() const {
		    if (!state_) throw std::runtime_error("EntityObject::getBoundingBox() called on null state_");
			if (shape_) {
				// 直接使用ECEF坐标进行几何运算，避免坐标转换开销
				EcefPoint ecef_pos = NSUtils::CoordinateConverter::wgs84ToECEF(state_->position);

				// 创建FCL变换矩阵（使用ECEF坐标）
				fcl::Vector3d translation(ecef_pos.x(), ecef_pos.y(), ecef_pos.z());
				Transform3d transform = Transform3d::Identity();
				transform.translation() = translation;

				// 直接返回FCL AABB（现在BoundingBox就是fcl::AABBd）
				return shape_->getAABB(transform);
			}
			LOG_TRACE("对象 [{} ID:{}]: 无形状，返回无效包围盒", name_, id_);
			return BoundingBox(); // 无形状则返回无效包围盒
		}

		/**
		 * @brief 初始化对象。
		 *        加载与对象类型标签关联的默认参数。
		 *        **重要:** 派生类重写时应调用基类的 `initialize()`。
		 *        此方法通常在对象添加到环境后被调用。
		 * @return 如果初始化成功返回 true。
		 */
		bool EntityObject::initialize(std::shared_ptr<NSParams::ParamValues> final_params,
		                            const nlohmann::json& raw_instance_json_config) {
			LOG_TRACE("对象 [{} ID:{}]: 开始执行 initialize(std::shared_ptr<ParamValues>, const json&) (类型标签: '{}')", name_, id_, type_tag_);

			// 调用基类的 initialize 方法
			// 如果基类初始化失败，则 EntityObject 初始化也失败
			if (!BaseObject::initialize(final_params, raw_instance_json_config)) {
				LOG_ERROR("对象 [{} ID:{}] (类型标签: {}): BaseObject::initialize 失败。EntityObject 初始化终止。", name_, id_, type_tag_);
				return false;
			}
			// 基类初始化成功，日志已在 BaseObject::initialize 内部记录

			// 从参数系统加载初始位置和姿态
			if (!loadInitialStateFromParams()) {
				LOG_ERROR("对象 [{} ID:{}] (类型标签: {}): 从参数加载初始状态失败。初始化终止。", name_, id_, type_tag_);
				return false;
			}

			// 从 raw_instance_json_config 加载/覆盖非参数化的数据
			if (raw_instance_json_config.contains("initial_state_override")) {
				const auto& state_override = raw_instance_json_config["initial_state_override"];
				LOG_TRACE("对象 [{} ID:{}]: 发现 'initial_state_override'，尝试应用...", name_, id_);
				// 示例: 更新位置 (需要更完整的 JSON 解析和错误处理)
				// JSON数组格式: [经度, 纬度, 高度]，但WGS84Point构造函数需要(纬度, 经度, 高度)
				if (state_override.contains("position") && state_override["position"].is_array() && state_override["position"].size() == 3) {
					double lon = state_override["position"][0].get<double>(); // 经度
					double lat = state_override["position"][1].get<double>(); // 纬度
					double alt = state_override["position"][2].get<double>(); // 高度
					state_->position = WGS84Point(lat, lon, alt); // 构造函数参数顺序: (纬度, 经度, 高度)
					LOG_TRACE("对象 [{} ID:{}]: 位置已通过 'initial_state_override' 更新为: ({})",
						name_, id_, state_->position.toString());
				}
				// 可以类似地处理姿态、速度等其他状态字段的覆盖
			}

			// 创建并设置形状 (现在 this->params_ 已经完全就绪)
			if (!createAndSetShapeFromParams()) {
				LOG_ERROR("对象 [{} ID:{}] (类型标签: {}): 从参数创建形状失败。初始化终止。", name_, id_, type_tag_);
				return false; // 初始化失败
			}

			// 创建并设置移动策略 (如果尚未设置)
			if (!createAndSetMovementStrategyFromParams()) {
				LOG_ERROR("对象 [{} ID:{}] (类型标签: {}): 从参数创建移动策略失败。初始化终止。", name_, id_, type_tag_);
				return false; // 初始化失败
			}

			LOG_DEBUG("对象 [{} ID:{}]: EntityObject::initialize 已成功完成 (类型标签: '{}')。", name_, id_, type_tag_); // 改为 DEBUG 级别
			return true;
		}

		bool EntityObject::loadInitialStateFromParams() {
			LOG_TRACE("对象 [{} ID:{}]: 开始从参数加载初始状态...", name_, id_);

			try {
				// 优先尝试加载 WGS84 坐标，如果不存在则尝试 NED 坐标
				if (params_->hasParam("initial_position_wgs84")) {
					// 直接使用 WGS84Point 类型
					// 使用三参数版本的getValueOrDefault，它会自动从ParamRegistry获取参数定义中的缺省值
					WGS84Point backup_default(118.0, 32.0, 0.0); // 备用缺省值 (经度, 纬度, 高度)
					auto wgs84_pos = params_->getValueOrDefault<WGS84Point>("initial_position_wgs84", backup_default, &NSParams::ParamRegistry::getInstance());
					setWGS84Position(wgs84_pos);
					LOG_DEBUG("对象 [{} ID:{}]: 初始位置 (WGS84): {}",
						name_, id_, wgs84_pos.toString());
				} else if (params_->hasParam("initial_position_ned")) {
					// 使用 NED 坐标，需要转换为 WGS84 坐标存储
					auto initial_position_ned = getParamOrDefault<Vector3D>("initial_position_ned", Vector3D(0.0, 0.0, 0.0));
					NedPoint ned_pos(initial_position_ned.x(), initial_position_ned.y(), initial_position_ned.z());
					// 使用setNedPosition方法进行坐标转换
					setNedPosition(ned_pos);
					LOG_DEBUG("对象 [{} ID:{}]: 初始位置 (NED): {} -> WGS84: {}",
						name_, id_, ned_pos.toString(),
						state_->position.toString());
				} else {
					// 使用默认的 WGS84 坐标
					WGS84Point default_wgs84_pos(32.10, 118.80, 10.0);
					setWGS84Position(default_wgs84_pos);
					LOG_WARN("对象 [{} ID:{}]: 未找到初始位置参数，使用默认 WGS84 坐标: {}",
						name_, id_, default_wgs84_pos.toString());
				}

				// 加载初始姿态 (YPR 角度)
				auto initial_orientation_ypr_deg = getParamOrDefault<Vector3D>("initial_orientation_ypr_deg", Vector3D(0.0, 0.0, 0.0));

				// 使用 OrientationUtils 将 YPR 角度转换为四元数
				state_->orientation = NSUtils::OrientationUtils::yprToQuaternion(
					initial_orientation_ypr_deg.x(),  // Yaw
					initial_orientation_ypr_deg.y(),  // Pitch
					initial_orientation_ypr_deg.z()   // Roll
				);
				LOG_DEBUG("对象 [{} ID:{}]: 初始姿态 YPR: ({:.2f}, {:.2f}, {:.2f})度 -> 四元数: (w={:.4f}, x={:.4f}, y={:.4f}, z={:.4f})",
					name_, id_,
					initial_orientation_ypr_deg.x(), initial_orientation_ypr_deg.y(), initial_orientation_ypr_deg.z(),
					state_->orientation.w(), state_->orientation.x(), state_->orientation.y(), state_->orientation.z());

				// 更新时间戳
				state_->time_stamp = std::chrono::duration_cast<std::chrono::duration<double>>(
					std::chrono::system_clock::now().time_since_epoch()).count();

				LOG_TRACE("对象 [{} ID:{}]: 初始状态加载完成", name_, id_);
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("对象 [{} ID:{}]: 从参数加载初始状态时发生异常: {}", name_, id_, e.what());
				return false;
			}
		}

		bool EntityObject::createAndSetShapeFromParams() {
			LOG_TRACE("对象 [{} ID:{}] (类型标签: {}): 开始从参数创建形状...", name_, id_, type_tag_);

			// 使用正确的枚举获取方法
			auto shape_type_opt = params_->getEnumValue<ShapeType>("shape_type");
			if (!shape_type_opt.has_value()) {
				LOG_WARN("对象 [{} ID:{}] (类型标签: {}): 无法获取 shape_type 枚举值，使用默认值 UNKNOWN。", name_, id_, type_tag_);
				shape_type_opt = ShapeType::UNKNOWN;
			}

			ShapeType shape_type = shape_type_opt.value();
			IShapePtr new_shape = nullptr;

			if (shape_type == ShapeType::UNKNOWN) {
				LOG_TRACE("对象 [{} ID:{}] (类型标签: {}): 形状类型为 UNKNOWN。不创建形状。", name_, id_, type_tag_);
				setShape(nullptr); // 确保清除任何现有形状
				return true;
			}

			// 获取形状的局部偏移和姿态参数 (使用Vector3D然后转换为NedPoint)
			Vector3D shape_offset_vec = getParamOrDefault<Vector3D>("shape_offset_ned", Vector3D(0,0,0));
			NedPoint shape_offset_param(shape_offset_vec.x(), shape_offset_vec.y(), shape_offset_vec.z());
			Vector3D shape_orient_ypr_deg_param = getParamOrDefault<Vector3D>("shape_orientation_ypr_deg", Vector3D(0,0,0));

			// 使用新形状系统的ShapeFactory创建形状
			try {
				if (shape_type == ShapeType::BOX) {
					Vector3D size = getParamOrDefault<Vector3D>("shape_box_size_xyz", Vector3D(1,1,1));
					new_shape = ShapeFactory::createBox(size.x(), size.y(), size.z());
					LOG_TRACE("创建 BoxShape: 大小=({:.2f},{:.2f},{:.2f})", size.x(), size.y(), size.z());
				} else if (shape_type == ShapeType::CYLINDER) {
					double radius = getParamOrDefault<double>("shape_cylinder_radius_m", 1.0);
					double height = getParamOrDefault<double>("shape_cylinder_height_m", 1.0);
					new_shape = ShapeFactory::createCylinder(radius, height);
					LOG_TRACE("创建 CylinderShape: 半径={:.2f}, 高度={:.2f}", radius, height);
				} else if (shape_type == ShapeType::SPHERE) {
					double radius = getParamOrDefault<double>("shape_sphere_radius_m", 1.0);
					new_shape = ShapeFactory::createSphere(radius);
					LOG_TRACE("创建 SphereShape: 半径={:.2f}", radius);
				} else if (shape_type == ShapeType::CONE) {
					double radius = getParamOrDefault<double>("shape_cone_radius_m", 1.0);
					double height = getParamOrDefault<double>("shape_cone_height_m", 2.0);
					new_shape = ShapeFactory::createCone(radius, height);
					LOG_TRACE("创建 ConeShape: 半径={:.2f}, 高度={:.2f}", radius, height);
				} else if (shape_type == ShapeType::CAPSULE) {
					double radius = getParamOrDefault<double>("shape_capsule_radius_m", 0.5);
					double height = getParamOrDefault<double>("shape_capsule_height_m", 1.0);
					new_shape = ShapeFactory::createCapsule(radius, height);
					LOG_TRACE("创建 CapsuleShape: 半径={:.2f}, 高度={:.2f}", radius, height);
				} else if (shape_type == ShapeType::ELLIPSOID) {
					double radius_x = getParamOrDefault<double>("shape_ellipsoid_radius_x_m", 1.0);
					double radius_y = getParamOrDefault<double>("shape_ellipsoid_radius_y_m", 1.0);
					double radius_z = getParamOrDefault<double>("shape_ellipsoid_radius_z_m", 1.0);
					new_shape = ShapeFactory::createEllipsoid(radius_x, radius_y, radius_z);
					LOG_TRACE("创建 EllipsoidShape: 半径=({:.2f},{:.2f},{:.2f})", radius_x, radius_y, radius_z);
				} else if (shape_type == ShapeType::POLYGON) {
					// 获取多边形顶点参数
					auto vertices_param = getParamOrDefault<std::vector<Vector3D>>("shape_polygon_vertices_ned", std::vector<Vector3D>());
					std::vector<fcl::Vector3d> vertices;
					vertices.reserve(vertices_param.size());
					for (const auto& v : vertices_param) {
						vertices.emplace_back(v.x(), v.y(), v.z());
					}
					if (vertices.size() >= 3) {
						new_shape = std::make_unique<PolygonShape>(vertices);
						LOG_TRACE("创建 PolygonShape: 顶点数={}", vertices.size());
					} else {
						LOG_WARN("多边形顶点数不足 ({}), 创建默认三角形", vertices.size());
						new_shape = PolygonShape::createTriangle();
					}
				} else if (shape_type == ShapeType::POINT) {
					Vector3D point_pos = getParamOrDefault<Vector3D>("shape_point_position_ned", Vector3D(0,0,0));
					new_shape = std::make_unique<PointShape>(fcl::Vector3d(point_pos.x(), point_pos.y(), point_pos.z()));
					LOG_TRACE("创建 PointShape: 位置=({:.2f},{:.2f},{:.2f})", point_pos.x(), point_pos.y(), point_pos.z());
				} else if (shape_type == ShapeType::LINE) {
					Vector3D start_pos = getParamOrDefault<Vector3D>("shape_line_start_ned", Vector3D(0,0,0));
					Vector3D end_pos = getParamOrDefault<Vector3D>("shape_line_end_ned", Vector3D(1,0,0));
					new_shape = std::make_unique<LineShape>(start_pos, end_pos);
					LOG_TRACE("创建 LineShape: 起点=({:.2f},{:.2f},{:.2f}), 终点=({:.2f},{:.2f},{:.2f})",
						start_pos.x(), start_pos.y(), start_pos.z(), end_pos.x(), end_pos.y(), end_pos.z());
				} else if (shape_type == ShapeType::PLANE) {
					Vector3D normal = getParamOrDefault<Vector3D>("shape_plane_normal", Vector3D(0,0,1));
					double distance = getParamOrDefault<double>("shape_plane_distance_m", 0.0);
					new_shape = std::make_unique<PlaneShape>(fcl::Vector3d(normal.x(), normal.y(), normal.z()), distance);
					LOG_TRACE("创建 PlaneShape: 法向量=({:.2f},{:.2f},{:.2f}), 距离={:.2f}",
						normal.x(), normal.y(), normal.z(), distance);
				} else {
					// 使用枚举转字符串工具进行日志记录
					std::string shape_type_name = NSUtils::enumToString(shape_type);
					LOG_ERROR("对象 [{} ID:{}] (类型标签: {}): 不支持的形状类型 '{}'。无法创建形状。", name_, id_, type_tag_, shape_type_name);
					return false;
				}
			} catch (const std::exception& e) {
				std::string shape_type_name = NSUtils::enumToString(shape_type);
				LOG_ERROR("对象 [{} ID:{}] (类型标签: {}): 创建形状类型 '{}' 时发生异常: {}", name_, id_, type_tag_, shape_type_name, e.what());
				return false;
			}

			if (new_shape) {
				setShape(new_shape);
				std::string shape_type_name = NSUtils::enumToString(shape_type);
				LOG_TRACE("对象 [{} ID:{}] (类型标签: {}): 成功创建基础形状类型 '{}' 并设置。参数中指定的局部偏移:{}, 姿态YPR:({:.1f},{:.1f},{:.1f}) 将在需要时与对象状态结合使用。",
                         name_, id_, type_tag_, shape_type_name,
                         shape_offset_param.toString(),
                         shape_orient_ypr_deg_param.x(), shape_orient_ypr_deg_param.y(), shape_orient_ypr_deg_param.z());
				return true;
			} else {
                // 此处理论上不会到达，因为未知 shape_type_str 会提前返回 false
				LOG_ERROR("对象 [{} ID:{}] (类型标签: {}): 形状创建失败，new_shape 为空指针 (逻辑错误)。", name_, id_, type_tag_);
				return false;
			}
		}

		bool EntityObject::createAndSetMovementStrategyFromParams() {
			LOG_TRACE("对象 [{} ID:{}] (类型标签: {}): 开始从参数创建移动策略...", name_, id_, type_tag_);

			// 如果已经有移动策略，则跳过创建
			if (movement_strategy_) {
				LOG_DEBUG("对象 [{} ID:{}] (类型标签: {}): 已存在移动策略 '{}'，跳过创建。",
					name_, id_, type_tag_, typeid(*movement_strategy_).name());
				return true;
			}

			// 使用正确的枚举获取方法
			auto strategy_type_opt = params_->getEnumValue<MovementStrategyType>("movement_strategy_type");
			if (!strategy_type_opt.has_value()) {
				LOG_WARN("对象 [{} ID:{}] (类型标签: {}): 无法获取 movement_strategy_type 枚举值，使用默认值 STATIC。",
					name_, id_, type_tag_);
				strategy_type_opt = MovementStrategyType::STATIC;
			}

			MovementStrategyType strategy_type = strategy_type_opt.value();
			std::shared_ptr<IMovementStrategy> new_strategy = nullptr;

			// 根据枚举值创建相应的移动策略
			switch (strategy_type) {
			case MovementStrategyType::STATIC:
				new_strategy = std::make_shared<StillMovementStrategy>(*this);
				LOG_TRACE("创建 StillMovementStrategy");
				break;
			case MovementStrategyType::LINEAR:
				new_strategy = std::make_shared<LinearMovementStrategy>(*this);
				LOG_TRACE("创建 LinearMovementStrategy");
				break;
			case MovementStrategyType::ATTACHED:
				new_strategy = std::make_shared<AttachedMovementStrategy>(*this);
				LOG_TRACE("创建 AttachedMovementStrategy");
				break;
			case MovementStrategyType::FLIGHT_STRATEGY:
				// FlightStrategy 是 UAV 专用的，基类 EntityObject 不应该创建它
				// 这种情况应该由 UAV 类在其 initialize 方法中处理
				LOG_DEBUG("对象 [{} ID:{}] (类型标签: {}): movement_strategy_type 为 FLIGHT_STRATEGY，但这是 UAV 专用策略。基类不创建，由派生类处理。",
					name_, id_, type_tag_);
				return true; // 不是错误，由派生类处理
			case MovementStrategyType::CIRCULAR_PATH:
			case MovementStrategyType::SCRIPTED_PATH:
				// 这些策略尚未实现
				LOG_WARN("对象 [{} ID:{}] (类型标签: {}): movement_strategy_type '{}' 尚未实现，使用 STATIC 策略作为回退。",
					name_, id_, type_tag_, NSUtils::enumToString(strategy_type));
				new_strategy = std::make_shared<StillMovementStrategy>(*this);
				break;
			case MovementStrategyType::UNKNOWN:
			default:
				LOG_WARN("对象 [{} ID:{}] (类型标签: {}): 未知的 movement_strategy_type '{}'，使用 STATIC 策略作为默认。",
					name_, id_, type_tag_, NSUtils::enumToString(strategy_type));
				new_strategy = std::make_shared<StillMovementStrategy>(*this);
				break;
			}

			// 初始化策略
			if (new_strategy) {
				// 使用空的 JSON 对象作为策略参数，具体参数可以从 params_ 中获取
				nlohmann::json strategy_params = nlohmann::json::object();
				if (new_strategy->initialize(strategy_params, *this)) {
					setMovementStrategy(new_strategy);
					LOG_INFO("对象 [{} ID:{}] (类型标签: {}): 成功创建并设置移动策略 '{}'。",
						name_, id_, type_tag_, NSUtils::enumToString(strategy_type));
					return true;
				} else {
					LOG_ERROR("对象 [{} ID:{}] (类型标签: {}): 移动策略 '{}' 初始化失败。",
						name_, id_, type_tag_, NSUtils::enumToString(strategy_type));
					return false;
				}
			}

			LOG_ERROR("对象 [{} ID:{}] (类型标签: {}): 无法创建移动策略。", name_, id_, type_tag_);
			return false;
		}

		/**
		 * @brief 设置对象的几何形状。
		 */
		void EntityObject::setShape(IShapePtr shape) {
			if (shape_ == shape) {
				LOG_TRACE("对象 [{} ID:{}]: 新形状与当前形状相同，跳过设置。", name_, id_);
				return;
			}
			LOG_TRACE("对象 [{} ID:{}]: 设置新形状...", name_, id_);
			EntityStateSnapshot snapshot;
			snapshot.id = id_;
			snapshot.object_type = type_tag_;
			// 记录旧状态 (包围盒会变)
			if (shape_) snapshot.old_bounds = getBoundingBox();
			snapshot.old_position = state_->position; // 使用WGS84坐标
			snapshot.old_status = state_->status;
			snapshot.old_parent_id = parent_id_;
			snapshot.old_capabilities = state_->capabilities;
			// 记录新状态 (包围盒会变，但不由快照传递)
			snapshot.current_position = state_->position; // 使用WGS84坐标
			snapshot.current_status = state_->status;
			snapshot.current_parent_id = parent_id_;
			snapshot.current_capabilities = state_->capabilities;

			// 更新内部形状指针
			shape_ = std::move(shape);
			state_->time_stamp = std::chrono::duration_cast<std::chrono::duration<double>>(
				std::chrono::system_clock::now().time_since_epoch()).count(); // 更新时间戳

			// 通知环境更新 (主要是空间索引，因为包围盒变了)
			notifyUpdate(snapshot);
			LOG_TRACE("对象 [{} ID:{}]: 新形状设置完成并已通知环境。", name_, id_);
		}

		// --- 动态数据修改器实现 ---
		/**
		 * @brief (内部) 通知环境状态已更新。
		 */
		void EntityObject::notifyUpdate(const EntityStateSnapshot& state_before_update) {
			LOG_TRACE("对象 [{} ID:{}]: 准备通知环境状态更新...", name_, id_);
			if (NSUtils::isValidObjectID(id_)) { // 确保 ID 有效
				auto env = getEnvironment();
			if (env) {
				env->notifyObjectUpdate(id_, state_before_update); // 通知环境更新索引
			}
				LOG_TRACE("对象 [{} ID:{}]: 已通知环境状态更新。", name_, id_);
			}
			else {
				LOG_WARN("对象 [{} ID:{}]: ID 无效，无法通知环境更新。", name_, id_);
			}
		}

		/** @brief 设置对象WGS84位置，并通知环境更新空间索引。*/
		void EntityObject::setWGS84Position(const WGS84Point& pos) {
			// 检查位置是否有显著变化 (计算各坐标分量的差值)
			double lat_diff = std::abs(state_->position.latitude - pos.latitude);
			double lon_diff = std::abs(state_->position.longitude - pos.longitude);
			double alt_diff = std::abs(state_->position.altitude - pos.altitude);
			double max_diff = std::max({lat_diff, lon_diff, alt_diff});

			if (max_diff > Constants::GEOMETRY_EPSILON) {
				LOG_TRACE("对象 [{} ID:{}]: 位置改变: {} -> {}",
					name_, id_, state_->position.toString(), pos.toString());
				EntityStateSnapshot snapshot; // 创建快照记录变化
				snapshot.id = id_;
				snapshot.object_type = type_tag_;
				// 记录旧状态的关键信息
				snapshot.old_position = state_->position; // 使用当前WGS84坐标
				if (shape_) snapshot.old_bounds = getBoundingBox(); // 获取基于旧状态的包围盒
				// 填充其他旧状态 (用于可能的索引移除)
				snapshot.old_status = state_->status;
				snapshot.old_parent_id = parent_id_;
				snapshot.old_capabilities = state_->capabilities;

				// 填充新状态的关键信息 (用于添加新索引)
				snapshot.current_position = pos; // 使用新的WGS84位置
				snapshot.current_status = state_->status; // 其他状态不变
				snapshot.current_parent_id = parent_id_;
				snapshot.current_capabilities = state_->capabilities;

				// **重要：先更新内部状态，再通知环境**
				state_->position = pos; // 设置新位置
				state_->time_stamp = std::chrono::duration_cast<std::chrono::duration<double>>(
					std::chrono::system_clock::now().time_since_epoch()).count(); // 更新时间戳

				// 通知环境更新索引 (传递包含新旧位置和包围盒信息的快照)
				notifyUpdate(snapshot);
			}
			else {
				LOG_TRACE("对象 [{} ID:{}]: 位置未显著改变，跳过更新。", name_, id_);
			}
		}

		/** @brief 设置对象姿态 (可能影响包围盒，会通知环境更新空间索引)。*/
		void EntityObject::setOrientation(const Orientation& orn) {
			Orientation normalized_orn = orn.normalized(); // 先归一化输入姿态
			// 检查姿态是否有显著变化 (与当前已归一化的姿态比较角距离)
			if (state_->orientation.angularDistance(normalized_orn) > Constants::ANGLE_EPSILON) {
				LOG_TRACE("对象 [{} ID:{}]: 姿态改变。", name_, id_);
				// **姿态变化可能影响包围盒，因此需要通知环境更新空间索引**
				EntityStateSnapshot snapshot;
				snapshot.id = id_;
				snapshot.object_type = type_tag_;
				// 记录旧状态信息
				snapshot.old_position = state_->position; // 位置不变，使用WGS84坐标
				if (shape_) snapshot.old_bounds = getBoundingBox(); // 获取旧包围盒
				snapshot.old_status = state_->status;
				snapshot.old_parent_id = parent_id_;
				snapshot.old_capabilities = state_->capabilities;
				// 填充新状态信息
				snapshot.current_position = state_->position; // 位置不变，使用WGS84坐标
				snapshot.current_status = state_->status;
				snapshot.current_parent_id = parent_id_;
				snapshot.current_capabilities = state_->capabilities;
				// 注意: 快照不直接存储姿态，因为空间索引依赖包围盒，
				// notifyUpdate->addIndexes 会使用 obj_ptr->getBoundingBox() 获取基于新姿态的包围盒

				// 更新内部状态
				state_->orientation = normalized_orn; // 设置新姿态
				state_->time_stamp = std::chrono::duration_cast<std::chrono::duration<double>>(
					std::chrono::system_clock::now().time_since_epoch()).count(); // 更新时间戳

				// 通知环境更新索引 (主要是空间索引，因为包围盒可能改变)
				notifyUpdate(snapshot);
			}
			else {
				LOG_TRACE("对象 [{} ID:{}]: 姿态未显著改变，跳过更新。", name_, id_);
			}
		}

		/** @brief 设置对象速度。
		 *        主要影响具有移动策略的对象的状态更新。
		 *        对于没有移动策略的对象，此速度可能用于简单的预测，或不被主动使用。
		 */
		void EntityObject::setVelocity(const Vector3D& vel) {
			// 检查速度是否有显著变化 (使用平方范数避免开方)
			if ((state_->velocity_wgs84 - vel).squaredNorm() > Constants::VELOCITY_EPSILON * Constants::VELOCITY_EPSILON) {
				LOG_TRACE("对象 [{} ID:{}]: 速度改变: ({:.2f},{:.2f},{:.2f}) -> ({:.2f},{:.2f},{:.2f})",
					name_, id_, state_->velocity_wgs84.x(), state_->velocity_wgs84.y(), state_->velocity_wgs84.z(),
					vel.x(), vel.y(), vel.z());
				// 速度变化本身不影响环境索引，只更新内部状态
				state_->velocity_wgs84 = vel; // 设置新速度
				state_->time_stamp = std::chrono::duration_cast<std::chrono::duration<double>>(
					std::chrono::system_clock::now().time_since_epoch()).count(); // 更新时间戳
				// 移动策略 (如果存在) 在其 updateObjectState 方法中会使用这个速度。
			}
			else {
				LOG_TRACE("对象 [{} ID:{}]: 速度未显著改变，跳过更新。", name_, id_);
			}
		}

		/** @brief 设置对象加速度 (不直接更新索引)。*/
		void EntityObject::setAcceleration(const Vector3D& accel) {
			// 检查加速度是否有显著变化
			if ((state_->acceleration - accel).squaredNorm() > Constants::ACCEL_EPSILON * Constants::ACCEL_EPSILON) {
				LOG_TRACE("对象 [{} ID:{}]: 加速度改变: ({:.2f},{:.2f},{:.2f}) -> ({:.2f},{:.2f},{:.2f})",
					name_, id_, state_->acceleration.x(), state_->acceleration.y(), state_->acceleration.z(),
					accel.x(), accel.y(), accel.z());
				state_->acceleration = accel;
				state_->time_stamp = std::chrono::duration_cast<std::chrono::duration<double>>(
					std::chrono::system_clock::now().time_since_epoch()).count();
			}
			else {
				LOG_TRACE("对象 [{} ID:{}]: 加速度未显著改变，跳过更新。", name_, id_);
			}
		}

		/** @brief 设置对象状态字符串，并通知环境更新属性索引。*/
		void EntityObject::setStatus(const std::string& new_status) {
			// 检查状态字符串是否有变化
			if (state_->status != new_status) {
				LOG_DEBUG("对象 [{} ID:{}]: 状态改变: '{}' -> '{}'", name_, id_, state_->status, new_status);
				EntityStateSnapshot snapshot; // 创建快照
				snapshot.id = id_;
				snapshot.object_type = type_tag_;
				// 记录旧状态的关键信息
				snapshot.old_status = state_->status; // 旧状态
				// 填充其他旧状态 (用于可能的索引移除)
				snapshot.old_position = state_->position; // 使用WGS84坐标
				if (shape_) snapshot.old_bounds = getBoundingBox();
				snapshot.old_parent_id = parent_id_;
				snapshot.old_capabilities = state_->capabilities;
				// 填充新状态的关键信息 (用于添加新索引)
				snapshot.current_status = new_status; // 新状态
				// 填充其他当前状态 (这些状态未变，但更新属性索引时可能需要)
				snapshot.current_position = state_->position; // 使用WGS84坐标
				snapshot.current_parent_id = parent_id_;
				snapshot.current_capabilities = state_->capabilities;

				// 先更新内部状态
				state_->status = new_status; // 设置新状态
				state_->time_stamp = std::chrono::duration_cast<std::chrono::duration<double>>(
					std::chrono::system_clock::now().time_since_epoch()).count();

				// 通知环境更新属性索引
				notifyUpdate(snapshot);
			}
			else {
				LOG_TRACE("对象 [{} ID:{}]: 状态未改变 ('{}')，跳过更新。", name_, id_, new_status);
			}
		}

		/** @brief 添加能力，并通知环境更新属性索引。*/
		void EntityObject::addCapability(const std::string& capability) {
			if (capability.empty()) { // 检查能力字符串是否为空
				LOG_WARN("对象 [{} ID:{}]: 尝试添加空能力字符串。", name_, id_);
				return;
			}
			auto& caps = state_->capabilities; // 获取能力列表引用
			// 检查能力是否已存在
			if (std::find(caps.begin(), caps.end(), capability) == caps.end()) { // 如果不存在
				LOG_TRACE("对象 [{} ID:{}]: 添加能力: '{}'", name_, id_, capability);
				EntityStateSnapshot snapshot; // 创建快照
				snapshot.id = id_;
				snapshot.object_type = type_tag_;
				// 记录旧状态的关键信息
				snapshot.old_capabilities = caps; // 旧能力列表
				// 填充其他旧状态
				snapshot.old_position = state_->position; // 使用WGS84坐标
				if (shape_) snapshot.old_bounds = getBoundingBox();
				snapshot.old_status = state_->status;
				snapshot.old_parent_id = parent_id_;
				// 填充新状态的关键信息
				std::vector<std::string> new_caps = caps; // 复制旧列表
				new_caps.push_back(capability); // 在副本中添加新的
				snapshot.current_capabilities = new_caps;
				// 填充其他当前状态
				snapshot.current_position = state_->position; // 使用WGS84坐标
				snapshot.current_status = state_->status;
				snapshot.current_parent_id = parent_id_;

				// 先更新内部状态
				caps.push_back(capability); // 添加到实际列表
				state_->time_stamp = std::chrono::duration_cast<std::chrono::duration<double>>(
					std::chrono::system_clock::now().time_since_epoch()).count();
				// 再通知环境更新索引 (主要是属性索引)
				notifyUpdate(snapshot);
			}
			else { // 如果能力已存在
				LOG_TRACE("对象 [{} ID:{}]: 能力 '{}' 已存在，无需添加。", name_, id_, capability);
			}
		}

		/** @brief 移除能力，并通知环境更新属性索引。*/
		void EntityObject::removeCapability(const std::string& capability) {
			if (capability.empty()) { // 检查能力字符串是否为空
				LOG_WARN("对象 [{} ID:{}]: 尝试移除空能力字符串。", name_, id_);
				return;
			}
			auto& caps = state_->capabilities; // 获取能力列表引用
			// 查找要移除的能力
			auto it = std::remove(caps.begin(), caps.end(), capability);
			if (it != caps.end()) { // 如果找到
				LOG_TRACE("对象 [{} ID:{}]: 移除能力: '{}'", name_, id_, capability);
				EntityStateSnapshot snapshot; // 创建快照
				snapshot.id = id_;
				snapshot.object_type = type_tag_;
				// 记录旧状态的关键信息
				snapshot.old_capabilities = caps; // 旧能力列表
				// 填充其他旧状态
				snapshot.old_position = state_->position; // 使用WGS84坐标
				if (shape_) snapshot.old_bounds = getBoundingBox();
				snapshot.old_status = state_->status;
				snapshot.old_parent_id = parent_id_;
				// 填充新状态的关键信息
				snapshot.current_capabilities = caps; // 复制旧的
				// 从副本中移除能力 (使用 erase-remove idiom)
				snapshot.current_capabilities.erase(
					std::remove(snapshot.current_capabilities.begin(), snapshot.current_capabilities.end(), capability),
					snapshot.current_capabilities.end()
				);
				// 填充其他当前状态
				snapshot.current_position = state_->position; // 使用WGS84坐标
				snapshot.current_status = state_->status;
				snapshot.current_parent_id = parent_id_;

				// 先更新内部状态
				caps.erase(it, caps.end()); // 从实际列表中移除
				state_->time_stamp = std::chrono::duration_cast<std::chrono::duration<double>>(
					std::chrono::system_clock::now().time_since_epoch()).count(); // 更新时间戳
				// 再通知环境更新索引 (主要是属性索引)
				notifyUpdate(snapshot);
			}
			else { // 如果能力不存在
				LOG_TRACE("对象 [{} ID:{}]: 能力 '{}' 不存在，无需移除。", name_, id_, capability);
			}
		}

		/** @brief 更新整个动态状态，并通知环境更新相关索引。*/
		void EntityObject::updateState(const EntityState& new_state) {
			LOG_TRACE("对象 [{} ID:{}]: 开始更新整个动态状态...", name_, id_);
			// --- 1. 创建快照，记录变化前状态 ---
			EntityStateSnapshot snapshot;
			snapshot.id = id_;
			snapshot.object_type = type_tag_;

			// 记录所有可能影响索引的旧状态
			snapshot.old_position = state_->position; // 使用当前WGS84坐标
			if (shape_) snapshot.old_bounds = getBoundingBox(); // 旧包围盒
			snapshot.old_status = state_->status;
			snapshot.old_parent_id = parent_id_;
			snapshot.old_capabilities = state_->capabilities;

			// 记录所有可能影响索引的新状态
			snapshot.current_position = new_state.position; // 使用新的WGS84位置
			snapshot.current_status = new_state.status;     // 使用新状态
			// 父 ID 通常不通过 updateDynamicState 更改，保持当前值
			snapshot.current_parent_id = parent_id_;
			snapshot.current_capabilities = new_state.capabilities; // 使用新能力

			// --- 2. 更新内部状态 ---
			// 注意: 不直接 dynamic_data_ = new_state;
			// 因为我们需要保留某些内部状态或进行归一化等处理
			// 并且需要检查哪些字段实际改变了，以优化通知逻辑 (虽然当前快照传递所有信息)
			// 检查位置是否有显著变化
			double lat_diff = std::abs(state_->position.latitude - new_state.position.latitude);
			double lon_diff = std::abs(state_->position.longitude - new_state.position.longitude);
			double alt_diff = std::abs(state_->position.altitude - new_state.position.altitude);
			bool position_changed = std::max({lat_diff, lon_diff, alt_diff}) > Constants::GEOMETRY_EPSILON;

			bool orientation_changed = state_->orientation.angularDistance(new_state.orientation.normalized()) > Constants::ANGLE_EPSILON;
			bool velocity_changed = (state_->velocity_wgs84 - new_state.velocity_wgs84).squaredNorm() > Constants::VELOCITY_EPSILON * Constants::VELOCITY_EPSILON;
			bool acceleration_changed = (state_->acceleration - new_state.acceleration).squaredNorm() > Constants::ACCEL_EPSILON * Constants::ACCEL_EPSILON;
			bool status_changed = state_->status != new_state.status;
			bool capabilities_changed = state_->capabilities != new_state.capabilities; // 简单比较

			if (position_changed) state_->position = new_state.position;
			if (orientation_changed) state_->orientation = new_state.orientation.normalized();
			if (velocity_changed) state_->velocity_wgs84 = new_state.velocity_wgs84;
			if (acceleration_changed) state_->acceleration = new_state.acceleration;
			if (status_changed) state_->status = new_state.status;
			if (capabilities_changed) state_->capabilities = new_state.capabilities;
			// 时间戳总是更新
			state_->time_stamp = new_state.time_stamp; // 使用传入的时间戳

			// --- 3. 通知环境更新 ---
			// 只有当任何可能影响索引的状态改变时才通知
			if (position_changed || orientation_changed || status_changed || capabilities_changed) {
				notifyUpdate(snapshot);
				LOG_TRACE("对象 [{} ID:{}]: 状态已更新并通知环境。 Pos:{}, Orn:{}, Vel:{}, Acc:{}, Sta:{}, Cap:{}",
					name_, id_, position_changed, orientation_changed, velocity_changed, acceleration_changed, status_changed, capabilities_changed);
			}
			else if (velocity_changed || acceleration_changed) { // 如果只有速度或加速度变化，也记录一下
				LOG_TRACE("对象 [{} ID:{}]: 仅速度或加速度改变，无需通知索引更新。 Pos:{}, Orn:{}, Vel:{}, Acc:{}, Sta:{}, Cap:{}",
					name_, id_, position_changed, orientation_changed, velocity_changed, acceleration_changed, status_changed, capabilities_changed);
			}
			else {
				LOG_TRACE("对象 [{} ID:{}]: 状态未显著改变，无需通知索引更新或记录。 Pos:{}, Orn:{}, Vel:{}, Acc:{}, Sta:{}, Cap:{}",
					name_, id_, position_changed, orientation_changed, velocity_changed, acceleration_changed, status_changed, capabilities_changed);
			}
			LOG_TRACE("对象 [{} ID:{}]: 整个动态状态更新完成。", name_, id_);
		}

		/** @brief 获取此对象对应的 FCL 碰撞对象。 */
		std::shared_ptr<fcl::CollisionObjectd> EntityObject::getFclObject() const {
			if (shape_) {
				// 使用局部坐标进行碰撞检测
				auto ned_pos_opt = getNedPosition();
				if (ned_pos_opt.has_value()) {
					// 创建FCL变换矩阵
					const NedPoint& ned_pos = ned_pos_opt.value();
					fcl::Vector3d translation(ned_pos.north(), ned_pos.east(), ned_pos.down());
					fcl::Transform3d transform = fcl::Transform3d::Identity();
					transform.translation() = translation;

					// 获取FCL几何对象
					auto fcl_geometry = shape_->getFCLGeometry();
					if (fcl_geometry) {
						return std::make_shared<fcl::CollisionObjectd>(fcl_geometry, transform);
					} else {
						LOG_WARN("对象 [{} ID:{}]: 形状无法提供FCL几何对象", name_, id_);
						return nullptr;
					}
				} else {
					LOG_WARN("对象 [{} ID:{}]: 无法获取局部坐标，无法创建FCL碰撞对象", name_, id_);
					return nullptr;
				}
			}
			return nullptr;
		}

		/** @brief 检查对象是否具有特定能力。 */
		bool EntityObject::hasCapability(const std::string& capability) const {
			const auto& caps = state_->capabilities;
			return std::find(caps.begin(), caps.end(), capability) != caps.end();
		}


		// --- 移动策略相关方法实现 ---
		void EntityObject::setMovementStrategy(std::shared_ptr<IMovementStrategy> strategy) {
			LOG_TRACE("对象 [{} ID:{}]: 设置移动策略。", name_, id_);
			if (strategy) movement_strategy_ = std::move(strategy);
			else movement_strategy_ = nullptr;
		}

		std::shared_ptr<IMovementStrategy> EntityObject::getMovementStrategy() const {
			return movement_strategy_;
		}

		void EntityObject::autoStateUpdate(Time dt) {
			LOG_TRACE("对象 [{} ID:{}] autoStateUpdate 调用 (dt={:.4f})", name_, id_, dt);
			if (movement_strategy_) {
				auto env = getEnvironment();
				if (!env) {
					LOG_ERROR("对象 [{} ID:{}] autoStateUpdate 失败：无法获取环境实例", name_, id_);
					return;
				}
				LOG_TRACE("对象 [{} ID:{}] 调用移动策略 '{}' 的 updateObjectState",
					name_, id_, typeid(*movement_strategy_).name());
				// 创建更新前的状态快照
				// EntityStateSnapshot state_before = createSnapshot();

				movement_strategy_->updateState(*this, dt);

				// 检查状态是否实际改变 (与快照比较)
				EntityStateSnapshot dummy_snapshot; // Create a dummysnapshot for now
				dummy_snapshot.id = id_;
				dummy_snapshot.object_type = type_tag_;
				// Populate with current state for both old and new to signify an update happened
				dummy_snapshot.old_position = state_->position; // 使用WGS84坐标
				if (shape_) dummy_snapshot.old_bounds = getBoundingBox();
				dummy_snapshot.old_status = state_->status;
				dummy_snapshot.old_parent_id = parent_id_;
				dummy_snapshot.old_capabilities = state_->capabilities;
				dummy_snapshot.current_position = state_->position; // 使用WGS84坐标
				dummy_snapshot.current_status = state_->status;
				dummy_snapshot.current_parent_id = parent_id_;
				dummy_snapshot.current_capabilities = state_->capabilities;
				notifyUpdate(dummy_snapshot);

			}
			else {
				LOG_TRACE("对象 [{} ID:{}] 无移动策略，autoStateUpdate 无操作。", name_, id_);
			}
		}

		// --- 预测相关方法实现 ---
		WGS84Point EntityObject::predictWGS84Position(Time dt) const {
			LOG_TRACE("对象 [{} ID:{}]: 请求预测 dt={:.4f}s 后的WGS84位置。", name_, id_, dt);
			if (movement_strategy_) {
				LOG_DEBUG("对象 [{} ID:{}]: 使用移动策略进行WGS84位置预测。", name_, id_);
				return movement_strategy_->predictWGS84Position(*this, dt);
			}
			else {
				LOG_DEBUG("对象 [{} ID:{}]: 无移动策略，使用ECEF坐标系进行简单WGS84位置预测。", name_, id_);
				// 使用ECEF坐标系进行位置预测
				EcefPoint current_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(state_->position);

				// 如果有速度信息，使用ECEF速度进行预测
				if (state_->velocity_wgs84.norm() > Constants::VELOCITY_EPSILON) {
					// 在ECEF坐标系中进行位置预测（假设velocity_wgs84是ECEF坐标系中的速度）
					EcefPoint predicted_ecef(current_ecef.toVector3D() + state_->velocity_wgs84 * dt);
					WGS84Point predicted_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(predicted_ecef);

					LOG_TRACE("对象 [{} ID:{}]: 预测WGS84位置: {}", name_, id_, predicted_wgs84.toString());
					return predicted_wgs84;
				}

				// 无速度信息时，返回当前WGS84位置
				LOG_TRACE("对象 [{} ID:{}]: 无速度信息，返回当前WGS84位置: {}", name_, id_, state_->position.toString());
				return state_->position;
			}
		}

		EcefPoint EntityObject::predictEcefPosition(Time dt) const {
			LOG_TRACE("对象 [{} ID:{}]: 请求预测 dt={:.4f}s 后的ECEF位置。", name_, id_, dt);
			if (movement_strategy_) {
				LOG_DEBUG("对象 [{} ID:{}]: 使用移动策略进行ECEF位置预测。", name_, id_);
				return movement_strategy_->predictEcefPosition(*this, dt);
			}
			else {
				LOG_DEBUG("对象 [{} ID:{}]: 无移动策略，使用ECEF坐标系进行简单ECEF位置预测。", name_, id_);
				// 直接使用ECEF坐标系进行位置预测
				EcefPoint current_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(state_->position);

				// 如果有速度信息，使用ECEF速度进行预测
				if (state_->velocity_wgs84.norm() > Constants::VELOCITY_EPSILON) {
					// 在ECEF坐标系中进行位置预测（假设velocity_wgs84是ECEF坐标系中的速度）
					EcefPoint predicted_ecef(current_ecef.toVector3D() + state_->velocity_wgs84 * dt);

					LOG_TRACE("对象 [{} ID:{}]: 预测ECEF位置: [{:.2f},{:.2f},{:.2f}]", name_, id_,
						predicted_ecef.x(), predicted_ecef.y(), predicted_ecef.z());
					return predicted_ecef;
				}

				// 无速度信息时，返回当前ECEF位置
				LOG_TRACE("对象 [{} ID:{}]: 无速度信息，返回当前ECEF位置: [{:.2f},{:.2f},{:.2f}]", name_, id_,
					current_ecef.x(), current_ecef.y(), current_ecef.z());
				return current_ecef;
			}
		}

		BoundingBox EntityObject::predictBoundingBox(Time t) const {
			LOG_TRACE("EntityObject '{}' (ID: '{}'): 调用 predictedBoundingBox(t={:.2f})。", name_, id_, t);

			// 获取预测的WGS84位置，然后直接转换为ECEF进行几何运算
			WGS84Point predicted_wgs84 = predictWGS84Position(t);
			EcefPoint predicted_ecef_pos = NSUtils::CoordinateConverter::wgs84ToECEF(predicted_wgs84);

			if (shape_) {
				// 使用预测的ECEF位置和当前姿态计算包围盒
				// 注意：如果移动策略也会改变姿态，这里的预测可能需要更复杂

				// 创建FCL变换矩阵（直接使用ECEF坐标）
				fcl::Vector3d translation(predicted_ecef_pos.x(), predicted_ecef_pos.y(), predicted_ecef_pos.z());
				Transform3d transform = Transform3d::Identity();
				transform.translation() = translation;

				// 直接返回FCL AABB（现在BoundingBox就是fcl::AABBd）
				return shape_->getAABB(transform);
			}
			LOG_WARN("对象 [{} ID:{}]: 无形状，无法计算预测包围盒，返回无效包围盒。", name_, id_);
			return BoundingBox(); // 无形状则返回无效包围盒
		}

		void EntityObject::populateSnapshot(EntityStateSnapshot& snapshot, const std::optional<EntityState>& old_state_if_available) const {
			LOG_TRACE("对象 [{} ID:{}]: 正在填充 EntityStateSnapshot...", name_, id_);

			snapshot.id = id_;
			snapshot.object_type = type_tag_;

			// 填充当前状态
			if (state_) {
				snapshot.current_position = state_->position; // 使用WGS84坐标
				snapshot.current_status = state_->status;
				snapshot.current_capabilities = state_->capabilities;
				if (shape_) {
					snapshot.current_bounds = getBoundingBox(); // 基于当前状态的包围盒
				}
			} else {
				LOG_WARN("对象 [{} ID:{}]: state_ 为空，无法完全填充快照的当前状态部分。", name_, id_);
			}
			snapshot.current_parent_id = parent_id_; // 父ID直接从EntityObject获取

			// 填充旧状态 (如果提供了)
			if (old_state_if_available.has_value()) {
				const auto& old_state = old_state_if_available.value();
				snapshot.old_position = old_state.position; // 使用WGS84坐标
				snapshot.old_status = old_state.status;
				snapshot.old_capabilities = old_state.capabilities;
				// old_bounds 的计算比较复杂，因为它依赖于旧的位置、姿态和形状。
				// 如果形状也可能改变，那么这里需要更复杂的逻辑来获取正确的旧包围盒。
				// 暂时假设形状不变，使用旧状态的位置/姿态和当前形状计算。
				// 更鲁棒的做法是，如果形状可能在旧状态和新状态之间改变，那么EntityStateSnapshot也应该记录旧形状信息。
				if (shape_) {
					// 注意：getBoundingBox 需要局部坐标，但这里我们需要将WGS84坐标转换为局部坐标
					// 这需要坐标转换器，暂时使用当前包围盒作为近似
					snapshot.old_bounds = getBoundingBox(); // 使用当前包围盒作为近似
				} else {
					snapshot.old_bounds = std::nullopt; // 如果没有形状，就没有旧包围盒
				}
				// old_parent_id 通常在父子关系变更时由更上层逻辑直接设置到快照中，
				// 或者 EntityObject 需要追踪其先前的父对象ID。
				// 此处暂不从 old_state 推断 old_parent_id。
				// old_object_type 也是在对象类型变更（例如替换）时由外部逻辑设置。
			} else {
				// 如果没有提供 old_state，则 old_* 字段保持其默认的 std::nullopt
				LOG_TRACE("对象 [{} ID:{}]: 未提供 old_state，快照的 old_* 字段将为 nullopt。", name_, id_);
			}

			LOG_TRACE("对象 [{} ID:{}]: EntityStateSnapshot 填充完成。", name_, id_);
		}

		// --- 任务空间管理方法实现 ---
		void EntityObject::setTaskSpace(const std::string& task_space_id) {
			task_space_id_ = task_space_id;
			LOG_DEBUG("对象 [{} ID:{}]: 设置任务空间ID为 '{}'", name_, id_, task_space_id);
		}

		void EntityObject::onTaskSpaceOriginChanged(const std::string& task_space_id, const WGS84Point& new_origin) {
			LOG_DEBUG("对象 [{} ID:{}]: 收到任务空间 '{}' 基准点变更通知，新基准点: {}",
				name_, id_, task_space_id, new_origin.toString());

			// 如果这是当前使用的任务空间，可能需要重新计算一些缓存的局部坐标
			if (task_space_id == task_space_id_) {
				LOG_DEBUG("对象 [{} ID:{}]: 当前使用的任务空间基准点已变更，局部坐标计算将使用新基准点", name_, id_);
				// 注意：实际的坐标转换是在需要时动态计算的，所以这里不需要特殊处理
				// 如果有缓存的局部坐标，应该在这里清除缓存
			}
		}

	} // namespace NSCore
} // namespace NSDrones

// 在文件末尾包含Environment的完整定义，以支持notifyObjectUpdate调用
#include "environment/environment.h"