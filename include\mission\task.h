// include/mission/task.h
#pragma once

#include "core/types.h"           
#include "mission/capability_requirement.h" 
#include "mission/task_targets.h"    
#include "mission/task_strategies.h" 
#include "mission/task_params.h" 
#include "utils/logging.h"          
#include <string>
#include <vector>
#include <memory>                   
#include <map>
#include <optional>
#include <stdexcept>                
#include <typeinfo>                 

namespace NSDrones {
	namespace NSMission {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class Task
		 * @brief 代表一个任务实例 (具体类，不再是抽象基类)。
		 *        包含任务的基本信息（ID、类型、目标、需求、策略）和指向特定参数的指针。
		 */
		class Task {
		public:
			/**
			* @brief Task 构造函数。
			* @param id 任务 ID。
			* @param type 任务类型。
			* @param target 任务目标 (TaskTargetVariant)。
			* @param requirements 能力需求。
			* @param strategies (可选) 应用于此任务的策略集合。
			* @param task_params 指向任务特定参数结构体的共享指针 (BaseTaskParamsPtr)。
			*                    **必须**提供，即使参数为空也应传入 make_shared<BaseTaskParams>()。
			* @param description (可选) 任务描述。
			* @throws DroneException 如果 id、target、requirements 或 task_params 无效。
			*/
			Task(ObjectID id, TaskType type,
				TaskTargetVariant target, // 使用 Variant 存储目标
				CapabilityRequirement requirements,
				ITaskStrategyMap strategies, // 存储策略
				ITaskParamsPtr task_params, // 存储特定参数
				std::string description = "");

			/** @brief 虚析构函数 */
			virtual ~Task() = default;

			// --- 禁止拷贝和移动 ---
			Task(const Task&) = delete;
			Task& operator=(const Task&) = delete;
			Task(Task&&) = delete;
			Task& operator=(Task&&) = delete;

			// --- 核心属性访问 ---
			const ObjectID& getId() const { return id_; }
			TaskType getType() const { return type_; }
			const TaskTargetVariant& getTarget() const { return target_; } // 获取任务目标 Variant
			const CapabilityRequirement& getRequirements() const { return requirements_; } // 获取能力需求
			const ITaskStrategyMap& getStrategies() const { return strategies_; } // 获取策略集合
			const std::string& getDescription() const { return description_; }

			// --- 快捷方式获取常用策略 ---
			/** @brief 获取高度策略  */
			std::optional<AltitudeStrategy> getAltitudeStrategy() const;
			/** @brief 获取速度策略 */
			std::optional<SpeedStrategy> getSpeedStrategy() const;
			/** @brief 获取路径约束策略 */
			std::optional<PathConstraintStrategy> getPathConstraintStrategy() const;
			/** @brief 获取一个指定类型的策略指针。 */
			template <typename T>
			std::shared_ptr<const T> getStrategyByType() const {
				std::string strategy_key; // 需要确定如何从类型 T 获取 key
				if constexpr (std::is_same_v<T, AltitudeStrategy>) strategy_key = "Altitude";
				else if constexpr (std::is_same_v<T, SpeedStrategy>) strategy_key = "Speed";
				else if constexpr (std::is_same_v<T, PathConstraintStrategy>) strategy_key = "PathConstraint";
				else if constexpr (std::is_same_v<T, PayloadControlStrategy>) strategy_key = "PayloadControl";
				// ... 其他策略 ...
				else {
					LOG_WARN("任务 [{}] 获取未知策略类型 '{}' 的键失败。", id_, typeid(T).name());
					return nullptr;
				}

				auto it = strategies_.find(strategy_key);
				if (it != strategies_.end()) {
					return std::dynamic_pointer_cast<const T>(it->second);
				}
				return nullptr; // 未找到该类型的策略
			}

			/**
			 * @brief 获取任务的期望速度 (从策略或默认值)。
			 * @param default_speed 如果未找到速度策略，使用的默认速度。
			 * @return 期望速度 (m/s)。
			 */
			double getDesiredSpeed(double default_speed = 10.0) const;
			/**
			 * @brief 获取任务的期望高度值 (从策略或默认值)。
			 * @param default_altitude 如果未找到高度策略，使用的默认高度值。
			 * @return 期望高度值 (米)。
			 */
			double getDesiredAltitude(double default_altitude = 100.0) const;
			/**
			 * @brief 获取任务的期望高度类型 (从策略或默认值)。
			 * @param default_type 如果未找到高度策略，使用的默认高度类型。
			 * @return 高度类型。
			 */
			AltitudeType getDesiredHeightType(AltitudeType default_type = AltitudeType::ABOVE_GROUND_LEVEL) const;


			/**
			 * @brief 获取指向具体任务参数结构体的指针。
			 * @tparam T 期望的具体参数结构体类型 (例如 LoiterPointTaskParams)。
			 * @return 指向 T 的 const 共享指针，如果类型不匹配或内部指针为空则返回 nullptr。
			 */
			template <typename T>
			std::shared_ptr<const T> getTaskParameters() const {
				// 先检查基类指针是否有效
				if (!params_) return nullptr;
				// 尝试进行动态类型转换
				return std::dynamic_pointer_cast<const T>(params_);
			}
			/** @brief 获取指向任务参数基类的指针 (const 版本)。 */
			ConstITaskParamsPtr getBaseTaskParameters() const { return params_; }

			// --- 期望状态设置 (通过修改策略实现) ---
			/** @brief 添加或更新策略。 */
			void setStrategy(ITaskStrategyPtr strategy);
			/** @brief 移除指定类型的策略。 */
			void removeStrategy(const std::string& strategy_type);
			void setDescription(const std::string& desc) { description_ = desc; }

		protected:
			ObjectID id_;						// 任务 ID
			TaskType type_;						// 任务类型
			TaskTargetVariant target_;			// 任务目标
			CapabilityRequirement requirements_; // 能力需求
			ITaskStrategyMap strategies_;       // 执行策略集合
			ITaskParamsPtr params_;				// 指向特定参数的指针
			std::string description_;			// 任务描述
		};

		// 定义 Task 的智能指针类型
		using TaskPtr = std::shared_ptr<Task>;
		using ConstTaskPtr = std::shared_ptr<const Task>;

	} // namespace NSMission
} // namespace NSDrones