// include/algorithm/allocator/itask_allocator.h
#pragma once

#include "core/types.h"       
#include "mission/mission.h"   
#include "uav/uav.h"           
#include <vector>
#include <map>
#include <memory>
#include <optional>
#include <string> 
#include "nlohmann/json.hpp"
#include "utils/logging.h"
#include "uav/uav_fwd.h"
#include "uav/uav_types.h" 

namespace NSDrones { namespace NSParams { class ParamValues; } }

namespace NSDrones {
	namespace NSAlgorithm {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @struct TaskAllocationResult
		 * @brief 存储任务分配的结果。
		 */
		struct TaskAllocationResult {
			bool success = false; // 分配是否成功
			std::string message = ""; // 相关消息或失败原因
			// 任务 ID 到分配的无人机 ID 列表的映射
			std::map<ObjectID, std::vector<ObjectID>> allocation;
			// (可选) 未分配的任务 ID 列表
			std::vector<ObjectID> unallocated_tasks;
		};

		/**
		 * @class ITaskAllocator
		 * @brief 任务分配器接口 (抽象基类)。
		 *
		 * 负责根据任务需求和可用无人机资源，决定哪个（些）无人机执行哪个任务。
		 */
		class ITaskAllocator {
		public:
			ITaskAllocator() = default;
			virtual ~ITaskAllocator() = default;

			ITaskAllocator(const ITaskAllocator&) = default;
			ITaskAllocator& operator=(const ITaskAllocator&) = default;
			ITaskAllocator(ITaskAllocator&&) = default;
			ITaskAllocator& operator=(ITaskAllocator&&) = default;

		public:
			/**
			 * @brief (纯虚函数) 为给定的任务计划分配无人机。
			 * @param mission 要分配的任务计划。
			 * @param available_uavs 当前可用的无人机列表。
			 * @param current_uav_states (可选) 无人机当前状态，用于更智能的分配。
			 * @return 任务分配结果。
			 */
			virtual TaskAllocationResult allocate(
				const NSMission::Mission& mission,
				const std::vector<NSUav::UavPtr>& available_uavs,
				const std::map<ObjectID, NSUav::UavState>& current_uav_states = {}
			) = 0;

			/**
			 * @brief (可选) 允许人工调整分配结果。
			 *        基类提供默认实现（不进行任何调整）。
			 * @param initial_allocation 算法生成的初始分配结果。
			 * @return 调整后的分配结果。
			 */
			virtual TaskAllocationResult adjustAllocation(TaskAllocationResult initial_allocation) {
				// 假设 LOG_DEBUG 宏可用
				LOG_DEBUG("任务分配器：默认的人工调整接口被调用，未进行调整。"); 
				return initial_allocation; // 默认不调整
			}
		};

		using ITaskAllocatorPtr = std::shared_ptr<ITaskAllocator>;

	} // namespace NSAlgorithm
} // namespace NSDrones