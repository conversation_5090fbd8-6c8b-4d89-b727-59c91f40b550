// include/environment/geometry/geometry_manager.h
#pragma once

#include "core/types.h"
#include <memory>
#include <vector>
#include <optional>

namespace NSDrones {
    namespace NSEnvironment {
        class CoordinateManager;
    }
}

namespace NSDrones {
    namespace NSEnvironment {
        using namespace NSDrones::NSCore;
        /**
         * @class GeometryManager
         * @brief 统一几何运算管理器 - 完全替代utils/geometry_utils
         *
         * 设计原则：
         * 1. **统一接口**：所有几何运算的唯一入口，替代分散的几何计算
         * 2. **自适应精度**：基于AdaptiveCoordinate自动选择最优算法
         * 3. **简洁易用**：隐藏内部复杂性，用户只需提供WGS84坐标
         * 4. **批量优化**：支持批量操作，提升性能
         */
        class GeometryManager {
        public:
            /**
             * @brief 构造函数
             * @param coord_manager 坐标系统管理器，用于高精度坐标转换
             */
            explicit GeometryManager(std::shared_ptr<CoordinateManager> coord_manager);

            //=== 距离计算 ===//
            /**
             * @brief 计算两个WGS84点之间的精确距离
             * @param p1 第一个WGS84点
             * @param p2 第二个WGS84点
             * @return 距离(米)，自动选择最优算法保证精度
             */
            double calculateDistance(const WGS84Point& p1, const WGS84Point& p2) const;

            /**
             * @brief 批量计算距离（性能优化版本）
             * @param points1 第一组点（WGS84）
             * @param points2 第二组点（WGS84）
             * @return 对应的距离列表，自动优化批量计算
             */
            std::vector<double> batchCalculateDistance(
                const std::vector<WGS84Point>& points1,
                const std::vector<WGS84Point>& points2) const;

            //=== 半径查询 ===//

            /**
             * @brief 在指定半径内查找点
             * @param center 中心点（WGS84）
             * @param radius_m 半径(米)
             * @param candidates 候选点列表（WGS84）
             * @return 在半径内的点的索引列表，自动优化查询算法
             */
            std::vector<size_t> findPointsInRadius(
                const WGS84Point& center,
                double radius_m,
                const std::vector<WGS84Point>& candidates) const;

            //=== 面积计算 ===//

            /**
             * @brief 计算多边形面积
             * @param polygon WGS84坐标的多边形顶点
             * @return 面积(平方米)，自动选择最优算法保证精度
             */
            double calculatePolygonArea(const std::vector<WGS84Point>& polygon) const;

            //=== 几何关系判断 ===//

            /**
             * @brief 判断点是否在多边形内
             * @param point 待判断的点（WGS84）
             * @param polygon 多边形顶点（WGS84）
             * @return 如果点在多边形内返回true
             */
            bool isPointInPolygon(const WGS84Point& point, const std::vector<WGS84Point>& polygon) const;

            /**
             * @brief 判断两个WGS84边界框是否相交
             * @param box1 第一个边界框
             * @param box2 第二个边界框
             * @return 如果相交返回true
             */
            bool doBoundingBoxesIntersect(const WGS84BoundingBox& box1, const WGS84BoundingBox& box2) const;

            //=== 路径和轨迹分析 ===//

            /**
             * @brief 计算路径总长度
             * @param waypoints 路径点列表（WGS84）
             * @return 路径总长度(米)，自动优化计算精度
             */
            double calculatePathLength(const std::vector<WGS84Point>& waypoints) const;

            /**
             * @brief 计算点到线段的最短距离
             * @param point 点（WGS84）
             * @param line_start 线段起点（WGS84）
             * @param line_end 线段终点（WGS84）
             * @return 最短距离(米)
             */
            double calculatePointToLineDistance(
                const WGS84Point& point,
                const WGS84Point& line_start,
                const WGS84Point& line_end) const;

            /**
             * @brief 计算多边形质心
             * @param polygon WGS84坐标的多边形顶点
             * @return 质心位置（WGS84）
             */
            WGS84Point calculatePolygonCentroid(const std::vector<WGS84Point>& polygon) const;

            /**
             * @brief 检查两个多边形是否相交
             * @param poly1 第一个多边形（WGS84）
             * @param poly2 第二个多边形（WGS84）
             * @return 如果相交返回true
             */
            bool doPolygonsIntersect(const std::vector<WGS84Point>& poly1,
                                   const std::vector<WGS84Point>& poly2) const;

            /**
             * @brief 三角剖分多边形
             * @param polygon 多边形顶点（WGS84）
             * @param holes 孔洞列表（可选）
             * @return 三角形索引列表
             */
            std::vector<TriangleIndices> triangulatePolygon(
                const std::vector<WGS84Point>& polygon,
                const std::vector<std::vector<WGS84Point>>& holes = {}) const;

            /**
             * @brief 生成网格点
             * @param bounds 边界框（WGS84）
             * @param spacing_m 网格间距（米）
             * @return 网格点列表（WGS84）
             */
            std::vector<WGS84Point> generateGridPoints(const WGS84BoundingBox& bounds,
                                                      double spacing_m) const;

            /**
             * @brief 计算点集的边界框
             * @param points 点集（WGS84）
             * @return 边界框（WGS84）
             */
            WGS84BoundingBox calculateBoundingBox(const std::vector<WGS84Point>& points) const;

            /**
             * @brief 简化多边形（Douglas-Peucker算法）
             * @param polygon 原始多边形（WGS84）
             * @param tolerance_m 简化容差（米）
             * @return 简化后的多边形（WGS84）
             */
            std::vector<WGS84Point> simplifyPolygon(const std::vector<WGS84Point>& polygon,
                                                   double tolerance_m) const;

            /**
             * @brief 计算两个向量之间的夹角
             * @param v1_start 向量1起点（WGS84）
             * @param v1_end 向量1终点（WGS84）
             * @param v2_start 向量2起点（WGS84）
             * @param v2_end 向量2终点（WGS84）
             * @return 夹角（弧度）
             */
            double calculateAngleBetweenVectors(const WGS84Point& v1_start, const WGS84Point& v1_end,
                                              const WGS84Point& v2_start, const WGS84Point& v2_end) const;

            //=== 任务规划专用几何计算 ===//

            /**
             * @brief 检查点集是否共面
             * @param points 点集（WGS84）
             * @param tolerance_m 容差（米）
             * @return 如果点集共面返回true
             */
            bool checkPointsCoplanar(const std::vector<WGS84Point>& points, double tolerance_m = 1.0) const;

            /**
             * @brief 生成区域扫描路径
             * @param boundary 边界点（WGS84）
             * @param strip_width 扫描带宽（米）
             * @param overlap_ratio 重叠率（0-1）
             * @param scan_angle_deg 扫描角度（度）
             * @param height_above_plane 离面高度（米）
             * @return 扫描路径点列表（WGS84）
             */
            std::vector<WGS84Point> generateScanPath(
                const std::vector<WGS84Point>& boundary,
                double strip_width,
                double overlap_ratio,
                double scan_angle_deg,
                double height_above_plane = 0.0) const;

            /**
             * @brief 生成圆柱扫描路径
             * @param center 圆柱底面中心（WGS84）
             * @param radius 半径（米）
             * @param height 高度（米）
             * @param vertical_spacing 垂直间距（米）
             * @param points_per_circle 每圈点数
             * @param clockwise 是否顺时针
             * @return 扫描路径点列表（WGS84）
             */
            std::vector<WGS84Point> generateCylinderScanPath(
                const WGS84Point& center,
                double radius,
                double height,
                double vertical_spacing,
                int points_per_circle = 36,
                bool clockwise = true) const;

            /**
             * @brief 计算从一点到另一点的方位角（使用 GeographicLib）
             * @param from 起始点（WGS84）
             * @param to 目标点（WGS84）
             * @return 方位角（度，0-360）
             */
            double calculateBearing(const WGS84Point& from, const WGS84Point& to) const;

            /**
             * @brief 根据起始点、方位角和距离计算目标点（使用 GeographicLib）
             * @param start 起始点（WGS84）
             * @param bearing_deg 方位角（度）
             * @param distance_m 距离（米）
             * @return 目标点（WGS84）
             */
            WGS84Point calculateDestination(const WGS84Point& start, double bearing_deg, double distance_m) const;

            /**
             * @brief 生成两点间的大圆路径（使用 GeographicLib）
             * @param start 起始点（WGS84）
             * @param end 终点（WGS84）
             * @param max_segment_length_m 最大分段长度（米）
             * @return 路径点列表（WGS84）
             */
            std::vector<WGS84Point> generateGreatCirclePath(const WGS84Point& start,
                                                           const WGS84Point& end,
                                                           double max_segment_length_m = 1000.0) const;

            //=== 局部坐标几何工具（用于形状和碰撞检测） ===//

            /**
             * @brief 检查ECEF点是否在ECEF多边形内部（投影到XY平面进行2D检测）
             * @param ecef_point 待判断的ECEF点
             * @param ecef_polygon ECEF多边形顶点
             * @param tolerance 容差
             * @return 如果点在多边形内返回true
             */
            static bool isPointInPolygonECEF(const EcefPoint& ecef_point,
                                           const std::vector<EcefPoint>& ecef_polygon,
                                           double tolerance = Constants::GEOMETRY_EPSILON);

            /**
             * @brief 检查两条线段是否相交（局部坐标版本）
             * @param p1 第一条线段的起点
             * @param p2 第一条线段的终点
             * @param p3 第二条线段的起点
             * @param p4 第二条线段的终点
             * @param include_endpoints 是否包含端点
             * @param tolerance 容差
             * @return 如果线段相交则返回true
             */
            static bool isSegmentsIntersect2D(const EcefPoint& ecef_p1, const EcefPoint& ecef_p2,
                                            const EcefPoint& ecef_p3, const EcefPoint& ecef_p4,
                                            bool include_endpoints = true,
                                            double tolerance = Constants::GEOMETRY_EPSILON);

            //=== 形状几何检测（局部坐标版本） ===//

            /**
             * @brief 检查ECEF点是否在球体内
             * @param ecef_point 待检查的ECEF点
             * @param sphere_center 球体中心（ECEF坐标）
             * @param radius 球体半径
             * @param tolerance 容差
             * @return 如果点在球体内返回true
             */
            static bool isPointInSphere(const EcefPoint& ecef_point, const EcefPoint& sphere_center,
                                      double radius, double tolerance = Constants::GEOMETRY_EPSILON);

            /**
             * @brief 检查ECEF点是否在盒子内
             * @param ecef_point 待检查的ECEF点
             * @param box_center 盒子中心（ECEF坐标）
             * @param box_dimensions 盒子尺寸（长宽高）
             * @param tolerance 容差
             * @return 如果点在盒子内返回true
             */
            static bool isPointInBox(const EcefPoint& ecef_point, const EcefPoint& box_center,
                                   const Vector3D& box_dimensions, double tolerance = Constants::GEOMETRY_EPSILON);

            /**
             * @brief 检查ECEF线段是否与球体相交
             * @param line_start 线段起点（ECEF坐标）
             * @param line_end 线段终点（ECEF坐标）
             * @param sphere_center 球体中心（ECEF坐标）
             * @param radius 球体半径
             * @param tolerance 容差
             * @return 如果线段与球体相交返回true
             */
            static bool isLineIntersectSphere(const EcefPoint& line_start, const EcefPoint& line_end,
                                            const EcefPoint& sphere_center, double radius,
                                            double tolerance = Constants::GEOMETRY_EPSILON);

            /**
             * @brief 检查ECEF线段是否与盒子相交
             * @param line_start 线段起点（ECEF坐标）
             * @param line_end 线段终点（ECEF坐标）
             * @param box_center 盒子中心（ECEF坐标）
             * @param box_dimensions 盒子尺寸（长宽高）
             * @param tolerance 容差
             * @return 如果线段与盒子相交返回true
             */
            static bool isLineIntersectBox(const EcefPoint& line_start, const EcefPoint& line_end,
                                         const EcefPoint& box_center, const Vector3D& box_dimensions,
                                         double tolerance = Constants::GEOMETRY_EPSILON);

            /**
             * @brief 对ECEF多边形进行三角剖分
             * @param ecef_polygon ECEF多边形顶点
             * @return 三角形索引列表
             */
            static std::vector<TriangleIndices> triangulatePolygon2D(const std::vector<EcefPoint>& ecef_polygon);

        private:
            std::shared_ptr<CoordinateManager> coord_manager_;  ///< 坐标系统管理器

            //=== 内部辅助方法 ===//

            /**
             * @brief 根据数据范围选择最优的使用提示
             * @param points 输入点集
             * @return 使用提示字符串
             */
            std::string selectOptimalUsageHint(const std::vector<WGS84Point>& points) const;

            /**
             * @brief 计算数据范围的粗略估算
             * @param points 输入点集
             * @return 最大距离(公里)
             */
            double estimateDataRange(const std::vector<WGS84Point>& points) const;

            //=== 内部几何计算方法（基于ECEF坐标系） ===//

            /**
             * @brief 在ECEF坐标系中计算多边形面积（投影到局部平面）
             * @param polygon_ecef ECEF坐标的多边形顶点
             * @return 面积（平方米）
             */
            double calculatePolygonAreaECEF(const std::vector<EcefPoint>& polygon_ecef) const;

            /**
             * @brief 在ECEF坐标系中判断点是否在多边形内（投影到局部平面）
             * @param point_ecef ECEF坐标的点
             * @param polygon_ecef ECEF坐标的多边形顶点
             * @return 如果点在多边形内返回true
             */
            bool isPointInPolygonECEF(const EcefPoint& point_ecef, const std::vector<EcefPoint>& polygon_ecef) const;

            /**
             * @brief 在ECEF坐标系中计算点到线段距离
             * @param point_ecef ECEF坐标的点
             * @param line_start_ecef ECEF坐标的线段起点
             * @param line_end_ecef ECEF坐标的线段终点
             * @return 距离（米）
             */
            double calculatePointToLineDistanceECEF(
                const EcefPoint& point_ecef,
                const EcefPoint& line_start_ecef,
                const EcefPoint& line_end_ecef) const;

            /**
             * @brief 在ECEF坐标系中计算多边形质心
             * @param polygon_ecef ECEF坐标的多边形顶点
             * @return 质心（ECEF坐标）
             */
            EcefPoint calculatePolygonCentroidECEF(const std::vector<EcefPoint>& polygon_ecef) const;
        };

    } // namespace NSEnvironment
} // namespace NSDrones
