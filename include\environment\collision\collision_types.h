#pragma once

#include "core/types.h"
#include "utils/object_id.h" // For ObjectID
#include "environment/coordinate/coordinate_converter.h" // For CoordinateConverter
#include <variant>
#include <vector>
#include <string>
#include <unordered_set>
#include <memory>

namespace NSDrones {
	namespace NSEnvironment {

		/**
		* @struct CollisionOptions
		* @brief 统一的碰撞检测选项结构，用于配置所有类型的碰撞检测行为
		*/
		struct CollisionOptions {
			// 通用选项
			double safetyMargin = 0.0;                              ///< 安全边距，用于碰撞检测的额外距离
			bool checkDynamicObjects = true;                        ///< 是否检查动态对象
			bool checkStaticObjects = true;                         ///< 是否检查静态对象
			std::unordered_set<ObjectID> ignoredObjectIds;  ///< 忽略的对象ID集合

			// 时间相关选项
			Time timePoint = 0.0;                           ///< 检测的时间点
			int timeSteps = 5;                                      ///< 用于时空碰撞检测的离散步数
			double timeHorizon = 1.0;                               ///< 未来时间窗口大小（秒）

			// 碰撞检测细节选项
			bool enableContact = true;                              ///< 是否计算接触点信息
			bool enableDistance = true;                             ///< 是否计算最短距离
			bool enableNearestPoints = true;                        ///< 是否计算最近点
			bool enableContinuousDetection = false;                 ///< 是否启用连续碰撞检测
			uint32_t maxContacts = 10;                              ///< 最大接触点数量

			// 性能相关选项
			double collisionEpsilon = 1e-6;                         ///< 碰撞检测精度阈值
			uint32_t maxIterations = 20;                            ///< 最大迭代次数（适用于迭代算法）
			bool useSpacePartitioning = true;                       ///< 是否使用空间分区加速

			// 缓存选项
			bool enableResultCache = true;                          ///< 是否启用结果缓存
			double cacheValidTime = 0.05;                           ///< 缓存有效时间（秒）

			/**
			 * @brief 默认构造函数
			 */
			CollisionOptions() = default;

			/**
			 * @brief 创建仅进行碰撞检测的选项（不计算距离和接触点）
			 * @return 优化的碰撞选项
			 */
			static CollisionOptions createCollisionOnlyOptions() {
				CollisionOptions options;
				options.enableContact = false;
				options.enableDistance = false;
				options.enableNearestPoints = false;
				return options;
			}

			/**
			 * @brief 创建详细碰撞检测选项
			 * @return 详细的碰撞选项
			 */
			static CollisionOptions createDetailedOptions() {
				CollisionOptions options;
				options.enableContact = true;
				options.enableDistance = true;
				options.enableNearestPoints = true;
				options.maxContacts = 30;
				return options;
			}

			/**
			 * @brief 创建用于连续碰撞检测的选项
			 * @param horizon 时间窗口大小（秒）
			 * @param steps 离散步数
			 * @return 连续碰撞检测选项
			 */
			static CollisionOptions createContinuousOptions(double horizon = 1.0, int steps = 10) {
				CollisionOptions options;
				options.enableContinuousDetection = true;
				options.timeHorizon = horizon;
				options.timeSteps = steps;
				return options;
			}
		};

		/**
		 * @struct CollisionQuery
		 * @brief 定义碰撞检测查询的结构。
		 *
		 * 包含查询的类型和与该类型相关的具体数据。
		 */
		struct CollisionQuery {
			/**
			 * @enum QueryType
			 * @brief 定义了不同类型的碰撞查询。
			 */
			enum class QueryType {
				UNKNOWN = 0,                  ///< 未知或未初始化的查询类型
				POINT_ENVIRONMENT,            ///< 点与环境的碰撞查询
				SEGMENT_ENVIRONMENT,          ///< 线段与环境的碰撞查询
				TWO_OBJECTS,                  ///< 两个特定对象之间的碰撞查询
				OBJECT_VS_ENVIRONMENT,        ///< 特定对象与环境中其他所有对象的碰撞查询
				ALL_RELEVANT_OBJECT_PAIRS,    ///< 场景中所有相关对象对之间的碰撞查询
				CONTINUOUS_OBJECT_VS_ENVIRONMENT ///< 连续碰撞检测：对象沿路径运动与环境的碰撞 (扫掠测试)
			};

			// --- 查询特定数据结构 ---

			/**
			 * @struct PointEnvQueryData
			 * @brief 用于 POINT_ENVIRONMENT 查询类型的数据。
			 */
			struct PointEnvQueryData {
				EcefPoint point;       ///< 要检查的点 (ECEF坐标)
				Time time_stamp = 0.0; ///< 查询的时间戳 (用于动态对象)

				PointEnvQueryData(EcefPoint p, Time t = 0.0) : point(std::move(p)), time_stamp(t) {}
			};

			/**
			 * @struct SegmentEnvQueryData
			 * @brief 用于 SEGMENT_ENVIRONMENT 查询类型的数据。
			 */
			struct SegmentEnvQueryData {
				WGS84Point start_point; ///< 线段起点 (WGS84坐标)
				WGS84Point end_point;   ///< 线段终点 (WGS84坐标)
				Time start_time = 0.0; ///< 线段起点的时间戳
				Time end_time = 0.0;   ///< 线段终点的时间戳

				SegmentEnvQueryData(WGS84Point s, WGS84Point e, Time t1 = 0.0, Time t2 = 0.0)
					: start_point(std::move(s)), end_point(std::move(e)), start_time(t1), end_time(t2) {}
			};

			/**
			 * @struct TwoObjectsQueryData
			 * @brief 用于 TWO_OBJECTS 查询类型的数据。
			 */
			struct TwoObjectsQueryData {
				ObjectID object1_id = NSUtils::INVALID_OBJECT_ID; ///< 第一个对象的ID
				ObjectID object2_id = NSUtils::INVALID_OBJECT_ID; ///< 第二个对象的ID

				TwoObjectsQueryData(ObjectID id1, ObjectID id2) : object1_id(id1), object2_id(id2) {}
			};

			/**
			 * @struct ObjectEnvQueryData
			 * @brief 用于 OBJECT_VS_ENVIRONMENT 查询类型的数据。
			 */
			struct ObjectEnvQueryData {
				ObjectID object_id = NSUtils::INVALID_OBJECT_ID; ///< 要检查的对象的ID

				explicit ObjectEnvQueryData(ObjectID id) : object_id(id) {}
			};

			/**
			 * @struct SweepQueryData
			 * @brief 用于 CONTINUOUS_OBJECT_VS_ENVIRONMENT (扫掠测试) 查询类型的数据。
			 */
			struct SweepQueryData {
				ObjectID moving_object_id = NSUtils::INVALID_OBJECT_ID; ///< 进行扫掠测试的移动对象的ID
				Vector3D direction;       ///< 移动方向 (建议归一化)
				double max_distance = 0.0;        ///< 最大移动距离

				SweepQueryData(ObjectID id, Vector3D dir, double dist)
					: moving_object_id(id), direction(std::move(dir)), max_distance(dist) {}
			};


			// --- 成员变量 ---
			QueryType type = QueryType::UNKNOWN; ///< 查询类型

			// 使用 std::variant 存储特定于查询类型的数据
			// std::monostate 用于表示某些查询类型可能不需要额外数据 (例如 ALL_RELEVANT_OBJECT_PAIRS)
			std::variant<
				std::monostate,              // 对应 QueryType::UNKNOWN 或 QueryType::ALL_RELEVANT_OBJECT_PAIRS
				PointEnvQueryData,
				SegmentEnvQueryData,
				TwoObjectsQueryData,
				ObjectEnvQueryData,
				SweepQueryData
			> data;

			// --- 构造函数 ---
			CollisionQuery() : type(QueryType::UNKNOWN), data(std::monostate{}) {}

			// 便利构造函数
			explicit CollisionQuery(QueryType query_type, decltype(data) query_data = std::monostate{})
				: type(query_type), data(std::move(query_data)) {
				// 确保 ALL_RELEVANT_OBJECT_PAIRS 使用 monostate
				if (type == QueryType::ALL_RELEVANT_OBJECT_PAIRS && !std::holds_alternative<std::monostate>(data)) {
					data = std::monostate{};
				}
			}
		};

		/**
				 * @struct CollisionResult
				 * @brief 统一的碰撞检测结果结构，包含所有类型碰撞检测所需的信息
				 */
		struct CollisionResult {
			// 基本碰撞信息
			bool hasCollision = false;                                     ///< 是否检测到碰撞
			double distance = std::numeric_limits<double>::infinity();     ///< 最近距离（如果未碰撞且计算了）
			double penetrationDepth = 0.0;                                 ///< 穿透深度（如果碰撞）

			// 接触点信息（ECEF坐标系）
			std::vector<EcefPoint> contactPoints;                  ///< 接触点列表（ECEF坐标）
			std::vector<Vector3D> contactNormals;                  ///< 接触点的法线列表

			// 接触点信息（WGS84坐标系，用于外部输出）
			std::vector<WGS84Point> contactPointsWGS84;            ///< 接触点列表（WGS84坐标）

			// 最近点信息（如果计算了最近距离）
			EcefPoint nearestPoint1;                               ///< 第一个形状上的最近点（ECEF坐标）
			EcefPoint nearestPoint2;                               ///< 第二个形状上的最近点（ECEF坐标）
			WGS84Point nearestPoint1WGS84;                         ///< 第一个形状上的最近点（WGS84坐标）
			WGS84Point nearestPoint2WGS84;                         ///< 第二个形状上的最近点（WGS84坐标）

			// 碰撞对象信息
			ObjectID object1Id = NSUtils::INVALID_OBJECT_ID;        ///< 第一个碰撞对象ID
			ObjectID object2Id = NSUtils::INVALID_OBJECT_ID;        ///< 第二个碰撞对象ID

			// 时间信息（用于连续碰撞检测）
			Time collisionTime = 0.0;                              ///< 碰撞发生的时间点（连续检测）

			// 碰撞源信息（调试用）
			std::string collisionSource;                                   ///< 检测到碰撞的检测器名称

			/**
			 * @brief 默认构造函数
			 */
			CollisionResult() = default;

			/**
			 * @brief 构造带有碰撞信息的结果
			 * @param id1 第一个碰撞对象ID
			 * @param id2 第二个碰撞对象ID
			 * @param depth 穿透深度
			 * @param source 碰撞源信息
			 */
			CollisionResult(
				const ObjectID& id1,
				const ObjectID& id2,
				double depth,
				const std::string& source = "通用检测器"
			) : hasCollision(true),
				penetrationDepth(depth),
				object1Id(id1),
				object2Id(id2),
				collisionSource(source) {}

			/**
			 * @brief 添加接触点信息（ECEF坐标）
			 * @param ecef_point 接触点（ECEF坐标）
			 * @param normal 接触点法线
			 */
			void addContact(const EcefPoint& ecef_point, const Vector3D& normal) {
				contactPoints.push_back(ecef_point);
				contactNormals.push_back(normal);
			}

			/**
			 * @brief 添加接触点信息（WGS84坐标）
			 * @param wgs84_point 接触点（WGS84坐标）
			 * @param normal 接触点法线
			 */
			void addContactWGS84(const WGS84Point& wgs84_point, const Vector3D& normal) {
				contactPointsWGS84.push_back(wgs84_point);
				contactNormals.push_back(normal);
			}

			/**
			 * @brief 添加接触点信息（同时包含ECEF和WGS84坐标）
			 * @param ecef_point 接触点（ECEF坐标）
			 * @param wgs84_point 接触点（WGS84坐标）
			 * @param normal 接触点法线
			 */
			void addContactBoth(const EcefPoint& ecef_point, const WGS84Point& wgs84_point, const Vector3D& normal) {
				contactPoints.push_back(ecef_point);
				contactPointsWGS84.push_back(wgs84_point);
				contactNormals.push_back(normal);
			}

			/**
			 * @brief 设置最近点信息
			 * @param ecef_p1 第一个形状上的最近点（ECEF坐标）
			 * @param ecef_p2 第二个形状上的最近点（ECEF坐标）
			 * @param dist 两点之间的距离
			 */
			void setNearestPoints(const EcefPoint& ecef_p1, const EcefPoint& ecef_p2, double dist) {
				// 设置ECEF坐标的最近点
				nearestPoint1 = ecef_p1;
				nearestPoint2 = ecef_p2;
				distance = dist;

				// 同时转换并设置WGS84坐标的最近点（用于外部输出）
				nearestPoint1WGS84 = CoordinateConverter::ecefToWGS84(ecef_p1);
				nearestPoint2WGS84 = CoordinateConverter::ecefToWGS84(ecef_p2);
			}

			/**
			 * @brief 将当前结果与另一个结果合并，保留距离最小的结果
			 * @param other 另一个碰撞结果
			 * @return 合并后的结果引用
			 */
			CollisionResult& merge(const CollisionResult& other) {
				// 如果另一个结果检测到碰撞，但当前结果没有
				if (!hasCollision && other.hasCollision) {
					*this = other;
					return *this;
				}

				// 如果两者都检测到碰撞，保留穿透深度更大的
				if (hasCollision && other.hasCollision) {
					if (other.penetrationDepth > penetrationDepth) {
						*this = other;
					}
					return *this;
				}

				// 如果两者都没有碰撞，保留距离更小的
				if (!hasCollision && !other.hasCollision) {
					if (other.distance < distance) {
						*this = other;
					}
					return *this;
				}

				// 默认情况下保留当前结果
				return *this;
			}
		};
	} // namespace NSEnvironment
} // namespace NSDrones