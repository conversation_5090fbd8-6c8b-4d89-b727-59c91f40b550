// include/planning/task_planners/task_planner_scanarea.h
#pragma once

#include "planning/itask_planner.h"
#include "mission/task.h"
#include "mission/task_params.h"

namespace NSDrones {
	namespace NSPlanning {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class ScanAreaTaskPlanner
		 * @brief 负责规划 SCAN_AREA (区域扫描) 任务。
		 */
		class ScanAreaTaskPlanner : public ITaskPlanner {
		public:
			explicit ScanAreaTaskPlanner(
				IPathPlannerPtr path_planner,
				ITrajectoryOptimizerPtr traj_optimizer);

			/** @brief 检查是否支持 SCAN_AREA 类型。 */
			bool isTaskTypeSupported(TaskType task_type) const override {
				return task_type == TaskType::SCAN_AREA;
			}

			/**
			 * @brief 规划 SCAN_AREA 任务。
			 * @param task 通用任务对象 (期望类型为 SCAN_AREA)。
			 * @param assigned_uavs 分配给此任务的无人机列表。
			 * @param start_states 每个分配无人机的起始状态。
			 * @return 规划结果 (航线、告警等)。
			 */
			PlanningResult planTask(const NSMission::Task& task,
				const std::vector<NSUav::UavPtr>& assigned_uavs,
				const std::map<ObjectID, NSUav::UavState>& start_states) override;

			/**
			 * @brief 初始化任务规划器。
			 * @param params 包含配置参数的 ParamValues 对象。
			 * @param raw_config 实例的原始JSON配置。
			 * @return 如果初始化成功则返回 true，否则返回 false。
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) override;

		private:
			// 扫描参数
			double scan_altitude_ = 50.0;      ///< 扫描高度 (米)
			double scan_speed_ = 10.0;         ///< 扫描速度 (米/秒)
			double overlap_ratio_ = 0.3;       ///< 重叠率 (0.0-1.0)
			ScanPatternType scan_pattern_ = ScanPatternType::ZIGZAG; ///< 扫描模式
			double camera_fov_ = 60.0;         ///< 相机视场角 (度)
		};

	} // namespace NSPlanning
} // namespace NSDrones