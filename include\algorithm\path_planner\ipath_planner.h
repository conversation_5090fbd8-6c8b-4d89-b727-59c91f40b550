// include/algorithm/path_planner/ipath_planner.h
#pragma once

#include "core/types.h"          
#include "utils/logging.h"
#include "uav/idynamic_model.h"  
#include "mission/task_strategies.h" 
#include <vector>
#include <memory>                  
#include <string>
#include <map>                     
#include "nlohmann/json.hpp" 

namespace NSDrones {
	namespace NSUav { class Uav; }
	namespace NSMission { struct PathConstraintStrategy; } 
	namespace NSParams { class ParamValues; } 
}

namespace NSDrones {
	namespace NSAlgorithm {
		using namespace NSDrones::NSUtils; // 考虑是否真的需要 using namespace
		using namespace NSDrones::NSCore;  // 考虑是否真的需要 using namespace

		/**
		 * @class IPathPlanner
		 * @brief 路径规划算法接口 (抽象基类)。
		 *        生成连接起点和终点的几何路径，考虑环境和路径约束。
		 *        现在应继承自 AlgorithmObject (间接通过具体实现类)。
		 */
		class IPathPlanner {
		public:
			/** @brief 默认构造函数 */
			explicit IPathPlanner();

			/** @brief 虚析构函数 */
			virtual ~IPathPlanner() = default;

			// --- 禁止拷贝和移动 ---
			IPathPlanner(const IPathPlanner&) = delete;
			IPathPlanner& operator=(const IPathPlanner&) = delete;
			IPathPlanner(IPathPlanner&&) = delete;
			IPathPlanner& operator=(IPathPlanner&&) = delete;

		public:
			/**
			 * @brief (纯虚函数) 查找从起点到终点的几何路径。
			 * @param start 起始点 (世界坐标)。
			 * @param goal 目标点 (世界坐标)。
			 * @param dynamics (可选) UAV 的动力学模型指针，用于获取粗略约束。
			 * @param path_constraints (可选) 路径约束策略指针。**新增**
			 * @param path 输出参数：如果找到路径，则填充路径点序列 (世界坐标)。
			 * @return 如果成功找到路径，返回 true；否则返回 false。
			 */
			virtual bool findPath(const EcefPoint& start,
				const EcefPoint& goal,
				const NSUav::IDynamicModel* dynamics,
				const NSMission::PathConstraintStrategy* path_constraints,
				std::vector<EcefPoint>& path) = 0;

			/**
			 * @brief (可选) 检查几何路径是否在环境中可行。
			 *        **注意:** 此默认实现仅检查静态环境，不考虑时间。
			 */
			virtual bool isPathFeasible(const std::vector<EcefPoint>& path) const;
		};

		using IPathPlannerPtr = std::shared_ptr<IPathPlanner>;

	} // namespace NSAlgorithm
} // namespace NSDrones

using namespace NSDrones::NSAlgorithm;