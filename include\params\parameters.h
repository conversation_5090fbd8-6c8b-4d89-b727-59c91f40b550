// include/params/parameters.h
#pragma once

#include <string>
#include <vector>
#include <variant>
#include <unordered_map>
#include <optional>
#include <stdexcept>
#include <typeinfo>
#include <typeindex>
#include <limits>
#include <memory>
#include <iostream>
#include <algorithm>
#include <cmath>
#include <utility>
#include <map>
#include <shared_mutex>
#include "nlohmann/json.hpp"
#include "utils/logging.h"
#include "utils/enum_utils.h"

#include <Eigen/Core>
#include <Eigen/Geometry>
#include "core/types.h"

namespace NSDrones {
	namespace NSParams {

		// Forward declarations
		class ParamRegistry; // ParamValues methods use this
		class ParamDefine;   // Used by ParamDefines and ParamValues
		class ParamDefines;  // May be used by ParamValues or ParamRegistry in full def

		using json = nlohmann::json;

		/**
		 * @brief 参数值的类型联合 (std::variant)。
		 * 定义了参数可以持有的所有受支持的数据类型。
		 */
		using ParamValue = std::variant<
			std::monostate, // Represents an uninitialized or invalid state
			int,
			double,
			bool,
			std::string,
			Eigen::Vector2d,
			Eigen::Vector3d,
			Eigen::Quaterniond,
			NSCore::WGS84Point,
			std::vector<int>,
			std::vector<double>,
			std::vector<bool>,
			std::vector<std::string>,
			std::vector<std::vector<double>>, // For polygon vertices or matrices
			std::vector<std::vector<std::string>> // For 2D string arrays (std::string[][])
		>;

		/**
		 * @brief 数值型参数的约束定义。
		 */
		struct NumericConstraint {
			std::optional<double> min_value = std::nullopt; ///< 允许的最小值 (可选)
			std::optional<double> max_value = std::nullopt; ///< 允许的最大值 (可选)

			/**
			 * @brief 验证给定数值是否符合约束。
			 * @tparam T 数值类型 (应可转换为 double)。
			 * @param value 要验证的值。
			 * @return bool 如果值有效则为 true，否则为 false。
			 */
			template<typename T> bool validate(T value) const {
				if (min_value.has_value() && static_cast<double>(value) < *min_value) return false;
				if (max_value.has_value() && static_cast<double>(value) > *max_value) return false;
				return true;
			}
		};
		struct StringConstraint {
			std::optional<size_t> max_length = std::nullopt; ///< 字符串最大长度 (可选)
			std::optional<std::vector<std::string>> allowed_values = std::nullopt; ///< 允许的字符串值列表 (可选)
			bool validate(const std::string& value) const { // 检查字符串是否符合约束
				if (max_length.has_value() && value.length() > *max_length) return false;
				if (allowed_values.has_value() && !allowed_values->empty()) {
					const auto& av = *allowed_values;
					return std::find(av.begin(), av.end(), value) != av.end();
				}
				return true;
			}
		};
		struct VectorConstraint { // Eigen 向量类型参数的约束定义。
			std::optional<int> expected_dimension = std::nullopt; ///< 期望的向量维度 (可选)
			std::optional<double> min_norm = std::nullopt;       ///< 向量的最小模长 (可选)
			std::optional<double> max_norm = std::nullopt;       ///< 向量的最大模长 (可选)
			template<typename Derived> bool validate(const Eigen::MatrixBase<Derived>& vec) const { // 检查 Eigen 向量是否符合约束
				if (expected_dimension.has_value() && vec.size() != *expected_dimension) return false;
				if (min_norm.has_value() || max_norm.has_value()) {
					double norm = vec.norm();
					if (min_norm.has_value() && norm < *min_norm) return false;
					if (max_norm.has_value() && norm > *max_norm) return false;
				}
				return true;
			}
		};
		struct VectorSizeConstraint { // std::vector 类型参数的大小约束定义。
			std::optional<size_t> min_size = std::nullopt; ///< vector 的最小尺寸 (可选)
			std::optional<size_t> max_size = std::nullopt; ///< vector 的最大尺寸 (可选)
			template<typename T> bool validate(const std::vector<T>& vec) const { // 检查 std::vector 的大小是否符合约束
				if (min_size.has_value() && vec.size() < *min_size) return false;
				if (max_size.has_value() && vec.size() > *max_size) return false;
				return true;
			}
		};

		/**
		 * @brief 参数约束的类型联合 (std::variant)。
		 * 可以是无约束 (std::monostate) 或特定类型的约束。
		 */
		using ParamConstraint = std::variant<
			std::monostate,
			NumericConstraint,
			StringConstraint,
			VectorConstraint,
			VectorSizeConstraint
		>;

		struct ParamCondition {
			std::string dependent_param_key_;
			ParamValue required_value_;
			std::optional<std::string> description_;

			// 默认构造函数
			ParamCondition() : required_value_(0) {} // 初始化成员，例如 ParamValue 用 int 0 初始化

			ParamCondition(std::string dep_key, ParamValue req_val, std::optional<std::string> desc = std::nullopt)
				: dependent_param_key_(std::move(dep_key)), required_value_(std::move(req_val)), description_(std::move(desc)) {}
		};

		/**
		 * @struct ParamDefine
		 * @brief 参数定义结构体，包含参数的元数据。
		 * 包括键名、可读名称、描述、类型、默认值和约束。
		 */
		struct ParamDefine {
			std::string key;            ///< 参数的唯一键名 (例如 "physics.mass")
			std::string name;           ///< 参数的可读名称 (例如 "Mass")
			std::string description;    ///< 参数的详细描述
			std::type_index type;       ///< 参数的 C++ 类型 (使用 std::type_index 存储)
			ParamValue default_value;   ///< 参数的默认值 (使用 ParamValue variant 存储)
			ParamConstraint constraints;  ///< 参数的约束条件 (使用 ParamConstraint variant 存储)
			std::optional<std::vector<std::string>> enum_values_;
			std::optional<std::string> enum_type_name_; 
			std::optional<ParamCondition> condition_;
			std::optional<std::string> defined_in_type_tag_;

			/**
			 * @brief ParamDefine 的构造函数。
			 * @param k 键名。
			 * @param n 可读名称。
			 * @param desc 描述。
			 * @param t 类型 (std::type_index)。
			 * @param def_val 默认值 (ParamValue)。
			 * @param cons 约束 (ParamConstraint)，默认为无约束。
			 */
			ParamDefine(std::string k = "",
				std::type_index t = typeid(nullptr_t),
				ParamValue def_val = {},
				std::string desc = "",
				std::string n = "",
				ParamConstraint cons = {},
				std::optional<std::vector<std::string>> enums = std::nullopt,
				std::optional<std::string> enum_type_str = std::nullopt,
				std::optional<ParamCondition> cond = std::nullopt,
				std::optional<std::string> defined_tag = std::nullopt)
				: key(std::move(k)),
				name(std::move(n)),
				description(std::move(desc)),
				type(t),
				default_value(std::move(def_val)),
				constraints(std::move(cons)),
				enum_values_(std::move(enums)),
				enum_type_name_(std::move(enum_type_str)),
				condition_(std::move(cond)),
				defined_in_type_tag_(std::move(defined_tag))
			{
				// 如果提供了 enum_type_name_，并且 default_value 是字符串，可以进行一次验证
				// 但这更适合在从 JSON 加载 ParamDefine 时进行，因为那时有日志和上下文
			}

			/**
			 * @brief 静态工厂方法，用于创建 ParamDefine 实例。
			 * @tparam T 参数的实际 C++ 类型。
			 * @param k 键名。
			 * @param n 可读名称。
			 * @param desc 描述。
			 * @param def_val 默认值 (其实际类型 T)。
			 * @param cons 约束 (ParamConstraint)，默认为无约束。
			 * @return ParamDefine 创建的参数定义对象。
			 */
			template <typename T>
			static ParamDefine create(std::string k, std::string n, std::string desc,
				T def_val, ParamConstraint cons = {}, std::optional<std::string> enum_type_str = std::nullopt) {
				LOG_TRACE("参数定义 (ParamDefine): 通过静态工厂方法为键 '{}' (类型: '{}') 创建定义.", k, typeid(T).name());
				return ParamDefine(
					std::move(k),
					std::type_index(typeid(T)),
					ParamValue{ std::move(def_val) },
					std::move(desc),
					std::move(n),
					std::move(cons),
					std::nullopt, // enum_values_ 通常从JSON加载
					std::move(enum_type_str), // 传递 enum_type_name_
					std::nullopt,
					std::nullopt
				);
			}

			bool hasConstraints() const { return !std::holds_alternative<std::monostate>(constraints); }
			const ParamConstraint& getConstraints() const { return constraints; }
			std::type_index getParamType() const { return type; } // Corrected: ParamType was not defined, use std::type_index directly
			bool hasEnumValues() const { return enum_values_.has_value() && !enum_values_->empty(); }
			const std::optional<std::vector<std::string>>& getEnumValues() const { return enum_values_; }
			bool hasEnumType() const { return enum_type_name_.has_value() && !enum_type_name_->empty(); }
			const std::optional<std::string>& getEnumTypeName() const { return enum_type_name_; }
			bool hasCondition() const { return condition_.has_value(); }
			const std::optional<ParamCondition>& getCondition() const { return condition_; }
		};

		/**
		 * @class ParamDefines
		 * @brief 存储一个特定类型标签 (type_tag) 的所有参数定义 (ParamDefine) 的集合。
		 * 这些定义通常从对应的 <type_tag>.json 文件加载，或者通过编程方式添加。
		 * 提供了添加、获取和合并参数定义的功能。
		 */
		class ParamDefines {
		private:
			std::string type_tag_;                     	///< 此参数定义集所属的类型标签
			std::map<std::string, ParamDefine> definitions_; 	///< 参数键到参数定义的映射
			mutable std::shared_mutex definitions_mutex_; 		///< 保护参数定义的读写锁

		public:
			/**
			 * @brief 构造函数，初始化类型标签。
			 * @param type_tag 参数定义集的类型标签（不可变，必须提供）。
			 */
			explicit ParamDefines(std::string type_tag);

			// 添加析构函数声明
			~ParamDefines();

			// 添加拷贝构造和拷贝赋值
			ParamDefines(const ParamDefines& other);
			ParamDefines& operator=(const ParamDefines& other);

			// 添加移动构造和移动赋值
			ParamDefines(ParamDefines&& other) noexcept;
			ParamDefines& operator=(ParamDefines&& other) noexcept;

			/**
			 * @brief 获取此参数定义集的类型标签。
			 * @return const std::string& 类型标签。
			 */
			const std::string& getTypeTag() const;

			/**
			 * @brief 获取内部的参数定义映射表。
			 * @return const std::map<std::string, ParamDefine>& 对参数定义映射的常量引用。
			 */
			const std::map<std::string, ParamDefine>& getDefinitions() const;

			/**
			 * @brief 添加单个参数定义。
			 * @param definition 要添加的 ParamDefine 对象。
			 * @return bool 如果成功添加 (键之前不存在或允许覆盖) 则为 true。
			 *         当前实现会覆盖同名键的现有定义。
			 */
			bool addDefine(const ParamDefine& definition);

			/**
			 * @brief 添加多个参数定义。
			 * @param definitions 包含 ParamDefine 对象的向量。
			 * @return bool 如果所有定义都成功添加则为 true。
			 */
			bool addDefines(const std::vector<ParamDefine>& definitions);

			/**
			 * @brief 获取指定键的参数定义。
			 * @param key 参数键。
			 * @return const ParamDefine* 如果找到则返回指向 ParamDefine 的常量指针，否则返回 nullptr。
			 */
			const ParamDefine* getEffectiveParamDefine(const std::string& key) const;

			/**
			 * @brief 移除指定键的参数定义。
			 * @param key 要移除的参数键。
			 * @return bool 如果成功移除则为 true，如果键不存在则为 false。
			 */
			bool removeDefine(const std::string& key);

			/**
			 * @brief 检查是否存在指定键的参数定义。
			 * @param key 参数键。
			 * @return bool 如果存在则为 true，否则为 false。
			 */
			bool hasParam(const std::string& key) const;

			/**
			 * @brief 获取所有参数定义的键名列表。
			 * @return std::vector<std::string> 包含所有键名的向量。
			 */
			std::vector<std::string> getKeys() const;

			/**
			 * @brief 清空所有参数定义。
			 */
			void clear();

			/**
			 * @brief 获取参数定义集中定义的参数数量。
			 * @return size_t 参数数量。
			 */
			size_t size() const;

			/**
			 * @brief 将另一个 ParamDefines 集合合并到当前集合中。
			 * @param other_set 要合并进来的另一个参数定义集。
			 * @param overwrite_existing 如果为 true (默认)，则 other_set 中的同名参数会覆盖当前集合中的参数。
			 *                         如果为 false，则当前集合中已存在的参数不会被覆盖。
			 */
			void merge(const ParamDefines& other_set, bool overwrite_existing = true);

			// --- 静态工厂方法 ---

			/**
			 * @brief 从 JSON 对象创建 ParamDefines 实例
			 * @param definitions_json JSON 对象，包含参数定义
			 * @param type_tag_override 可选的类型标签覆盖，如果为空则从 JSON 中读取
			 * @param out_final_type_tag 输出参数，返回最终确定的类型标签
			 * @return 成功时返回 ParamDefines 实例，失败时返回 nullopt
			 */
			static std::optional<ParamDefines> fromJson(
				const nlohmann::json& definitions_json,
				const std::string& type_tag_override = "",
				std::string* out_final_type_tag = nullptr);
		};
		using ParamDefinesPtr = std::shared_ptr<ParamDefines>;

		/**
		 * @brief 参数值集合类 (ParamValues)。
		 * 存储一组特定对象实例或类型的具体参数值。
		 * 值通过键名 (std::string) 索引，并使用 ParamValue 类型存储。
		 * 通常与一个 type_tag 相关联，用于从 ParamRegistry 获取参数定义和约束。
		 */
		class ParamValues {
		private:
			std::string type_tag_; ///< 此参数值集所属的类型标签
			std::unordered_map<std::string, ParamValue> values_; ///< 存储参数键到参数值的映射
			mutable std::shared_mutex values_mutex_; ///< 保护参数值的读写锁
			/**
			 * @brief 内部辅助函数，用于检查给定键的值是否符合其在注册表中的约束。
			 * @param key 参数键。
			 * @param value 要检查的参数值。
			 * @param registry 参数注册表实例，用于获取参数定义和约束。
			 * @return bool 如果值符合约束或无约束，则为 true；否则为 false。
			 */
			bool checkConstraints(const std::string& key, const ParamValue& value, const ParamRegistry& registry);

		public:
			/**
			 * @brief 构造函数，初始化类型标签。
			 * @param type_tag 参数值集的类型标签（不可变，必须提供）。
			 */
			explicit ParamValues(std::string type_tag);

			// 显式声明拷贝和移动操作以添加日志
			ParamValues(const ParamValues& other);
			ParamValues& operator=(const ParamValues& other);
			ParamValues(ParamValues&& other) noexcept;
			ParamValues& operator=(ParamValues&& other) noexcept;

			~ParamValues(); // 添加析构函数声明

			const std::string& getTypeTag() const;

			/**
			 * @brief 设置参数的 Variant 类型值。
			 * 会进行类型检查和约束检查 (如果 type_tag_ 和注册表提供了定义)。
			 * @param key 参数键。
			 * @param value 参数的 ParamValue (std::variant) 值。
			 * @param registry ParamRegistry 实例，用于查找定义和约束。
			 * @return bool 如果设置成功 (类型匹配且满足约束) 则为 true，否则为 false。
			 */
			bool setValueVariant(const std::string& key, ParamValue value, const ParamRegistry& registry);

			/**
			 * @brief 根据参数定义 (ParamDefine) 从 JSON 值安全地设置参数。
			 * 此方法会执行类型检查、约束验证，并将 JSON 值转换为适当的 ParamValue 类型。
			 * @param key 要设置的参数的键名。
			 * @param json_value 包含参数值的 nlohmann::json 对象。
			 * @param definition与键关联的 ParamDefine 对象，包含类型和约束信息。
			 * @param registry ParamRegistry 实例，可能用于查找嵌套类型定义等 (当前主要用于 checkConstraints)。
			 * @return 如果值成功设置 (类型匹配、满足约束) 则为 true，否则为 false。
			 */
			bool setValueFromJson(const std::string& key, const nlohmann::json& json_value, const ParamDefine& definition, const ParamRegistry& registry);

			/**
			 * @brief 在没有显式参数定义的情况下，尝试从 JSON 值猜测类型并设置参数。
			 * 主要用于灵活性，例如加载未在 ParamRegistry 中严格定义的临时参数。
			 * 类型推断基于 JSON 值本身的类型。
			 * @param key 要设置的参数的键名。
			 * @param json_value 包含参数值的 nlohmann::json 对象。
			 * @param registry ParamRegistry 实例 (主要用于 setValueVariant 中的约束检查，如果适用)。
			 * @return 如果值被成功猜测并设置则为 true，否则为 false。
			 */
			bool setValueFromJsonGuessType(const std::string& key, const nlohmann::json& json_value, const ParamRegistry& registry);

			/**
			 * @brief 设置参数的特定类型值 (模板方法)。
			 * @tparam T 参数的期望类型。
			 * @param key 参数键。
			 * @param value 参数值。
			 * @param registry ParamRegistry 实例。
			 * @return bool 如果设置成功则为 true，否则为 false。
			 */
			template <typename T>
			bool setValue(const std::string& key, T value, const ParamRegistry& registry) {
				return setValueVariant(key, ParamValue{ std::move(value) }, registry);
			}

			/**
			 * @brief 获取当前参数集中参数的数量。
			 * @return size_t 参数数量。
			 */
			size_t size() const;

			/**
			 * @brief 获取指定键的参数值 (模板方法)。
			 * @tparam T 期望获取的值的类型。
			 * @param key 参数键。
			 * @return std::optional<T> 如果找到且类型匹配，则返回包含值的 optional；否则返回 std::nullopt。
			 */
			template <typename T>
			std::optional<T> getValue(const std::string& key) const {
				std::shared_lock lock(values_mutex_);
				auto it = values_.find(key);
				if (it != values_.end()) {
					if (const T* val_ptr = std::get_if<T>(&(it->second))) {
						return *val_ptr;
					}
					LOG_WARN("ParamValues::getValue (TypeTag: '{}'): 参数 '{}' 存在，但存储的类型与请求的类型 '{}' 不匹配。",
							 type_tag_, key, typeid(T).name());
				}
				return std::nullopt;
			}

			/**
			 * @brief 获取指定键的参数值，如果不存在或类型不匹配则抛出异常。
			 * @tparam T 期望获取的值的类型。
			 * @param key 参数键。
			 * @return T 参数值。
			 * @throw std::runtime_error 如果参数未找到或类型不匹配。
			 */
			template <typename T>
			T getValueOrThrow(const std::string& key) const {
				std::shared_lock lock(values_mutex_);
				auto it = values_.find(key);
				if (it != values_.end()) {
					if (const T* val_ptr = std::get_if<T>(&(it->second))) {
						return *val_ptr;
					}
					// 键存在但类型不匹配
					std::string msg = "参数值集 (ParamValues): 参数 '" + key + "' 未找到或类型不匹配 (请求类型: " + std::string(typeid(T).name()) + ") (键存在但实际类型不符)";
					throw std::runtime_error(msg);
				}
				// 键不存在
				std::string msg = "参数值集 (ParamValues): 参数 '" + key + "' 未找到或类型不匹配 (请求类型: " + std::string(typeid(T).name()) + ")";
				throw std::runtime_error(msg);
			}

			/**
			 * @brief 获取指定键的参数值，如果不存在或类型不匹配则返回默认值。
			 * @tparam T 期望获取的值的类型。
			 * @param key 参数键。
			 * @param default_val 获取失败时返回的默认值。
			 * @return T 参数值或默认值。
			 */
			template <typename T>
			T getValueOrDefault(const std::string& key, T default_value_arg) const {
				std::shared_lock lock(values_mutex_);
				auto it = values_.find(key);
				if (it != values_.end()) {
					if (const T* val_ptr = std::get_if<T>(&(it->second))) {
						return *val_ptr; // Value found and type matches
					}
					LOG_WARN("ParamValues::getValueOrDefault (2-arg) (TypeTag: '{}'): 参数 '{}' 存在，但存储类型与请求类型 '{}' 不匹配。返回提供的默认值。",
							 type_tag_, key, typeid(T).name());
				}
				LOG_TRACE("ParamValues::getValueOrDefault (2-arg) (TypeTag: '{}'): 参数 '{}' 未在值中找到。返回提供的默认值。", type_tag_, key);
				return default_value_arg;
			}

			/**
			 * @brief 获取指定键的参数值，如果不存在或类型不匹配则返回默认值。
			 * @tparam T 期望获取的值的类型。
			 * @param key 参数键。
			 * @param default_val 获取失败时返回的默认值。
			 * @param registry_ptr ParamRegistry 实例，用于查找参数定义的默认值。
			 * @return T 参数值或默认值。
			 */
			template <typename T>
			T getValueOrDefault(const std::string& key, T default_value_arg, const ParamRegistry* registry_ptr) const {
				// 线程安全的读操作
				{
					std::shared_lock lock(values_mutex_);
					auto it = values_.find(key);
					if (it != values_.end()) {
						if (const T* val_ptr = std::get_if<T>(&(it->second))) {
							return *val_ptr;
						}
						LOG_WARN("ParamValues::getValueOrDefault (3-arg) (TypeTag: '{}'): 参数 '{}' 在值中存在，但存储的类型与请求的类型 '{}' 不匹配。将尝试 ParamDefine 或提供的默认值。",
								 type_tag_, key, typeid(T).name());
					}
				}

				if (registry_ptr) {
					const ParamDefine* def = registry_ptr->getEffectiveParamDefine(type_tag_, key);
					if (def) {
						if (const T* def_val_ptr = std::get_if<T>(&(def->default_value))) {
							LOG_TRACE("ParamValues::getValueOrDefault (3-arg) (TypeTag: '{}'): 参数 '{}' 未在值中直接找到或类型不符。使用 ParamDefine 中的默认值。", type_tag_, key);
							return *def_val_ptr;
						} else {
							LOG_WARN("ParamValues::getValueOrDefault (3-arg) (TypeTag: '{}'): 参数 '{}' 在 ParamDefine 中的默认值类型与请求的类型 '{}' 不匹配。将使用代码中提供的默认值。",
									 type_tag_, key, typeid(T).name());
						}
					} else {
					    LOG_TRACE("ParamValues::getValueOrDefault (3-arg) (TypeTag: '{}'): 参数 '{}' 在 ParamRegistry 中未找到定义。将使用代码中提供的默认值。", type_tag_, key);
					}
				} else {
				    LOG_TRACE("ParamValues::getValueOrDefault (3-arg) (TypeTag: '{}'): ParamRegistry 未提供。无法查找 ParamDefine 的默认值。将使用代码中提供的默认值。", type_tag_, key);
				}

				LOG_TRACE("ParamValues::getValueOrDefault (3-arg) (TypeTag: '{}'): 参数 '{}' 最终使用代码中提供的默认值。", type_tag_, key);
				return default_value_arg;
			}

			/**
			 * @brief 获取参数的原始 std::variant 类型值。
			 * 当不确定值的具体类型时使用。
			 * @param key 参数键。
			 * @return std::optional<ParamValue> 如果找到则返回包含 ParamValue 的 optional，否则返回 std::nullopt。
			 */
			std::optional<ParamValue> getValueVariant(const std::string& key) const;

			/**
			 * @brief 批量获取一组键的参数值。
			 * @param keys 要获取的参数键列表。
			 * @return std::vector<std::pair<std::string, ParamValue>> 包含找到的键值对的向量。
			 */
			std::vector<std::pair<std::string, ParamValue>> getValues(const std::vector<std::string>& keys) const;

			/**
			 * @brief 批量设置一组参数值。
			 * @param params 要设置的参数键值对列表。
			 * @param registry ParamRegistry 实例，用于约束检查。
			 * @return bool 如果所有参数都设置成功则为 true，否则为 false (即使部分成功也可能返回 false)。
			 */
			bool setValues(const std::vector<std::pair<std::string, ParamValue>>& params, const ParamRegistry& registry);

			/**
			 * @brief 将指定键的参数值转换为 JSON 对象。
			 * @param keys 要转换的参数键列表。
			 * @return json 包含这些参数的 JSON 对象。
			 */
			json valuesToJson(const std::vector<std::string>& keys) const;

			/**
			 * @brief 从 JSON 对象加载多个参数值。
			 * @param params_json_obj 包含参数键值对的 JSON 对象。
			 * @param registry ParamRegistry 实例，用于查找定义和约束。
			 * @param effective_defines_map 可选的、预先解析的参数定义映射。如果提供，将优先使用这些定义。
			 *                          如果为 nullptr，则会尝试通过 ParamValues 的 type_tag_ 从 registry 获取定义。
			 * @param is_instance_specific 可选标志，指示这些是否为实例特定参数，可能影响错误处理或日志记录。
			 * @param guess_types_for_undefined 如果参数定义未找到，是否尝试猜测类型进行转换。
			 * @return 如果所有值都根据其定义（或猜测）成功加载则为 true，否则为 false。
			 */
			bool valuesFromJson(const json& params_json_obj,
				const ParamRegistry& registry,
				const std::map<std::string, ParamDefine>* effective_defines_map = nullptr,
				bool is_instance_specific = false,
				bool guess_types_for_undefined = false);

			/**
			 * @brief 将当前所有参数值转换为 JSON 对象。
			 * @return json 包含所有参数的 JSON 对象。
			 */
			json valuesToJson() const;

			/**
			 * @brief 从 JSON 对象加载整个参数集，会先清空现有参数。
			 * @param json_data 包含参数的 JSON 对象。
			 * @param registry ParamRegistry 实例。
			 * @return bool 如果加载成功则为 true。
			 */
			bool loadFromJson(const json& json_data, const ParamRegistry& registry);

			/**
			 * @brief 检查参数集中是否包含指定的参数键。
			 * @param key 参数键。
			 * @return bool 如果包含则为 true，否则为 false。
			 */
			bool hasParam(const std::string& key) const;

			/**
			 * @brief 获取所有参数及其值的映射表副本（线程安全）。
			 * @return std::unordered_map<std::string, ParamValue> 内部存储的副本。
			 * @note 为了线程安全，此方法返回副本而不是引用
			 */
			std::unordered_map<std::string, ParamValue> getValues() const;

			/**
			 * @brief 清除所有已设置的参数值。
			 */
			void clear();

			/**
			 * @brief 创建当前 ParamValues 对象的深拷贝。
			 * @return std::shared_ptr<ParamValues> 指向新创建的克隆对象的共享指针。
			 */
			std::shared_ptr<ParamValues> clone() const; // 保留 clone 方法声明

			/**
			 * @brief 根据当前 ParamValues 的 type_tag_ 从 ParamRegistry 加载所有相应的默认值。
			 * 会考虑参数继承。仅当键不存在于当前参数集中时才加载。
			 * @param registry ParamRegistry 实例。
			 * @return bool 如果成功获取并尝试加载默认值则为true，如果无法获取定义集则为false。
			 */
			bool loadDefaults(const ParamRegistry& registry); // 确保返回 bool

			/**
			 * @brief 获取所有参数定义的键名列表。
			 * @return std::vector<std::string> 包含所有键名的向量。
			 */
			std::vector<std::string> getKeys() const;

			/**
			 * @brief 获取指定键的参数值，并将其转换为指定的枚举类型。
			 *
			 * 查找顺序:
			 * 1. 当前 ParamValues 实例中存储的字符串值。
			 * 2. 如果1失败，则查找参数定义(ParamDefine)中的默认字符串值。
			 * 3. 如果1和2都失败，或未找到定义，则视为空字符串进行转换尝试。
			 * 转换时使用 NSUtils::convertStringToEnumAny，它会在转换失败时应用该枚举类型注册的编译期默认值。
			 *
			 * @tparam EnumType 要转换成的目标枚举类型。此类型必须已通过 NSUtils::registerEnum 正确注册。
			 * @param key 参数的键。
			 * @return std::optional<EnumType> 包含转换后的枚举值。
			 *         如果 EnumType 未注册，或 ParamDefine 中的 enum_type 与请求的 EnumType 不匹配，则返回 std::nullopt。
			 *         否则，应始终返回一个包含有效枚举值(可能是默认值)的 optional。
			 */
			template <typename EnumType>
			std::optional<EnumType> getEnumValue(const std::string& key) const {
				static_assert(std::is_enum_v<EnumType>, "EnumType must be an enumeration type.");

				std::optional<std::string> string_to_convert_opt;
				const ParamDefine* def = nullptr;

				// 1. 尝试从当前值中获取字符串（线程安全）
				{
					std::shared_lock lock(values_mutex_);
					auto it = values_.find(key);
					if (it != values_.end()) {
						if (std::holds_alternative<std::string>(it->second)) {
							string_to_convert_opt = std::get<std::string>(it->second);
							LOG_TRACE("ParamValues::getEnumValue (TypeTag: '{}'): 键 '{}', 在 values_ 中找到字符串: '{}'.", type_tag_, key, *string_to_convert_opt);
						}
						else {
							LOG_WARN("ParamValues::getEnumValue (TypeTag: '{}'): 键 '{}' 在 values_ 中存在但不是字符串 (实际类型索引: {})。将尝试定义中的默认值。", type_tag_, key, it->second.index());
						}
					}
				}

				// 2. 如果在 values_ 中未找到字符串，则尝试从参数定义获取默认字符串值
				if (!string_to_convert_opt) {
					def = ParamRegistry::getInstance().getEffectiveParamDefine(this->type_tag_, key);
					if (def) {
						// 只有当定义明确指出是枚举类型时，才使用其默认值作为字符串源
						if (def->enum_type_name_.has_value()) {
							if (std::holds_alternative<std::string>(def->default_value)) {
								string_to_convert_opt = std::get<std::string>(def->default_value);
								LOG_TRACE("ParamValues::getEnumValue (TypeTag: '{}'): 键 '{}', 使用参数定义中的默认字符串: '{}'.", type_tag_, key, *string_to_convert_opt);
							}
							else {
								LOG_WARN("ParamValues::getEnumValue (TypeTag: '{}'): 键 '{}', 其定义 ('{}') 的默认值不是字符串 (实际类型索引: {})。将尝试使用空字符串转换。",
									type_tag_, key, *def->enum_type_name_, def->default_value.index());
							}
						}
						else {
							LOG_WARN("ParamValues::getEnumValue (TypeTag: '{}'): 键 '{}' 的定义存在，但未指定 enum_type_name。无法按枚举处理。", type_tag_, key);
							return std::nullopt; // 定义不是枚举类型
						}
					}
					else {
						LOG_WARN("ParamValues::getEnumValue (TypeTag: '{}'): 键 '{}' 在 values_ 中未找到，且参数定义也未在 ParamRegistry 中找到。将尝试使用空字符串转换。", type_tag_, key);
					}
				}

				// 确保我们有一个用于转换的字符串 (可能是空字符串作为最终回退)
				std::string final_string_to_convert = string_to_convert_opt.value_or("");

				// 3. 获取请求的 EnumType 的注册名称
				std::optional<std::string> registered_name_for_EnumType = NSUtils::typeIdToRegisteredName(typeid(EnumType));
				if (!registered_name_for_EnumType) {
					LOG_ERROR("ParamValues::getEnumValue (TypeTag: '{}'): EnumType '{}' 未通过 NSUtils::registerEnum 注册。无法转换键 '{}'.",
						type_tag_, typeid(EnumType).name(), key);
					return std::nullopt;
				}

				// 4. 如果之前获取了参数定义(def)，校验其 enum_type_name 是否与请求的 EnumType 的注册名匹配
				if (def && def->enum_type_name_.has_value() && (*def->enum_type_name_ != *registered_name_for_EnumType)) {
					LOG_ERROR("ParamValues::getEnumValue (TypeTag: '{}'): 键 '{}' 的类型不匹配。请求的 EnumType '{}' (注册为 '{}') 与 ParamDefine 中的 enum_type '{}' 不符。",
						type_tag_, key, typeid(EnumType).name(), *registered_name_for_EnumType, *def->enum_type_name_);
					return std::nullopt;
				}

				// 5. 执行转换
				LOG_TRACE("ParamValues::getEnumValue (TypeTag: '{}'): 准备为键 '{}' 将字符串 '{}' 转换为注册类型 '{}' (对应于 EnumType '{}')。",
					type_tag_, key, final_string_to_convert, *registered_name_for_EnumType, typeid(EnumType).name());
				std::any result_any = NSUtils::convertStringToEnumAny(*registered_name_for_EnumType, final_string_to_convert, std::any{});

				if (result_any.has_value()) {
					try {
						return std::any_cast<EnumType>(result_any);
					}
					catch (const std::bad_any_cast& e) {
						LOG_ERROR("ParamValues::getEnumValue (TypeTag: '{}'): 转换键 '{}' 后，无法将 std::any (类型: {}) 转换为目标 EnumType '{}'. 错误: {}",
							type_tag_, key, result_any.type().name(), typeid(EnumType).name(), e.what());
						return std::nullopt;
					}
				}
				else {
					// NSUtils::convertStringToEnumAny 应该总是返回一个有值的 std::any (包含默认值)
					LOG_CRITICAL("ParamValues::getEnumValue (TypeTag: '{}'): NSUtils::convertStringToEnumAny 为键 '{}' 返回了一个空的 std::any，这不应发生。注册名: '{}', 字符串: '{}'",
						type_tag_, key, *registered_name_for_EnumType, final_string_to_convert);
					return std::nullopt;
				}
			}

			/**
			 * @brief 以类型安全的方式编程设置枚举参数的值。
			 *
			 * 此方法会将提供的 C++ 枚举值转换为其规范的字符串表示形式，
			 * 然后使用 setValue<std::string> 将其存储。
			 * 它还会（如果定义存在）验证参数定义中的 enum_type_name 是否与提供的 EnumType 匹配。
			 *
			 * @tparam EnumType 要设置的枚举的 C++ 类型。
			 * @param key 参数的键。
			 * @param enum_value 要设置的 C++ 枚举值。
			 * @param registry ParamRegistry 实例，用于获取定义和进行约束检查。
			 * @return 如果设置成功（包括约束检查通过），则为 true；否则为 false。
			 */
			template <typename EnumType>
			bool setEnumValue(const std::string& key, EnumType enum_value, const ParamRegistry& registry) {
				static_assert(std::is_enum_v<EnumType>, "EnumType must be an enumeration type.");

				// 1. 验证 EnumType 是否已注册并获取其注册名称
				std::optional<std::string> registered_name_for_EnumType = NSUtils::typeIdToRegisteredName(typeid(EnumType));
				if (!registered_name_for_EnumType) {
					LOG_ERROR("ParamValues::setEnumValue (TypeTag: '{}'): EnumType '{}' 未通过 NSUtils::registerEnum 注册。无法设置键 '{}'.",
						this->type_tag_, typeid(EnumType).name(), key);
					return false;
				}

				// 2. 获取参数定义，并校验其 enum_type_name (如果定义存在)
				const ParamDefine* def = registry.getEffectiveParamDefine(this->type_tag_, key);
				if (def) {
					if (!def->enum_type_name_.has_value() || (*def->enum_type_name_ != *registered_name_for_EnumType)) {
						LOG_ERROR("ParamValues::setEnumValue (TypeTag: '{}'): 键 '{}' 的定义 ('{}') 与提供的 EnumType '{}' (注册名 '{}') 不匹配。",
							this->type_tag_, key, def->enum_type_name_.value_or("未定义"), typeid(EnumType).name(), *registered_name_for_EnumType);
						return false;
					}
					// 确保 ParamDefine 内部也将此参数类型视为 std::string
					if (def->type != typeid(std::string)) {
						LOG_ERROR("ParamValues::setEnumValue (TypeTag: '{}'): 键 '{}' 的定义 enum_type 为 '{}'，但其内部 C++ 类型不是 std::string (而是 '{}')。这是配置不一致。",
							this->type_tag_, key, *def->enum_type_name_, def->type.name());
						return false;
					}
				}
				else {
					LOG_WARN("ParamValues::setEnumValue (TypeTag: '{}'): 未找到键 '{}' 的参数定义。将尝试设置值，但无法根据定义验证其 enum_type。", this->type_tag_, key);
					// 即使没有定义，也允许设置，但这意味着它不会从定义层面被强制为特定枚举类型
				}

				// 3. 将 C++ 枚举值转换为其规范字符串表示
				std::string string_to_store = NSUtils::enumToString(enum_value);
				// 检查 enumToString 是否成功 (magic_enum 在失败时可能返回空串或包含 "Invalid")
				if (string_to_store.empty() || string_to_store.rfind("Invalid<", 0) == 0 || string_to_store.rfind("ERROR", 0) == 0) {
					LOG_ERROR("ParamValues::setEnumValue (TypeTag: '{}'): 无法将提供的 EnumType '{}' 的值转换为有效字符串表示形式以设置键 '{}'.",
						this->type_tag_, typeid(EnumType).name(), key);
					return false;
				}

				// 4. 使用 setValue<std::string> 存储，这将触发约束检查等
				LOG_TRACE("ParamValues::setEnumValue (TypeTag: '{}'): 准备为键 '{}' 设置枚举值 '{}' (将存储为字符串 '{}').", this->type_tag_, key, typeid(EnumType).name(), string_to_store);
				return this->setValue<std::string>(key, string_to_store, registry);
			}
		};
		using ParamValuesPtr = std::shared_ptr<ParamValues>;

		/**
		 * @brief 将ParamValue转换为字符串表示，用于日志记录和调试
		 * @param value 要转换的ParamValue
		 * @return 参数值的字符串表示
		 */
		std::string toString(const ParamValue& value);

		// 参数验证辅助函数
		bool isJsonTypeCompatible(const nlohmann::json& json_value, const std::type_index& target_type);
		bool isValidEnumValue(const std::string& value, const std::string& enum_type_name);
		bool validateValueRange(const ParamValue& value, const ParamDefine& definition);

	} // namespace NSParams
} // namespace NSDrones