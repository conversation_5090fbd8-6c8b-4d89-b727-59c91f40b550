// include/environment/collision/collision_engine.h
#pragma once

#include "environment/collision/collision_types.h"
#include "environment/collision/detector_registry.h"
#include "core/entity_object.h"
#include "core/geometry/ishape.h"  // 新形状系统
#include "environment/indices/ispatial_index.h"
#include "environment/storage/object_storage.h"
#include "environment/coordinate/coordinate_manager.h"
#include "environment/coordinate/task_space.h"
#include "utils/coordinate_converter.h"
#include "core/types.h"
#include "utils/logging.h"
#include <vector>
#include <memory>
#include <unordered_map>
#include <unordered_set>

// 前向声明 ObjectStorage
template<typename T = std::unordered_map<ObjectID,
	std::shared_ptr<EntityObject>>>
	class ObjectStorage;

namespace NSDrones {
	namespace NSEnvironment {

		/**
		 * @class CollisionEngine
		 * @brief 碰撞引擎，底层的碰撞检测计算引擎，依赖外部对象存储和空间索引进行碰撞检测
		 */
		template<typename ObjectContainer = std::unordered_map<ObjectID, std::shared_ptr<EntityObject>>>
		class CollisionEngine {
		public:
			/**
			 * @brief 构造函数
			 * @param object_storage 对 ObjectStorage 的引用
			 * @param spatial_index 对 ISpatialIndex 的引用
			 * @param coordinate_manager 对 CoordinateManager 的共享指针（可选）
			 */
			CollisionEngine(ObjectStorage<ObjectContainer>& object_storage,
				ISpatialIndex& spatial_index,
				std::shared_ptr<CoordinateManager> coordinate_manager = nullptr)
				: object_storage_(object_storage),
				spatial_index_(spatial_index),
				coordinate_manager_(coordinate_manager) {
				LOG_INFO("碰撞引擎：初始化，使用外部 ObjectStorage、ISpatialIndex 和坐标系统管理器。");
			}

			/**
			 * @brief 初始化碰撞引擎
			 * @param global_params 全局参数对象，用于获取碰撞检测配置
			 * @return 如果成功初始化则返回 true，否则返回 false
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> global_params);

			/**
			 * @brief 检测特定对象与所有其他对象的碰撞
			 * @param objectId 对象ID
			 * @param options 碰撞检测选项
			 * @return 碰撞结果列表
			 */
			std::vector<CollisionResult> checkObjectCollisions(ObjectID objectId,
				const CollisionOptions& options = {});

			/**
			 * @brief 检测场景中所有对象之间的碰撞 (基于 object_storage_ 中的对象)
			 * @param options 碰撞检测选项
			 * @return 碰撞结果列表
			 */
			std::vector<CollisionResult> checkAllCollisions(const CollisionOptions& options = {});

			/**
			 * @brief 根据对象ID获取对象的共享指针 (从 ObjectStorage 获取)
			 * @param id 要获取的对象的ID
			 * @return 对象的共享指针，如果未找到则返回 nullptr
			 */
			std::shared_ptr<EntityObject> getObject(ObjectID id) const;

			/**
			 * @brief 检测一个临时几何体与环境中所有其他对象的碰撞
			 * @param temp_shape 要检测的临时几何体
			 * @param ecef_position 临时几何体的ECEF位置
			 * @param orientation 临时几何体的朝向
			 * @param options 碰撞检测选项
			 * @return 碰撞结果列表
			 */
			std::vector<CollisionResult> checkGeometryAgainstEnvironment(
				const IShape& temp_shape,
				const EcefPoint& ecef_position,
				const Orientation& orientation,
				const CollisionOptions& options = {}
			);

			/**
			 * @brief 检测两个对象之间的碰撞
			 * @param obj1 第一个对象
			 * @param obj2 第二个对象
			 * @param options 碰撞检测选项
			 * @return 碰撞结果
			 */
			CollisionResult checkObjectCollision(
				const EntityObject& obj1,
				const EntityObject& obj2,
				const CollisionOptions& options = {}
			);

			/**
			 * @brief 检测对象与环境的碰撞
			 * @param obj 要检测的对象
			 * @param options 碰撞检测选项
			 * @return 碰撞结果列表
			 */
			std::vector<CollisionResult> checkObjectAgainstEnvironment(
				std::shared_ptr<EntityObject> obj,
				const CollisionOptions& options = {}
			);

			/**
			 * @brief 检测对象与对象的碰撞
			 * @param obj1 第一个对象
			 * @param obj2 第二个对象
			 * @param options 碰撞检测选项
			 * @return 碰撞结果列表
			 */
			std::vector<CollisionResult> checkObjectAgainstObject(
				std::shared_ptr<EntityObject> obj1,
				std::shared_ptr<EntityObject> obj2,
				const CollisionOptions& options = {}
			);

			/**
			 * @brief 获取对象存储的引用
			 * @return 对象存储引用
			 */
			const ObjectStorage<ObjectContainer>& getObjectStorage() const {
				return object_storage_;
			}

			/**
			 * @brief 设置坐标系统管理器
			 * @param coordinate_manager 坐标系统管理器的共享指针
			 */
			void setCoordinateManager(std::shared_ptr<CoordinateManager> coordinate_manager) {
				coordinate_manager_ = coordinate_manager;
				LOG_INFO("碰撞引擎：已设置坐标系统管理器");
			}

			/**
			 * @brief 获取坐标系统管理器
			 * @return 坐标系统管理器的共享指针
			 */
			std::shared_ptr<CoordinateManager> getCoordinateManager() const {
				return coordinate_manager_;
			}

			/**
			 * @brief 检测点与环境的碰撞
			 * @param ecef_point 查询点的ECEF坐标
			 * @param safety_radius 安全半径
			 * @param options 碰撞检测选项
			 * @return 碰撞结果列表
			 */
			std::vector<CollisionResult> checkPointAgainstEnvironment(
				const EcefPoint& ecef_point,
				double safety_radius,
				const CollisionOptions& options = {}
			);

			/**
			 * @brief 检测线段与环境的碰撞
			 * @param ecef_start 线段起点的ECEF坐标
			 * @param ecef_end 线段终点的ECEF坐标
			 * @param safety_radius 安全半径
			 * @param options 碰撞检测选项
			 * @return 碰撞结果列表
			 */
			std::vector<CollisionResult> checkSegmentAgainstEnvironment(
				const EcefPoint& ecef_start,
				const EcefPoint& ecef_end,
				double safety_radius,
				const CollisionOptions& options = {}
			);

		protected:
			DetectorRegistry detectorRegistry_; // 形状检测器注册表 (这个可以保留，因为它不存储状态，只提供方法)
			ObjectStorage<ObjectContainer>& object_storage_;      // 对外部对象存储的引用
			ISpatialIndex& spatial_index_;       // 对外部空间索引的引用
			std::shared_ptr<CoordinateManager> coordinate_manager_; // 坐标系统管理器（可选）

			/**
			 * @brief 创建FCL碰撞对象（使用ECEF坐标系）
			 * @param shape 形状指针
			 * @param ecef_position ECEF坐标系中的位置
			 * @param orientation 方向（四元数）
			 * @return FCL碰撞对象指针，如果创建失败返回nullptr
			 */
			std::shared_ptr<fcl::CollisionObjectd> createFCLObject(
				const IShape* shape, // 接受指针，因为从 ObjectStorage 获取的 shape 可能是 nullptr
				const EcefPoint& ecef_position,
				const Orientation& orientation) const {
				if (!shape) {
					LOG_WARN("碰撞引擎：尝试为null形状创建FCL对象");
					return nullptr;
				}

				// 使用新的IShape接口获取FCL几何体
				auto geometry = shape->getFCLGeometry();
				if (!geometry) {
					LOG_WARN("碰撞引擎：无法为形状 {} 获取FCL几何体", shape->toString());
					return nullptr;
				}

				// 创建变换矩阵（使用ECEF坐标）
				fcl::Transform3d transform = fcl::Transform3d::Identity();
				transform.translation() = fcl::Vector3d(ecef_position.x(), ecef_position.y(), ecef_position.z());

				// 设置旋转（四元数转换为旋转矩阵）
				if (orientation.coeffs().norm() > 0) {
					transform.linear() = orientation.toRotationMatrix();
				}

				// 创建FCL碰撞对象
				auto fcl_object = std::make_shared<fcl::CollisionObjectd>(geometry, transform);

				LOG_TRACE("碰撞引擎：成功为形状 {} 创建FCL对象，ECEF位置: [{:.2f},{:.2f},{:.2f}]",
					shape->toString(), ecef_position.x(), ecef_position.y(), ecef_position.z());

				return fcl_object;
			}

			/**
			 * @brief 创建FCL碰撞对象（使用ECEF坐标系）
			 * @param shape 形状指针
			 * @param ecef_position ECEF坐标系中的位置
			 * @param orientation 方向（四元数）
			 * @return FCL碰撞对象指针，如果创建失败返回nullptr
			 */
			std::shared_ptr<fcl::CollisionObjectd> createFCLObjectFromECEF(
				const IShape* shape,
				const EcefPoint& ecef_position,
				const Orientation& orientation) const {

				if (!shape) {
					LOG_WARN("碰撞引擎：尝试为null形状创建FCL对象");
					return nullptr;
				}

				LOG_TRACE("碰撞引擎：使用ECEF坐标 [{:.2f},{:.2f},{:.2f}] 创建FCL对象",
					ecef_position.x(), ecef_position.y(), ecef_position.z());

				// 直接使用ECEF坐标创建FCL对象
				return createFCLObject(shape, ecef_position, orientation);
			}

			/**
			 * @brief 创建FCL碰撞对象（从WGS84坐标转换为ECEF）
			 * @param shape 形状指针
			 * @param wgs84_position WGS84坐标系中的位置
			 * @param orientation 方向（四元数）
			 * @return FCL碰撞对象指针，如果创建失败返回nullptr
			 */
			std::shared_ptr<fcl::CollisionObjectd> createFCLObjectFromWGS84(
				const IShape* shape,
				const WGS84Point& wgs84_position,
				const Orientation& orientation) const {

				if (!shape) {
					LOG_WARN("碰撞引擎：尝试为null形状创建FCL对象");
					return nullptr;
				}

				// 转换WGS84坐标为ECEF坐标进行内部计算
				EcefPoint ecef_position = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_position);

				LOG_TRACE("碰撞引擎：WGS84坐标 {} 转换为ECEF坐标 {} 创建FCL对象",
					wgs84_position.toString(), ecef_position.toString());

				// 使用ECEF坐标创建FCL对象
				return createFCLObject(shape, ecef_position, orientation);
			}

		private:
			// 注意：坐标转换现在使用CoordinateConverter工具类，不再需要私有转换方法

		};

	} // namespace NSEnvironment
} // namespace NSDrones
