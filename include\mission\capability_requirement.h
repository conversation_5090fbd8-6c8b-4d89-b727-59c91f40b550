// include/mission/capability_requirement.h
#pragma once

#include "core/types.h" 
#include <vector>
#include <string>
#include <optional>
#include <algorithm> 

namespace NSDrones {
	namespace NSMission {

		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;
		/**
		 * @struct CapabilityRequirement
		 * @brief 定义任务对执行者（无人机）的能力要求。
		 */
		struct CapabilityRequirement {
			// --- 基本要求 ---
			std::optional<UavType> required_uav_type = std::nullopt; // (可选) 要求的无人机类型
			size_t min_required_count = 1;         // 最小需要的无人机数量 (默认为 1)
			size_t max_required_count = 1;         // 最大允许的无人机数量 (默认为 1)

			// --- 载荷要求 ---
			std::vector<std::string> required_payloads; // (可选) 必须携带的载荷名称列表
			std::vector<std::string> preferred_payloads; // (可选) 倾向携带的载荷名称列表

			// --- 性能要求 ---
			std::optional<Time> min_endurance_time = std::nullopt; // (可选) 最小续航时间要求 (秒)
			std::optional<double> min_range = std::nullopt;      // (可选) 最小航程要求 (米)
			std::optional<double> min_payload_capacity = std::nullopt; // (可选) 最小载荷能力 (千克)
			// ... 可以添加其他性能要求，如最大速度、抗风能力等

			// --- 构造函数  ---
			CapabilityRequirement() = default; // 默认构造

			/** @brief 设置所需无人机类型 */
			CapabilityRequirement& setType(UavType type) { required_uav_type = type; return *this; }
			/** @brief 设置所需数量 (最小和最大) */
			CapabilityRequirement& setCount(size_t min_count, size_t max_count = 0) {
				min_required_count = std::max(size_t(1), min_count); // 至少需要 1 架
				max_required_count = (max_count == 0) ? min_required_count : std::max(min_required_count, max_count); // 如果 max 为 0，则等于 min
				return *this;
			}
			/** @brief 添加必需载荷 */
			CapabilityRequirement& addRequiredPayload(const std::string& payload_name) {
				if (!payload_name.empty()) required_payloads.push_back(payload_name);
				return *this;
			}
			/** @brief 添加倾向载荷 */
			CapabilityRequirement& addPreferredPayload(const std::string& payload_name) {
				if (!payload_name.empty()) preferred_payloads.push_back(payload_name);
				return *this;
			}
			/** @brief 设置最小续航时间 */
			CapabilityRequirement& setMinEndurance(Time duration) { min_endurance_time = std::max(0.0, duration); return *this; }
			/** @brief 设置最小航程 */
			CapabilityRequirement& setMinRange(double range) { min_range = std::max(0.0, range); return *this; }
			/** @brief 设置最小载荷能力 */
			CapabilityRequirement& setMinPayloadCapacity(double capacity) { min_payload_capacity = std::max(0.0, capacity); return *this; }


			/**
			 * @brief 检查需求是否有效 (基本检查)。
			 * @return 如果有效返回 true。
			 */
			bool isEmpty() const {
				return min_required_count > 0 && max_required_count >= min_required_count;
			}
		};

	} // namespace NSMission
} // namespace NSDrones