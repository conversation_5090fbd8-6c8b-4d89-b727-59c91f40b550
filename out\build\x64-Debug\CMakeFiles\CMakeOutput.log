The system is: Windows - 10.0.26100 - AMD64
Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.35.32215/bin/Hostx64/x64/cl.exe 
Build flags: 
Id flags:  

The output was:
0
用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.35.32216.1 版
版权所有(C) Microsoft Corporation。保留所有权利。

CMakeCXXCompilerId.cpp
Microsoft (R) Incremental Linker Version 14.35.32216.1
Copyright (C) Microsoft Corporation.  All rights reserved.

/out:CMakeCXXCompilerId.exe 
CMakeCXXCompilerId.obj 


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.exe"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.obj"

The CXX compiler identification is MSVC, found in "E:/source/dronesplanning/out/build/x64-Debug/CMakeFiles/3.25.1-msvc1/CompilerIdCXX/CMakeCXXCompilerId.exe"

Detecting CXX compiler ABI info compiled with the following output:
Change Dir: E:/source/dronesplanning/out/build/x64-Debug/CMakeFiles/CMakeScratch/TryCompile-ao9nv0

Run Build Command(s):C:/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe cmTC_1f2ce && [1/2] Building CXX object CMakeFiles\cmTC_1f2ce.dir\CMakeCXXCompilerABI.cpp.obj

[2/2] Linking CXX executable cmTC_1f2ce.exe




