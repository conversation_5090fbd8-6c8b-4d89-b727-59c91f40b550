﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{D597C171-A85F-3714-B37D-519B08B0C1BF}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22000.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>drones_planning</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\source\dronesplanning\build\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">drones_planning.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">drones_planning</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\source\dronesplanning\build\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">drones_planning.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">drones_planning</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\source\dronesplanning\build\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">drones_planning.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">drones_planning</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\source\dronesplanning\build\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">drones_planning.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">drones_planning</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\source\dronesplanning\include;E:\source\third_party\install\tinyspline\include;e:\source\third_party\json\include;e:\source\third_party\json\single_include;e:\source\third_party\spdlog\include;e:\source\third_party\uuid;e:\source\third_party\earcut\include;e:\source\third_party\magic_enum\include\magic_enum;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/source/third_party/install/boost_1_87_0" /external:I "E:/source/third_party/install/ompl/release/include/ompl-1.7" /external:I "C:/vcpkg/installed/x64-windows/include/eigen3" /external:I "C:/vcpkg/installed/x64-windows/include" /external:I "E:/source/third_party/install/geographiclib/include" /external:I "E:/source/third_party/install/grid_map_core/release/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_USE_MATH_DEFINES;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_SERIALIZATION_NO_LIB;GEOGRAPHICLIB_SHARED_LIB=1;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_USE_MATH_DEFINES;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_SERIALIZATION_NO_LIB;GEOGRAPHICLIB_SHARED_LIB=1;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\source\dronesplanning\include;E:\source\third_party\install\tinyspline\include;e:\source\third_party\json\include;e:\source\third_party\json\single_include;e:\source\third_party\spdlog\include;e:\source\third_party\uuid;e:\source\third_party\earcut\include;e:\source\third_party\magic_enum\include\magic_enum;E:\source\third_party\install\boost_1_87_0;E:\source\third_party\install\ompl\release\include\ompl-1.7;C:\vcpkg\installed\x64-windows\include\eigen3;C:\vcpkg\installed\x64-windows\include;E:\source\third_party\install\geographiclib\include;E:\source\third_party\install\grid_map_core\release\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\source\dronesplanning\include;E:\source\third_party\install\tinyspline\include;e:\source\third_party\json\include;e:\source\third_party\json\single_include;e:\source\third_party\spdlog\include;e:\source\third_party\uuid;e:\source\third_party\earcut\include;e:\source\third_party\magic_enum\include\magic_enum;E:\source\third_party\install\boost_1_87_0;E:\source\third_party\install\ompl\release\include\ompl-1.7;C:\vcpkg\installed\x64-windows\include\eigen3;C:\vcpkg\installed\x64-windows\include;E:\source\third_party\install\geographiclib\include;E:\source\third_party\install\grid_map_core\release\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>拷贝 data 目录</Message>
      <Command>setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E copy_directory E:/source/dronesplanning/data E:/source/dronesplanning/build/Debug/data
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_system-vc143-mt-gd-x64-1_87.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_filesystem-vc143-mt-gd-x64-1_87.lib;E:\source\third_party\install\ompl\release\lib\ompl.lib;C:\vcpkg\installed\x64-windows\debug\lib\fcl.lib;E:\source\third_party\install\geographiclib\lib\GeographicLib-i.lib;E:\source\third_party\install\grid_map_core\release\lib\grid_map_core.lib;C:\vcpkg\installed\x64-windows\lib\gdal.lib;tinysplinecxx.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_filesystem-vc143-mt-gd-x64-1_87.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_system-vc143-mt-gd-x64-1_87.lib;bcrypt.lib;synchronization.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_atomic-vc143-mt-gd-x64-1_87.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_serialization-vc143-mt-gd-x64-1_87.lib;C:\vcpkg\installed\x64-windows\debug\lib\ccd.lib;C:\vcpkg\installed\x64-windows\debug\lib\octomap.lib;C:\vcpkg\installed\x64-windows\debug\lib\octomath.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/source/dronesplanning/build/Debug/drones_planning.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/source/dronesplanning/build/Debug/drones_planning.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\source\dronesplanning\include;E:\source\third_party\install\tinyspline\include;e:\source\third_party\json\include;e:\source\third_party\json\single_include;e:\source\third_party\spdlog\include;e:\source\third_party\uuid;e:\source\third_party\earcut\include;e:\source\third_party\magic_enum\include\magic_enum;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/source/third_party/install/boost_1_87_0" /external:I "E:/source/third_party/install/ompl/release/include/ompl-1.7" /external:I "C:/vcpkg/installed/x64-windows/include/eigen3" /external:I "C:/vcpkg/installed/x64-windows/include" /external:I "E:/source/third_party/install/geographiclib/include" /external:I "E:/source/third_party/install/grid_map_core/release/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_USE_MATH_DEFINES;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_SERIALIZATION_NO_LIB;GEOGRAPHICLIB_SHARED_LIB=1;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_USE_MATH_DEFINES;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_SERIALIZATION_NO_LIB;GEOGRAPHICLIB_SHARED_LIB=1;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\source\dronesplanning\include;E:\source\third_party\install\tinyspline\include;e:\source\third_party\json\include;e:\source\third_party\json\single_include;e:\source\third_party\spdlog\include;e:\source\third_party\uuid;e:\source\third_party\earcut\include;e:\source\third_party\magic_enum\include\magic_enum;E:\source\third_party\install\boost_1_87_0;E:\source\third_party\install\ompl\release\include\ompl-1.7;C:\vcpkg\installed\x64-windows\include\eigen3;C:\vcpkg\installed\x64-windows\include;E:\source\third_party\install\geographiclib\include;E:\source\third_party\install\grid_map_core\release\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\source\dronesplanning\include;E:\source\third_party\install\tinyspline\include;e:\source\third_party\json\include;e:\source\third_party\json\single_include;e:\source\third_party\spdlog\include;e:\source\third_party\uuid;e:\source\third_party\earcut\include;e:\source\third_party\magic_enum\include\magic_enum;E:\source\third_party\install\boost_1_87_0;E:\source\third_party\install\ompl\release\include\ompl-1.7;C:\vcpkg\installed\x64-windows\include\eigen3;C:\vcpkg\installed\x64-windows\include;E:\source\third_party\install\geographiclib\include;E:\source\third_party\install\grid_map_core\release\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>拷贝 data 目录</Message>
      <Command>setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E copy_directory E:/source/dronesplanning/data E:/source/dronesplanning/build/Release/data
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_system-vc143-mt-x64-1_87.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_filesystem-vc143-mt-x64-1_87.lib;E:\source\third_party\install\ompl\release\lib\ompl.lib;C:\vcpkg\installed\x64-windows\lib\fcl.lib;E:\source\third_party\install\geographiclib\lib\GeographicLib-i.lib;E:\source\third_party\install\grid_map_core\release\lib\grid_map_core.lib;C:\vcpkg\installed\x64-windows\lib\gdal.lib;E:\source\third_party\install\tinyspline\lib64\tinysplinecxx.lib;bcrypt.lib;synchronization.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_atomic-vc143-mt-x64-1_87.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_serialization-vc143-mt-x64-1_87.lib;C:\vcpkg\installed\x64-windows\lib\ccd.lib;C:\vcpkg\installed\x64-windows\lib\octomap.lib;C:\vcpkg\installed\x64-windows\lib\octomath.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/source/dronesplanning/build/Release/drones_planning.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/source/dronesplanning/build/Release/drones_planning.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\source\dronesplanning\include;E:\source\third_party\install\tinyspline\include;e:\source\third_party\json\include;e:\source\third_party\json\single_include;e:\source\third_party\spdlog\include;e:\source\third_party\uuid;e:\source\third_party\earcut\include;e:\source\third_party\magic_enum\include\magic_enum;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/source/third_party/install/boost_1_87_0" /external:I "E:/source/third_party/install/ompl/release/include/ompl-1.7" /external:I "C:/vcpkg/installed/x64-windows/include/eigen3" /external:I "C:/vcpkg/installed/x64-windows/include" /external:I "E:/source/third_party/install/geographiclib/include" /external:I "E:/source/third_party/install/grid_map_core/release/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_USE_MATH_DEFINES;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_SERIALIZATION_NO_LIB;GEOGRAPHICLIB_SHARED_LIB=1;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_USE_MATH_DEFINES;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_SERIALIZATION_NO_LIB;GEOGRAPHICLIB_SHARED_LIB=1;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\source\dronesplanning\include;E:\source\third_party\install\tinyspline\include;e:\source\third_party\json\include;e:\source\third_party\json\single_include;e:\source\third_party\spdlog\include;e:\source\third_party\uuid;e:\source\third_party\earcut\include;e:\source\third_party\magic_enum\include\magic_enum;E:\source\third_party\install\boost_1_87_0;E:\source\third_party\install\ompl\release\include\ompl-1.7;C:\vcpkg\installed\x64-windows\include\eigen3;C:\vcpkg\installed\x64-windows\include;E:\source\third_party\install\geographiclib\include;E:\source\third_party\install\grid_map_core\release\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\source\dronesplanning\include;E:\source\third_party\install\tinyspline\include;e:\source\third_party\json\include;e:\source\third_party\json\single_include;e:\source\third_party\spdlog\include;e:\source\third_party\uuid;e:\source\third_party\earcut\include;e:\source\third_party\magic_enum\include\magic_enum;E:\source\third_party\install\boost_1_87_0;E:\source\third_party\install\ompl\release\include\ompl-1.7;C:\vcpkg\installed\x64-windows\include\eigen3;C:\vcpkg\installed\x64-windows\include;E:\source\third_party\install\geographiclib\include;E:\source\third_party\install\grid_map_core\release\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>拷贝 data 目录</Message>
      <Command>setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E copy_directory E:/source/dronesplanning/data E:/source/dronesplanning/build/MinSizeRel/data
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_system-vc143-mt-x64-1_87.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_filesystem-vc143-mt-x64-1_87.lib;E:\source\third_party\install\ompl\release\lib\ompl.lib;C:\vcpkg\installed\x64-windows\debug\lib\fcl.lib;E:\source\third_party\install\geographiclib\lib\GeographicLib-i.lib;E:\source\third_party\install\grid_map_core\release\lib\grid_map_core.lib;C:\vcpkg\installed\x64-windows\lib\gdal.lib;tinysplinecxx.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_filesystem-vc143-mt-x64-1_87.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_system-vc143-mt-x64-1_87.lib;bcrypt.lib;synchronization.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_atomic-vc143-mt-x64-1_87.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_serialization-vc143-mt-x64-1_87.lib;C:\vcpkg\installed\x64-windows\debug\lib\ccd.lib;C:\vcpkg\installed\x64-windows\debug\lib\octomap.lib;C:\vcpkg\installed\x64-windows\debug\lib\octomath.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/source/dronesplanning/build/MinSizeRel/drones_planning.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/source/dronesplanning/build/MinSizeRel/drones_planning.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\source\dronesplanning\include;E:\source\third_party\install\tinyspline\include;e:\source\third_party\json\include;e:\source\third_party\json\single_include;e:\source\third_party\spdlog\include;e:\source\third_party\uuid;e:\source\third_party\earcut\include;e:\source\third_party\magic_enum\include\magic_enum;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/source/third_party/install/boost_1_87_0" /external:I "E:/source/third_party/install/ompl/release/include/ompl-1.7" /external:I "C:/vcpkg/installed/x64-windows/include/eigen3" /external:I "C:/vcpkg/installed/x64-windows/include" /external:I "E:/source/third_party/install/geographiclib/include" /external:I "E:/source/third_party/install/grid_map_core/release/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_USE_MATH_DEFINES;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_SERIALIZATION_NO_LIB;GEOGRAPHICLIB_SHARED_LIB=1;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_USE_MATH_DEFINES;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_SERIALIZATION_NO_LIB;GEOGRAPHICLIB_SHARED_LIB=1;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\source\dronesplanning\include;E:\source\third_party\install\tinyspline\include;e:\source\third_party\json\include;e:\source\third_party\json\single_include;e:\source\third_party\spdlog\include;e:\source\third_party\uuid;e:\source\third_party\earcut\include;e:\source\third_party\magic_enum\include\magic_enum;E:\source\third_party\install\boost_1_87_0;E:\source\third_party\install\ompl\release\include\ompl-1.7;C:\vcpkg\installed\x64-windows\include\eigen3;C:\vcpkg\installed\x64-windows\include;E:\source\third_party\install\geographiclib\include;E:\source\third_party\install\grid_map_core\release\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\source\dronesplanning\include;E:\source\third_party\install\tinyspline\include;e:\source\third_party\json\include;e:\source\third_party\json\single_include;e:\source\third_party\spdlog\include;e:\source\third_party\uuid;e:\source\third_party\earcut\include;e:\source\third_party\magic_enum\include\magic_enum;E:\source\third_party\install\boost_1_87_0;E:\source\third_party\install\ompl\release\include\ompl-1.7;C:\vcpkg\installed\x64-windows\include\eigen3;C:\vcpkg\installed\x64-windows\include;E:\source\third_party\install\geographiclib\include;E:\source\third_party\install\grid_map_core\release\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>拷贝 data 目录</Message>
      <Command>setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E copy_directory E:/source/dronesplanning/data E:/source/dronesplanning/build/RelWithDebInfo/data
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_system-vc143-mt-x64-1_87.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_filesystem-vc143-mt-x64-1_87.lib;E:\source\third_party\install\ompl\release\lib\ompl.lib;C:\vcpkg\installed\x64-windows\debug\lib\fcl.lib;E:\source\third_party\install\geographiclib\lib\GeographicLib-i.lib;E:\source\third_party\install\grid_map_core\release\lib\grid_map_core.lib;C:\vcpkg\installed\x64-windows\lib\gdal.lib;tinysplinecxx.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_filesystem-vc143-mt-x64-1_87.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_system-vc143-mt-x64-1_87.lib;bcrypt.lib;synchronization.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_atomic-vc143-mt-x64-1_87.lib;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\libboost_serialization-vc143-mt-x64-1_87.lib;C:\vcpkg\installed\x64-windows\debug\lib\ccd.lib;C:\vcpkg\installed\x64-windows\debug\lib\octomap.lib;C:\vcpkg\installed\x64-windows\debug\lib\octomath.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/source/dronesplanning/build/RelWithDebInfo/drones_planning.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/source/dronesplanning/build/RelWithDebInfo/drones_planning.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\source\dronesplanning\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/source/dronesplanning/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/source/dronesplanning -BE:/source/dronesplanning/build --check-stamp-file E:/source/dronesplanning/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindBoost.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindGDAL.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-config-version.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-config.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-targets.cmake;C:\vcpkg\installed\x64-windows\share\eigen3\Eigen3Config.cmake;C:\vcpkg\installed\x64-windows\share\eigen3\Eigen3ConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\eigen3\Eigen3Targets.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-config-version.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-config.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-targets.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-config-version.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-config.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-targets.cmake;E:\source\dronesplanning\build\CMakeFiles\3.25.1-msvc1\CMakeCXXCompiler.cmake;E:\source\dronesplanning\build\CMakeFiles\3.25.1-msvc1\CMakeRCCompiler.cmake;E:\source\dronesplanning\build\CMakeFiles\3.25.1-msvc1\CMakeSystem.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\Boost-1.87.0\BoostConfig.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\Boost-1.87.0\BoostConfigVersion.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\BoostDetectToolset-1.87.0.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\boost_atomic-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\boost_atomic-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\boost_filesystem-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\boost_filesystem-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_headers-1.87.0\boost_headers-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_headers-1.87.0\boost_headers-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\boost_serialization-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\boost_serialization-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\boost_system-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\boost_system-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-config-version.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-config.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-targets-release.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-targets.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreConfig.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreConfigVersion.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreTargets-release.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreTargets.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplConfig.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplConfigVersion.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplExport-release.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplExport.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-config-version.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-config.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-targets-release.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-targets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\source\dronesplanning\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/source/dronesplanning/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/source/dronesplanning -BE:/source/dronesplanning/build --check-stamp-file E:/source/dronesplanning/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindBoost.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindGDAL.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-config-version.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-config.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-targets.cmake;C:\vcpkg\installed\x64-windows\share\eigen3\Eigen3Config.cmake;C:\vcpkg\installed\x64-windows\share\eigen3\Eigen3ConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\eigen3\Eigen3Targets.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-config-version.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-config.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-targets.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-config-version.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-config.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-targets.cmake;E:\source\dronesplanning\build\CMakeFiles\3.25.1-msvc1\CMakeCXXCompiler.cmake;E:\source\dronesplanning\build\CMakeFiles\3.25.1-msvc1\CMakeRCCompiler.cmake;E:\source\dronesplanning\build\CMakeFiles\3.25.1-msvc1\CMakeSystem.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\Boost-1.87.0\BoostConfig.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\Boost-1.87.0\BoostConfigVersion.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\BoostDetectToolset-1.87.0.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\boost_atomic-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\boost_atomic-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\boost_filesystem-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\boost_filesystem-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_headers-1.87.0\boost_headers-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_headers-1.87.0\boost_headers-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\boost_serialization-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\boost_serialization-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\boost_system-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\boost_system-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-config-version.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-config.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-targets-release.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-targets.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreConfig.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreConfigVersion.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreTargets-release.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreTargets.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplConfig.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplConfigVersion.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplExport-release.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplExport.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-config-version.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-config.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-targets-release.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-targets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\source\dronesplanning\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule E:/source/dronesplanning/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/source/dronesplanning -BE:/source/dronesplanning/build --check-stamp-file E:/source/dronesplanning/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindBoost.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindGDAL.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-config-version.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-config.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-targets.cmake;C:\vcpkg\installed\x64-windows\share\eigen3\Eigen3Config.cmake;C:\vcpkg\installed\x64-windows\share\eigen3\Eigen3ConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\eigen3\Eigen3Targets.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-config-version.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-config.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-targets.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-config-version.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-config.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-targets.cmake;E:\source\dronesplanning\build\CMakeFiles\3.25.1-msvc1\CMakeCXXCompiler.cmake;E:\source\dronesplanning\build\CMakeFiles\3.25.1-msvc1\CMakeRCCompiler.cmake;E:\source\dronesplanning\build\CMakeFiles\3.25.1-msvc1\CMakeSystem.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\Boost-1.87.0\BoostConfig.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\Boost-1.87.0\BoostConfigVersion.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\BoostDetectToolset-1.87.0.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\boost_atomic-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\boost_atomic-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\boost_filesystem-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\boost_filesystem-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_headers-1.87.0\boost_headers-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_headers-1.87.0\boost_headers-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\boost_serialization-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\boost_serialization-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\boost_system-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\boost_system-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-config-version.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-config.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-targets-release.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-targets.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreConfig.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreConfigVersion.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreTargets-release.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreTargets.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplConfig.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplConfigVersion.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplExport-release.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplExport.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-config-version.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-config.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-targets-release.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-targets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\source\dronesplanning\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule E:/source/dronesplanning/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/source/dronesplanning -BE:/source/dronesplanning/build --check-stamp-file E:/source/dronesplanning/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindBoost.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindGDAL.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-config-version.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-config.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\ccd\ccd-targets.cmake;C:\vcpkg\installed\x64-windows\share\eigen3\Eigen3Config.cmake;C:\vcpkg\installed\x64-windows\share\eigen3\Eigen3ConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\eigen3\Eigen3Targets.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-config-version.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-config.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\fcl\fcl-targets.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-config-version.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-config.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\octomap\octomap-targets.cmake;E:\source\dronesplanning\build\CMakeFiles\3.25.1-msvc1\CMakeCXXCompiler.cmake;E:\source\dronesplanning\build\CMakeFiles\3.25.1-msvc1\CMakeRCCompiler.cmake;E:\source\dronesplanning\build\CMakeFiles\3.25.1-msvc1\CMakeSystem.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\Boost-1.87.0\BoostConfig.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\Boost-1.87.0\BoostConfigVersion.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\BoostDetectToolset-1.87.0.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\boost_atomic-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\boost_atomic-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_atomic-1.87.0\libboost_atomic-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\boost_filesystem-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\boost_filesystem-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_filesystem-1.87.0\libboost_filesystem-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_headers-1.87.0\boost_headers-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_headers-1.87.0\boost_headers-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\boost_serialization-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\boost_serialization-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_serialization-1.87.0\libboost_serialization-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\boost_system-config-version.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\boost_system-config.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-gd-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-gd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-s-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-sgd-x64-1_87-static.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-x64-1_87-shared.cmake;E:\source\third_party\install\boost_1_87_0\lib64-msvc-14.3\cmake\boost_system-1.87.0\libboost_system-variant-vc143-mt-x64-1_87-static.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-config-version.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-config.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-targets-release.cmake;E:\source\third_party\install\geographiclib\lib\cmake\GeographicLib\geographiclib-targets.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreConfig.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreConfigVersion.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreTargets-release.cmake;E:\source\third_party\install\grid_map_core\release\lib\cmake\grid_map_core\grid_map_coreTargets.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplConfig.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplConfigVersion.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplExport-release.cmake;E:\source\third_party\install\ompl\release\share\ompl\cmake\omplExport.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-config-version.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-config.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-targets-release.cmake;E:\source\third_party\install\tinyspline\lib64\cmake\tinysplinecxx\tinysplinecxx-targets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\source\dronesplanning\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\utils\coordinate_converter.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\algorithm\allocator\task_allocator.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\algorithm\evaluator\energy_evaluator.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\algorithm\path_planner\ipath_planner.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\algorithm\path_planner\rrtstar_planner.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\algorithm\trajectory_optimizer\trajectory_optimizer.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\config.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\base_object.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\entity_object.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\entity_state.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\ishape.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shape_factory.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\box_shape.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\capsule_shape.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\compound_shape.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\cone_shape.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\convex_hull_shape.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\cylinder_shape.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\ellipsoid_shape.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\line_shape.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\mesh_shape.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\plane_shape.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\point_shape.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\polygon_shape.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\geometry\shapes\sphere_shape.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\core\movement_strategy.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\environment\collision\collision_engine.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\environment\coordinate\coordinate_manager.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\environment\coordinate\task_space.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\environment\entities\obstacle.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\environment\entities\zone.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\environment\environment.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\environment\indices\bvh_spatial_index.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\environment\maps\igridmap.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\environment\maps\tiled_gridmap.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\main.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\mission\mission.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\mission\task.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\params\param_defs.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\params\param_json.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\params\parameters.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\params\paramregistry.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\planning\itask_planner.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\planning\mission_planner.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\planning\planning_result.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\planning\planning_types.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\uav\dynamics\fixedwing_dynamics.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\uav\dynamics\multirotor_dynamics.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\uav\dynamics\vtol_dynamics.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\uav\energies\fixedwing_energies.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\uav\energies\multirotor_energies.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\uav\energies\vtol_energies.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\uav\flight_strategy.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\uav\idynamic_model.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\uav\ienergy_model.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\uav\uav.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\uav\uav_config.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\utils\enum_utils.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\utils\file_utils.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\utils\logging.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\utils\object_id.cpp" />
    <ClCompile Include="E:\source\dronesplanning\src\utils\orientation_utils.cpp" />
    <ClInclude Include="..\include\utils\coordinate_converter.h" />
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\algorithm_object.h" />
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\allocator\itask_allocator.h" />
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\allocator\task_allocator.h" />
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\evaluator\energy_evaluator.h" />
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\evaluator\itrajectory_evaluator.h" />
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\path_planner\ipath_planner.h" />
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\path_planner\rrtstar_planner.h" />
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\trajectory_optimizer\itrajectory_optimizer.h" />
    <ClInclude Include="E:\source\dronesplanning\include\algorithm\trajectory_optimizer\trajectory_optimizer.h" />
    <ClInclude Include="E:\source\dronesplanning\include\config.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\base_object.h" />
    <None Include="E:\source\dronesplanning\include\core\base_object.tpp">
    </None>
    <ClInclude Include="E:\source\dronesplanning\include\core\entity_object.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\entity_state.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\ishape.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shape_factory.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\box_shape.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\capsule_shape.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\compound_shape.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\cone_shape.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\convex_hull_shape.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\cylinder_shape.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\ellipsoid_shape.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\line_shape.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\mesh_shape.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\plane_shape.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\point_shape.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\polygon_shape.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\geometry\shapes\sphere_shape.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\movement_strategy.h" />
    <ClInclude Include="E:\source\dronesplanning\include\core\types.h" />
    <ClInclude Include="E:\source\dronesplanning\include\drones.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\collision\collision_engine.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\collision\collision_types.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\collision\detector_registry.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\coordinate\coordinate_manager.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\coordinate\task_space.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\entities\obstacle.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\entities\zone.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\environment.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\environment_fwd.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\geometry\geometry_manager.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\indices\attribute_index.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\indices\bvh_spatial_index.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\indices\ispatial_index.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\maps\igridmap.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\maps\single_gridmap.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\maps\tiled_gridmap.h" />
    <ClInclude Include="E:\source\dronesplanning\include\environment\storage\object_storage.h" />
    <None Include="E:\source\dronesplanning\include\environment\storage\object_storage.tpp">
    </None>
    <ClInclude Include="E:\source\dronesplanning\include\mission\capability_requirement.h" />
    <ClInclude Include="E:\source\dronesplanning\include\mission\control_point.h" />
    <ClInclude Include="E:\source\dronesplanning\include\mission\execution_strategy.h" />
    <ClInclude Include="E:\source\dronesplanning\include\mission\mission.h" />
    <ClInclude Include="E:\source\dronesplanning\include\mission\mission_fwd.h" />
    <ClInclude Include="E:\source\dronesplanning\include\mission\task.h" />
    <ClInclude Include="E:\source\dronesplanning\include\mission\task_fwd.h" />
    <ClInclude Include="E:\source\dronesplanning\include\mission\task_params.h" />
    <ClInclude Include="E:\source\dronesplanning\include\mission\task_strategies.h" />
    <ClInclude Include="E:\source\dronesplanning\include\mission\task_targets.h" />
    <ClInclude Include="E:\source\dronesplanning\include\params\param_defs.h" />
    <ClInclude Include="E:\source\dronesplanning\include\params\param_json.h" />
    <ClInclude Include="E:\source\dronesplanning\include\params\parameters.h" />
    <ClInclude Include="E:\source\dronesplanning\include\params\paramregistry.h" />
    <ClInclude Include="E:\source\dronesplanning\include\planning\itask_planner.h" />
    <ClInclude Include="E:\source\dronesplanning\include\planning\mission_planner.h" />
    <ClInclude Include="E:\source\dronesplanning\include\planning\planning_result.h" />
    <ClInclude Include="E:\source\dronesplanning\include\planning\planning_types.h" />
    <ClInclude Include="E:\source\dronesplanning\include\planning\task_planners\followpath_taskplanner.h" />
    <ClInclude Include="E:\source\dronesplanning\include\planning\task_planners\loiterpoint_taskplanner.h" />
    <ClInclude Include="E:\source\dronesplanning\include\planning\task_planners\scanarea_taskplanner.h" />
    <ClInclude Include="E:\source\dronesplanning\include\planning\task_planners\surveycylinder_taskplanner.h" />
    <ClInclude Include="E:\source\dronesplanning\include\planning\task_planners\surveymultipoints_taskplanner.h" />
    <ClInclude Include="E:\source\dronesplanning\include\planning\task_planners\surveysphere_taskplanner.h" />
    <ClInclude Include="E:\source\dronesplanning\include\uav\dynamics\fixedwing_dynamics.h" />
    <ClInclude Include="E:\source\dronesplanning\include\uav\dynamics\multirotor_dynamics.h" />
    <ClInclude Include="E:\source\dronesplanning\include\uav\dynamics\vtol_dynamics.h" />
    <ClInclude Include="E:\source\dronesplanning\include\uav\energies\fixedwing_energies.h" />
    <ClInclude Include="E:\source\dronesplanning\include\uav\energies\multirotor_energies.h" />
    <ClInclude Include="E:\source\dronesplanning\include\uav\energies\vtol_energies.h" />
    <ClInclude Include="E:\source\dronesplanning\include\uav\flight_strategy.h" />
    <ClInclude Include="E:\source\dronesplanning\include\uav\idynamic_model.h" />
    <ClInclude Include="E:\source\dronesplanning\include\uav\ienergy_model.h" />
    <ClInclude Include="E:\source\dronesplanning\include\uav\uav.h" />
    <ClInclude Include="E:\source\dronesplanning\include\uav\uav_config.h" />
    <ClInclude Include="E:\source\dronesplanning\include\uav\uav_fwd.h" />
    <ClInclude Include="E:\source\dronesplanning\include\uav\uav_types.h" />
    <ClInclude Include="E:\source\dronesplanning\include\utils\enum_utils.h" />
    <ClInclude Include="E:\source\dronesplanning\include\utils\file_utils.h" />
    <ClInclude Include="E:\source\dronesplanning\include\utils\logging.h" />
    <ClInclude Include="E:\source\dronesplanning\include\utils\object_id.h" />
    <ClInclude Include="E:\source\dronesplanning\include\utils\orientation_utils.h" />
    <ClInclude Include="E:\source\dronesplanning\include\utils\stopwatch.h" />
    <ClInclude Include="E:\source\dronesplanning\include\utils\thread_safe_cache.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>