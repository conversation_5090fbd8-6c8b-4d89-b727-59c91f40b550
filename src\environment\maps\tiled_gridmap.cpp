#include "environment/maps/tiled_gridmap.h"
#include "environment/maps/single_gridmap.h"
#include "utils/logging.h"
#include "utils/file_utils.h" // For NSUtils::fileExists (if needed, or use own)
#include "params/paramregistry.h" // For ParamRegistry::getInstance()
#include <algorithm> // For std::min, std::max
#include <limits>    // For std::numeric_limits
#include <filesystem> // For std::filesystem::path
#include <set>  // 添加 set 头文件
#include <spdlog/fmt/ostr.h>
#include <spdlog/fmt/fmt.h>
#include <spdlog/fmt/ranges.h>

namespace NSDrones {
	namespace NSEnvironment {

		// 构造函数
		TiledGridMap::TiledGridMap(LayerConfig config)
			: layerConfig_(std::move(config)) {
			LOG_INFO("TiledGridMap 构造函数 - 初始化 (空), 使用图层配置: 高程='{}', 地物='{}', 地物高度='{}', 目标分辨率={}m",
				layerConfig_.elevationLayer, layerConfig_.featureTypeLayer,
				layerConfig_.featureHeightLayer, layerConfig_.targetResolution);
		}

		// 析构函数
		TiledGridMap::~TiledGridMap() {
			LOG_DEBUG("TiledGridMap 析构函数 - 释放 {} 个瓦片资源", single_gridmaps_.size());
			// 缓存将自动释放内存，但为了更好的调试，我们显式清空
			single_gridmaps_.clear();
		}

		// 重新实现的初始化方法
		bool TiledGridMap::initialize(std::shared_ptr<NSParams::ParamValues> global_params) {
			LOG_INFO("TiledGridMap::initialize - 开始初始化瓦片地图");

			if (!global_params) {
				LOG_ERROR("TiledGridMap::initialize - 全局参数为空");
				return false;
			}

			try {
				// 步骤1：从全局参数获取地图配置
				auto map_enabled = global_params->getValueOrDefault<bool>("map.enabled", true);
				if (!map_enabled) {
					LOG_INFO("TiledGridMap: 地图功能已禁用");
					initialized_ = true;
					return true;
				}

				auto data_directory = global_params->getValueOrDefault<std::string>("map.data_directory", "terrain");
				auto load_mode = global_params->getValueOrDefault<std::string>("map.load_mode", "geotiff");
				auto auto_convert = global_params->getValueOrDefault<bool>("map.auto_convert", true);
				auto cache_directory = global_params->getValueOrDefault<std::string>("map.cache_directory", "terrain/cache");

				// 获取数据根目录用于路径解析
				auto data_root = NSUtils::getDataPath();
				LOG_DEBUG("TiledGridMap: 使用数据根目录: '{}'", data_root.generic_string());

				// 解析数据目录路径（支持相对路径和绝对路径）
				auto resolved_data_directory = NSUtils::resolvePath(data_root, std::filesystem::path(data_directory));
				if (resolved_data_directory.empty()) {
					LOG_ERROR("TiledGridMap: 无法解析数据目录路径: '{}'", data_directory);
					return false;
				}
				LOG_DEBUG("TiledGridMap: 解析后的数据目录: '{}'", resolved_data_directory.generic_string());

				// 解析缓存目录路径
				auto resolved_cache_directory = NSUtils::resolvePath(data_root, std::filesystem::path(cache_directory));
				if (resolved_cache_directory.empty()) {
					LOG_ERROR("TiledGridMap: 无法解析缓存目录路径: '{}'", cache_directory);
					return false;
				}

				LOG_INFO("TiledGridMap: 配置 - 数据目录: '{}', 加载模式: '{}', 自动转换: {}, 缓存目录: '{}'",
						 resolved_data_directory.generic_string(), load_mode, auto_convert, resolved_cache_directory.generic_string());

				// 步骤2：迭代地图数据目录，扫描文件
				std::vector<std::string> map_files = scanMapDataDirectory(resolved_data_directory.generic_string(), load_mode);
				if (map_files.empty()) {
					LOG_WARN("TiledGridMap: 在目录 '{}' 中未找到任何地图文件", resolved_data_directory.generic_string());
					initialized_ = true; // 标记为已初始化，但没有瓦片
					return true;
				}

				LOG_INFO("TiledGridMap: 找到 {} 个地图文件", map_files.size());

				// 步骤3：依托 SingleGridMap 加载数据源
				single_gridmaps_.clear();
				bool all_success = true;

				// 保存全局参数用于后续动态加载
				globalParams_ = global_params;

				for (const auto& file_path : map_files) {
					LOG_INFO("TiledGridMap: 正在加载地图文件: '{}'", file_path);

					// 使用 initialize 方法传递全局参数，确保坐标系统一致性
					std::shared_ptr<SingleGridMap> single_map = std::make_shared<SingleGridMap>();

					// 创建临时参数对象，添加文件路径信息
					auto temp_params = std::make_shared<NSParams::ParamValues>(*global_params);

					// 获取ParamRegistry实例用于setValue调用
					const auto& registry = NSParams::ParamRegistry::getInstance();
					temp_params->setValue("map.file_path", file_path, registry);
					temp_params->setValue("map.load_mode", load_mode, registry);

					if (single_map->initialize(temp_params)) {
						LOG_DEBUG("TiledGridMap: 成功加载地图文件: '{}'", file_path);
					} else {
						LOG_WARN("TiledGridMap: 加载地图文件失败: '{}'", file_path);
						single_map = nullptr;
						all_success = false;
					}

					if (single_map && single_map->isInitialized()) {
						// 生成瓦片ID并将加载的实例存储到缓存中
						std::string tile_id = std::filesystem::path(file_path).stem().string();
						single_gridmaps_[tile_id] = single_map;

						LOG_DEBUG("TiledGridMap: 成功加载地图文件: '{}'", file_path);
					} else {
						LOG_ERROR("TiledGridMap: 加载地图文件失败: '{}'", file_path);
						all_success = false;
					}
				}

				// 步骤4：更新组合边界
				if (!single_gridmaps_.empty()) {
					updateCombinedBounds();
					initialized_ = true;
					LOG_INFO("TiledGridMap: 初始化完成，成功加载 {} 个瓦片", single_gridmaps_.size());
				} else {
					LOG_ERROR("TiledGridMap: 没有成功加载任何瓦片");
					return false;
				}

				return all_success;

			} catch (const std::exception& e) {
				LOG_ERROR("TiledGridMap::initialize - 初始化失败: {}", e.what());
				return false;
			}
		}

		// 辅助方法实现

		// 扫描地图数据目录
		std::vector<std::string> TiledGridMap::scanMapDataDirectory(const std::string& data_directory, const std::string& load_mode) {
			std::vector<std::string> map_files;

			// 使用 file_utils 检查目录是否存在
			if (!NSUtils::isDirExists(data_directory)) {
				LOG_WARN("TiledGridMap: 数据目录 '{}' 不存在", data_directory);
				return map_files;
			}

			// 根据加载模式确定目标扩展名
			std::vector<std::string> target_extensions;
			if (load_mode == "geotiff") {
				// 只加载.tif文件（真正的GeoTIFF数据），排除.tiff文件（可视化图片）
				target_extensions = {".tif"};
			} else if (load_mode == "tiff") {
				// 加载预处理的TIFF文件，优先.tiff后缀
				target_extensions = {".tiff"};
			} else {
				LOG_ERROR("TiledGridMap: 未知的加载模式: '{}'", load_mode);
				return map_files;
			}

			LOG_DEBUG("TiledGridMap: 扫描目录 '{}' 查找文件扩展名: [{}]", data_directory,
					 fmt::join(target_extensions, ", "));

			// 使用 file_utils 的 findFilesInDirectory 方法
			std::vector<std::filesystem::path> found_files;
			bool scan_success = NSUtils::findFilesInDirectory(
				std::filesystem::path(data_directory),
				target_extensions,
				false, // 不递归搜索
				found_files
			);

			if (!scan_success) {
				LOG_ERROR("TiledGridMap: 扫描目录 '{}' 失败", data_directory);
				return map_files;
			}

			// 转换路径对象为字符串，使用generic_string()确保跨平台一致性
			map_files.reserve(found_files.size());
			for (const auto& file_path : found_files) {
				map_files.push_back(file_path.generic_string());
				LOG_DEBUG("TiledGridMap: 找到地图文件: '{}'", file_path.generic_string());
			}

			LOG_INFO("TiledGridMap: 在目录 '{}' 中找到 {} 个地图文件", data_directory, map_files.size());
			return map_files;
		}

		// 更新组合边界
		void TiledGridMap::updateCombinedBounds() {
			if (single_gridmaps_.empty()) {
				combinedBoundsWGS84_ = WGS84BoundingBox(); // 无效边界
				LOG_DEBUG("TiledGridMap: 没有瓦片，设置无效边界");
				return;
			}

			// 初始化组合边界为第一个瓦片的边界
			auto first_tile = single_gridmaps_.begin();
			combinedBoundsWGS84_ = first_tile->second->getMetadata().wgs84_bounds;

			// 扩展边界以包含所有瓦片
			for (const auto& [tile_id, single_map] : single_gridmaps_) {
				if (single_map) {
					combinedBoundsWGS84_.extend(single_map->getMetadata().wgs84_bounds);
				}
			}

			LOG_INFO("TiledGridMap: 更新组合边界完成，组合边界: {}", combinedBoundsWGS84_.toString());
		}

		// IGridMap 接口实现
		std::optional<double> TiledGridMap::getElevation(double latitude, double longitude) const {
			if (!initialized_) return std::nullopt;

			std::shared_ptr<SingleGridMap> best_tile = nullptr;
			std::string best_tile_id;
			double best_resolution = std::numeric_limits<double>::max(); // 分辨率越小越好

			// LOG_TRACE("TiledGridMap::getElevation - 开始为点 ({}, {}) 查找最佳瓦片...", latitude, longitude);

			for (const auto& [tile_id, single_map] : single_gridmaps_) {
				if (!single_map) continue;

				// 使用SingleGridMap的isCovered方法检查实际数据覆盖
				if (single_map->isCovered(latitude, longitude)) {
					const auto& metadata = single_map->getMetadata();
					// 使用几何平均分辨率进行比较，平衡X和Y方向的精度
					double current_resolution = std::sqrt(metadata.resolution.first * metadata.resolution.second);
					LOG_TRACE("  瓦片 '{}' 的分辨率为: ({:.3f}, {:.3f}), 最高精度: {:.3f}",
						tile_id, metadata.resolution.first, metadata.resolution.second, current_resolution);

					// 分辨率 > 0 表示有效，且越小越好
					if (current_resolution > 0 && current_resolution < best_resolution) {
						best_resolution = current_resolution;
						best_tile = single_map;
						best_tile_id = tile_id;
						LOG_DEBUG("  找到新的最佳瓦片 '{}' (因为其分辨率 {:.3f} 更高，优于之前的 {:.3f})。",
							tile_id, current_resolution, best_resolution);
					}
					else if (current_resolution > 0 && current_resolution == best_resolution) {
						// 分辨率相同，我们保留第一个遇到的
						LOG_TRACE("  瓦片 '{}' 的分辨率 {:.3f} 与当前最佳瓦片相同，保留第一个匹配到的。",
							tile_id, current_resolution);
					}
					else if (current_resolution <= 0) {
						LOG_WARN("  瓦片 '{}' 返回无效的分辨率 {:.3f}，已忽略。", tile_id, current_resolution);
					}
				}
			}

			if (best_tile) {
				// LOG_INFO("TiledGridMap::getElevation - 为点 ({},{}) 选择的最佳瓦片为 '{}' (分辨率: {:.3f})",
				//	latitude, longitude, best_tile_id, best_resolution);

				return best_tile->getElevation(latitude, longitude);
			}

			LOG_DEBUG("TiledGridMap::getElevation - 查询点 ({},{}) 未被任何有效瓦片覆盖或所有覆盖瓦片分辨率无效。", latitude, longitude);
			return std::nullopt;
		}

		std::optional<FeatureType> TiledGridMap::getFeature(double latitude, double longitude) const {
			if (!initialized_) return std::nullopt;

			std::shared_ptr<SingleGridMap> best_tile = nullptr;
			std::string best_tile_id;
			double best_resolution = std::numeric_limits<double>::max(); // 分辨率越小越好

			LOG_TRACE("TiledGridMap::getFeature - 开始为点 ({}, {}) 查找最佳瓦片...", latitude, longitude);

			for (const auto& [tile_id, single_map] : single_gridmaps_) {
				if (!single_map) continue;

				const auto& metadata = single_map->getMetadata();

				// 关键修复：对于单瓦片情况，使用组合边界进行查询，确保覆盖范围一致
				bool point_in_bounds = false;
				if (single_gridmaps_.size() == 1) {
					// 单瓦片模式：使用组合边界
					point_in_bounds = combinedBoundsWGS84_.contains(latitude, longitude);
				} else {
					// 多瓦片模式：使用瓦片自己的边界
					point_in_bounds = metadata.wgs84_bounds.contains(latitude, longitude);
				}

				if (point_in_bounds) {
					LOG_TRACE("点 ({},{}) 在瓦片 '{}' 的边界内。", latitude, longitude, tile_id);
					// 使用几何平均分辨率进行比较，平衡X和Y方向的精度
					double current_resolution = std::sqrt(metadata.resolution.first * metadata.resolution.second);
					LOG_TRACE("  瓦片 '{}' 的分辨率为: ({:.3f}, {:.3f}), 最高精度: {:.3f}",
						tile_id, metadata.resolution.first, metadata.resolution.second, current_resolution);

					if (current_resolution > 0 && current_resolution < best_resolution) {
						best_resolution = current_resolution;
						best_tile = single_map;
						best_tile_id = tile_id;
						LOG_DEBUG("  找到新的最佳瓦片 '{}' (因为其分辨率 {:.3f} 更高，优于之前的 {:.3f})。",
							tile_id, current_resolution, best_resolution);
					}
					else if (current_resolution > 0 && current_resolution == best_resolution) {
						LOG_TRACE("  瓦片 '{}' 的分辨率 {:.3f} 与当前最佳瓦片相同，保留第一个匹配到的。",
							tile_id, current_resolution);
					}
					else if (current_resolution <= 0) {
						LOG_WARN("  瓦片 '{}' 返回无效的分辨率 {:.3f}，已忽略。", tile_id, current_resolution);
					}
				}
			}

			if (best_tile) {
				// LOG_INFO("TiledGridMap::getFeature - 为点 ({},{}) 选择的最佳瓦片为 '{}' (分辨率: {:.3f})",
				//	latitude, longitude, best_tile_id, best_resolution);

				return best_tile->getFeature(latitude, longitude);
			}

			LOG_DEBUG("TiledGridMap::getFeature - 查询点 ({},{}) 未被任何有效瓦片覆盖或所有覆盖瓦片分辨率无效。", latitude, longitude);
			return std::nullopt;
		}

		std::optional<double> TiledGridMap::getFeatureHeight(double latitude, double longitude) const {
			if (!initialized_) return std::nullopt;

			std::shared_ptr<SingleGridMap> best_tile = nullptr;
			std::string best_tile_id;
			double best_resolution = std::numeric_limits<double>::max(); // 分辨率越小越好

			LOG_TRACE("TiledGridMap::getFeatureHeight - 开始为点 ({}, {}) 查找最佳瓦片...", latitude, longitude);

			for (const auto& [tile_id, single_map] : single_gridmaps_) {
				if (!single_map) continue;

				const auto& metadata = single_map->getMetadata();

				// 关键修复：对于单瓦片情况，使用组合边界进行查询，确保覆盖范围一致
				bool point_in_bounds = false;
				if (single_gridmaps_.size() == 1) {
					// 单瓦片模式：使用组合边界
					point_in_bounds = combinedBoundsWGS84_.contains(latitude, longitude);
				} else {
					// 多瓦片模式：使用瓦片自己的边界
					point_in_bounds = metadata.wgs84_bounds.contains(latitude, longitude);
				}

				if (point_in_bounds) {
					LOG_TRACE("点 ({},{}) 在瓦片 '{}' 的边界内。", latitude, longitude, tile_id);
					// 使用分辨率的最小值（最高精度）进行比较
					double current_resolution = std::min(metadata.resolution.first, metadata.resolution.second);
					LOG_TRACE("  瓦片 '{}' 的分辨率为: ({:.3f}, {:.3f}), 最高精度: {:.3f}",
						tile_id, metadata.resolution.first, metadata.resolution.second, current_resolution);

					if (current_resolution > 0 && current_resolution < best_resolution) {
						best_resolution = current_resolution;
						best_tile = single_map;
						best_tile_id = tile_id;
						LOG_DEBUG("  找到新的最佳瓦片 '{}' (因为其分辨率 {:.3f} 更高，优于之前的 {:.3f})。",
							tile_id, current_resolution, best_resolution);
					}
					else if (current_resolution > 0 && current_resolution == best_resolution) {
						LOG_TRACE("  瓦片 '{}' 的分辨率 {:.3f} 与当前最佳瓦片相同，保留第一个匹配到的。",
							tile_id, current_resolution);
					}
					else if (current_resolution <= 0) {
						LOG_WARN("  瓦片 '{}' 返回无效的分辨率 {:.3f}，已忽略。", tile_id, current_resolution);
					}
				}
			}

			if (best_tile) {
				// LOG_INFO("TiledGridMap::getFeatureHeight - 为点 ({},{}) 选择的最佳瓦片为 '{}' (分辨率: {:.3f})",
				//	latitude, longitude, best_tile_id, best_resolution);

				return best_tile->getFeatureHeight(latitude, longitude);
			}

			LOG_DEBUG("TiledGridMap::getFeatureHeight - 查询点 ({},{}) 未被任何有效瓦片覆盖或所有覆盖瓦片分辨率无效。", latitude, longitude);
			return std::nullopt;
		}

		bool TiledGridMap::isInitialized() const {
			return initialized_;
		}

		// --- TiledGridMap 特有接口实现 (旧名，现在标记为 deprecated) ---
		WGS84BoundingBox TiledGridMap::getCombinedDataSourceWGS84Bounds() const {
			LOG_WARN("TiledGridMap: getCombinedDataSourceWGS84Bounds() 已被弃用，请使用 getDataSourceWGS84Bounds()。");
			return combinedBoundsWGS84_;
		}

		bool TiledGridMap::isCovered(double latitude, double longitude) const {
			if (!initialized_) return false;

			// 遍历所有瓦片，检查是否有任何瓦片实际覆盖该点
			for (const auto& [tile_id, single_map] : single_gridmaps_) {
				if (single_map && single_map->isCovered(latitude, longitude)) {
					return true;
				}
			}
			return false;
		}

		MapMetadata TiledGridMap::getMetadata() const {
			LOG_DEBUG("TiledGridMap::getMetadata - 被调用，包含 {} 个瓦片", single_gridmaps_.size());

			if (single_gridmaps_.empty()) {
				// 返回默认的无效元数据
				LOG_WARN("TiledGridMap::getMetadata - 没有瓦片，返回无效元数据");
				return MapMetadata();
			}

			// 创建汇总元数据
			MapMetadata summary_metadata;
			summary_metadata.map_id = "tiled_map";
			summary_metadata.map_name = "Tiled Grid Map";
			summary_metadata.file_format = "tiled";
			summary_metadata.file_path = fmt::format("tiled_map_with_{}_tiles", single_gridmaps_.size());

			// 使用组合边界（这是所有瓦片的外接矩形，可能包含空白区域）
			summary_metadata.wgs84_bounds = combinedBoundsWGS84_;

			// 计算汇总统计信息
			double min_resolution_x = std::numeric_limits<double>::max();
			double min_resolution_y = std::numeric_limits<double>::max();
			int total_grid_width = 0;
			int total_grid_height = 0;
			std::set<std::string> all_layers;

			for (const auto& [tile_id, single_map] : single_gridmaps_) {
				if (single_map) {
					auto tile_metadata = single_map->getMetadata();
					if (tile_metadata.isValid()) {
						// 收集最高精度（最小分辨率）
						min_resolution_x = std::min(min_resolution_x, tile_metadata.resolution.first);
						min_resolution_y = std::min(min_resolution_y, tile_metadata.resolution.second);

						// 累加网格尺寸（近似总像素数）
						total_grid_width += tile_metadata.grid_size.first;
						total_grid_height += tile_metadata.grid_size.second;

						// 收集所有图层
						for (const auto& layer : tile_metadata.available_layers) {
							all_layers.insert(layer);
						}
					}
				}
			}

			// 设置汇总信息
			summary_metadata.resolution = {min_resolution_x, min_resolution_y};
			summary_metadata.grid_size = {total_grid_width, total_grid_height};
			summary_metadata.available_layers = std::vector<std::string>(all_layers.begin(), all_layers.end());

			LOG_DEBUG("TiledGridMap: 生成汇总元数据，包含 {} 个瓦片，最高分辨率: ({:.3f}, {:.3f}), 总像素: {}x{}, 可用图层: [{}]",
				single_gridmaps_.size(), min_resolution_x, min_resolution_y,
				total_grid_width, total_grid_height, fmt::join(summary_metadata.available_layers, ", "));

			return summary_metadata;
		}

		std::vector<MapMetadata> TiledGridMap::getTileMetadataList() const {
			std::vector<MapMetadata> tiles;
			tiles.reserve(single_gridmaps_.size());

			for (const auto& [tile_id, single_map] : single_gridmaps_) {
				if (single_map) {
					auto metadata = single_map->getMetadata();
					metadata.map_id = tile_id; // 确保 map_id 正确设置为瓦片ID
					tiles.push_back(metadata);
				}
			}

			LOG_DEBUG("TiledGridMap: 返回 {} 个瓦片的元数据列表", tiles.size());
			return tiles;
		}

		std::vector<MapMetadata> TiledGridMap::getTiles() const {
			LOG_WARN("TiledGridMap: getTiles() 已被弃用，请使用 getTileMetadataList()");
			return getTileMetadataList();
		}

	} // namespace NSEnvironment
} // namespace NSDrones
