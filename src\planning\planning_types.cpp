// src/planning/planning_types.cpp
#include "planning/planning_types.h"
#include "utils/coordinate_converter.h"
#include "uav/idynamic_model.h"
#include "uav/uav_types.h"
#include "utils/logging.h"
#include "utils/object_id.h"
#include <cmath>              
#include <stdexcept>          
#include <numeric>           
#include <algorithm>         

namespace NSDrones {
	namespace NSPlanning {

		// --- PlannedRoute 实现 ---

		/**
		 * @brief PlannedRoute 构造函数。
		 * @param uav_id 关联的无人机 ID。
		 */
		PlannedRoute::PlannedRoute(ObjectID uav_id) : uav_id_(std::move(uav_id)) {
			// 检查 ID 是否有效
			if (!NSUtils::isValidObjectID(uav_id_)) {
				LOG_WARN("创建 PlannedRoute 时使用了无效的无人机 ID '{}'。", uav_id_);
				// uav_id_ 保持为空字符串
			}
			LOG_DEBUG("规划路径对象已创建，无人机 ID: '{}'", uav_id_);
		}

		/** @brief 获取关联的无人机 ID。 */
		const ObjectID& PlannedRoute::getUavId() const {
			return uav_id_;
		}

		/** @brief 获取航路点列表 (const 引用)。 */
		const RouteSegment& PlannedRoute::getWaypoints() const {
			return waypoints_;
		}

		/** @brief 获取航路点列表 (可修改引用)。 */
		RouteSegment& PlannedRoute::getWaypoints() {
			return waypoints_;
		}

		/** @brief 添加单个航路点。 */
		void PlannedRoute::addWaypoint(const RoutePoint& point) {
			LOG_TRACE("规划路径 [无人机:{}]: 尝试添加航点 t={:.4f}...", uav_id_, point.time_stamp);
			if (!waypoints_.empty()) {
				// 检查时间戳单调性
				if (point.time_stamp < waypoints_.back().time_stamp - Constants::TIME_EPSILON) {
					LOG_WARN("规划路径 [无人机:{}]: 添加的航路点时间戳 ({:.4f}) 早于前一个 ({:.4f})。可能导致问题。",
						uav_id_, point.time_stamp, waypoints_.back().time_stamp);
					// 根据策略决定是否抛出异常或忽略，这里仅记录警告
				}
				// 转换为ECEF坐标进行几何计算
				EcefPoint current_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(point.position);
				EcefPoint last_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(waypoints_.back().position);
				Vector3D pos_diff = current_ecef - last_ecef;
				// 检查位置和时间是否与上一个点过于接近（可能冗余）
				if (pos_diff.squaredNorm() < Constants::GEOMETRY_EPSILON * Constants::GEOMETRY_EPSILON &&
					std::abs(point.time_stamp - waypoints_.back().time_stamp) < Constants::TIME_EPSILON) {
					LOG_TRACE("规划路径 [无人机:{}]: 添加的航路点与上一个点位置和时间戳几乎相同，已跳过。", uav_id_);
					// **如果跳过的点有载荷动作，需要合并到上一个点**
					if (!point.payload_actions.empty()) {
						LOG_TRACE("  但跳过的点有载荷动作，将其合并到前一个点。");
						waypoints_.back().payload_actions.insert(waypoints_.back().payload_actions.end(),
							point.payload_actions.begin(), point.payload_actions.end());
					}
					return; // 跳过冗余点
				}
			}
			LOG_TRACE("  成功添加航点。当前点数: {}", waypoints_.size() + 1);
			waypoints_.push_back(point); // 添加到末尾
		}

		/** @brief 添加一个航段 (多个航路点)。 */
		void PlannedRoute::addWaypoints(const RouteSegment& segment) {
			LOG_TRACE("规划路径 [无人机:{}]: 尝试添加 {} 个航点的航段...", uav_id_, segment.size());
			if (segment.empty()) {
				LOG_TRACE("  要添加的航段为空，无操作。");
				return; // 空段无需添加
			}

			if (waypoints_.empty()) {
				LOG_TRACE("  当前路径为空，直接复制航段。");
				waypoints_ = segment; // 如果当前路径为空，直接复制
			}
			else {
				// 检查时间戳连续性
				if (segment.front().time_stamp < waypoints_.back().time_stamp - Constants::TIME_EPSILON) {
					LOG_WARN("规划路径 [无人机:{}]: 添加的航段起始时间戳 ({:.4f}) 早于路径结束时间 ({:.4f})。时间可能重叠。",
						uav_id_, segment.front().time_stamp, waypoints_.back().time_stamp);
					// 可能需要调整时间戳或报错，这里仅记录警告
				}

				// 转换为ECEF坐标进行几何计算
				EcefPoint last_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(waypoints_.back().position);
				EcefPoint first_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(segment.front().position);
				Vector3D pos_diff = last_ecef - first_ecef;
				// 合并，避免重复第一个点（如果时间和位置接近）
				if (pos_diff.squaredNorm() < Constants::GEOMETRY_EPSILON * Constants::GEOMETRY_EPSILON &&
					std::abs(waypoints_.back().time_stamp - segment.front().time_stamp) < Constants::TIME_EPSILON)
				{
					LOG_TRACE("  连接点与上一航段末尾接近，跳过第一个点合并。");
					// **合并载荷动作**
					if (!segment.front().payload_actions.empty()) {
						LOG_TRACE("  将重复起点的载荷动作合并到前一个点。");
						waypoints_.back().payload_actions.insert(waypoints_.back().payload_actions.end(),
							segment.front().payload_actions.begin(),
							segment.front().payload_actions.end());
					}
					// 插入从第二个点开始的段
					waypoints_.insert(waypoints_.end(), std::next(segment.begin()), segment.end());
				}
				else {
					LOG_TRACE("  直接追加整个航段。");
					// 直接插入整个段
					waypoints_.insert(waypoints_.end(), segment.begin(), segment.end());
				}
			}
			LOG_DEBUG("规划路径 [无人机:{}]: 添加航段完成。当前总点数: {}", uav_id_, waypoints_.size());
		}

		/** @brief 清空所有航路点。 */
		void PlannedRoute::clearWaypoints() {
			LOG_DEBUG("规划路径 [无人机:{}]: 清空 {} 个航路点。", uav_id_, waypoints_.size());
			waypoints_.clear();
		}

		/** @brief 检查航线是否为空。 */
		bool PlannedRoute::isEmpty() const {
			return waypoints_.empty();
		}

		/** @brief 计算航线总长度 (米)。 */
		double PlannedRoute::getTotalLength() const {
			double total_length = 0.0;
			if (waypoints_.size() < 2) {
				return 0.0; // 少于两个点，长度为 0
			}
			// 转换为ECEF坐标进行几何计算
			for (size_t i = 0; i < waypoints_.size() - 1; ++i) {
				EcefPoint ecef_current = NSUtils::CoordinateConverter::wgs84ToECEF(waypoints_[i].position);
				EcefPoint ecef_next = NSUtils::CoordinateConverter::wgs84ToECEF(waypoints_[i + 1].position);
				Vector3D diff = ecef_next - ecef_current;
				total_length += diff.norm();
			}
			LOG_TRACE("规划路径 [无人机:{}]: 计算总长度 = {:.2f} m", uav_id_, total_length);
			return total_length;
		}

		/** @brief 计算航线总时间 (秒)。 */
		Time PlannedRoute::getTotalTime() const {
			if (waypoints_.size() < 2) {
				return 0.0; // 少于两个点，时间为 0
			}
			// 检查时间戳有效性 (结束时间 >= 起始时间)
			if (waypoints_.back().time_stamp < waypoints_.front().time_stamp - Constants::TIME_EPSILON) {
				LOG_ERROR("规划路径 [无人机:{}]: 结束时间戳 ({:.4f}) 早于起始时间戳 ({:.4f})，无法计算总时间。",
					uav_id_, waypoints_.back().time_stamp, waypoints_.front().time_stamp);
				return 0.0; // 返回 0 或 NaN 表示无效
			}
			Time total_time = waypoints_.back().time_stamp - waypoints_.front().time_stamp;
			LOG_TRACE("规划路径 [无人机:{}]: 计算总时间 = {:.4f} s", uav_id_, total_time);
			return total_time;
		}

		/**
		 * @brief 获取起始点。
		 * @return 起始航路点的 const 引用。
		 * @throws DroneException 如果航线为空。
		 */
		const RoutePoint& PlannedRoute::getStartPoint() const {
			if (isEmpty()) {
				throw DroneException("尝试从无人机 [" + uav_id_ + "] 的空规划路径获取起始点。", ErrorCode::INVALID_STATE);
			}
			return waypoints_.front();
		}

		/**
		 * @brief 获取结束点。
		 * @return 结束航路点的 const 引用。
		 * @throws DroneException 如果航线为空。
		 */
		const RoutePoint& PlannedRoute::getEndPoint() const {
			if (isEmpty()) {
				throw DroneException("尝试从无人机 [" + uav_id_ + "] 的空规划路径获取结束点。", ErrorCode::INVALID_STATE);
			}
			return waypoints_.back();
		}


		// --- timeParameterizeConstantSpeed 实现 ---
		/**
		 * @brief 对几何路径进行简单的匀速时间参数化。
		 * @param path_geometry 输入的 3D 几何路径点。
		 * @param dynamics 无人机动力学模型 (可选)。
		 * @param start_time 起始时间戳。
		 * @param start_velocity 起始速度向量。
		 * @param desired_speed 期望的恒定速度。
		 * @param result 输出的时间参数化后的航段。
		 * @return 如果参数化成功返回 true。
		 */
		bool timeParameterizeConstantSpeed(
			const std::vector<WGS84Point>& path_geometry,
			const NSUav::IDynamicModel* dynamics, // 可选
			Time start_time,
			const Vector3D& start_velocity,
			double desired_speed,
			RouteSegment& result)
		{
			LOG_DEBUG("开始对 {} 个点的WGS84路径进行匀速时间参数化，期望速度 {:.2f} m/s...", path_geometry.size(), desired_speed);
			result.clear(); // 清空输出结果

			// 检查输入路径是否有效
			if (path_geometry.empty()) {
				LOG_WARN("无法时间参数化空路径。");
				return false;
			}
			if (path_geometry.size() == 1) {
				LOG_DEBUG("路径只有一个点，创建单点航段。");
				// 创建单点航段
				NSUav::UavState temp_start_state; // 用于获取姿态
				temp_start_state.velocity = start_velocity;
				RoutePoint rp; // 创建航点
				rp.position = path_geometry[0];
				rp.time_stamp = start_time;
				rp.velocity = start_velocity;
				// rp.payload_actions 默认为空
				if (start_velocity.norm() > Constants::VELOCITY_EPSILON) { // 如果有初始速度，设置初始姿态
					rp.orientation = Orientation::FromTwoVectors(Vector3D::UnitX(), start_velocity.normalized());
					rp.orientation.normalize();
				}
				else {
					rp.orientation = Orientation::Identity(); // 否则使用默认姿态
				}
				result.push_back(rp); // 添加起始点
				return true; // 单点路径参数化成功
			}

			// 检查期望速度是否有效
			double actual_speed = desired_speed; // 实际使用的速度
			if (actual_speed <= Constants::VELOCITY_EPSILON) {
				LOG_ERROR("时间参数化失败：期望速度无效 ({:.4f})。", actual_speed);
				return false;
			}

			// 检查速度是否超过动力学限制 (如果提供了动力学模型)
			if (dynamics) {
				// 获取一个示例状态来查询限制 (这里假设限制不随状态剧烈变化)
				NSUav::UavState dummy_state;
				dummy_state.position = path_geometry[0]; // 使用路径起点位置
				// 假设我们可以为状态设置一个典型的模式 (例如，基于速度)
				double min_op_speed = dynamics->getMinOperationalSpeed(dummy_state);
				dummy_state.mode = (actual_speed < min_op_speed + 1.0) ?
					NSUav::FlightMode::HOVER : NSUav::FlightMode::FIXED_WING; // 粗略猜测
				double max_h_speed = dynamics->getMaxHorizontalSpeed(dummy_state); // 获取最大水平速度

				// 如果动力学模型返回的最大速度无效，使用合理的默认值
				if (max_h_speed <= Constants::VELOCITY_EPSILON) {
					LOG_WARN("时间参数化：动力学模型返回的最大水平速度无效 ({:.2f} m/s)，使用默认值 15.0 m/s。", max_h_speed);
					max_h_speed = 15.0; // 使用合理的默认最大速度
				}

				if (actual_speed > max_h_speed + Constants::VELOCITY_EPSILON) {
					LOG_WARN("时间参数化：期望速度 ({:.2f} m/s) 超过最大水平速度 ({:.2f} m/s)。将速度限制为最大值。",
						actual_speed, max_h_speed);
					actual_speed = max_h_speed; // 限制速度
					if (actual_speed <= Constants::VELOCITY_EPSILON) { // 如果最大速度也无效
						LOG_ERROR("时间参数化失败：限制后的速度无效 ({:.4f})。", actual_speed);
						return false;
					}
				}
				LOG_DEBUG("  实际使用速度: {:.2f} m/s", actual_speed);
			}


			result.reserve(path_geometry.size()); // 预分配空间

			// 添加起始点
			RoutePoint current_rp;
			current_rp.position = path_geometry[0];    // 起始位置
			current_rp.time_stamp = start_time;       // 起始时间
			current_rp.velocity = start_velocity;     // 起始速度
			// payload_actions 默认为空

			// 设置起始姿态，优先使用非零的初始速度方向，否则使用路径的第一段方向
			Vector3D initial_dir = Vector3D::UnitX(); // 默认方向 (如果无法确定)
			if (start_velocity.norm() > Constants::VELOCITY_EPSILON) {
				initial_dir = start_velocity.normalized(); // 使用初始速度方向
				LOG_TRACE("  使用初始速度方向设置起始姿态。");
			}
			else {
				// 使用ECEF坐标计算第一段方向
				EcefPoint ecef_start = NSUtils::CoordinateConverter::wgs84ToECEF(path_geometry[0]);
				EcefPoint ecef_next = NSUtils::CoordinateConverter::wgs84ToECEF(path_geometry[1]);
				Vector3D first_segment = ecef_next.toVector3D() - ecef_start.toVector3D(); // 计算第一段向量
				if (first_segment.norm() > Constants::GEOMETRY_EPSILON) {
					initial_dir = first_segment.normalized(); // 使用第一段方向
					LOG_TRACE("  初始速度为零，使用第一段路径方向设置起始姿态。");
				}
				else {
					LOG_WARN("  无法确定起始姿态方向 (速度为零且第一段长度为零)，使用默认 X 轴方向。");
				}
			}
			// 使用安全的 FromTwoVectors 实现
			Vector3D ref_vec = Vector3D::UnitX();
			if ((ref_vec - initial_dir).norm() < Constants::ANGLE_EPSILON || (ref_vec + initial_dir).norm() < Constants::ANGLE_EPSILON) {
				ref_vec = Vector3D::UnitY();
			}
			current_rp.orientation = Orientation::FromTwoVectors(ref_vec, initial_dir); // 计算从参考轴到目标方向的旋转
			current_rp.orientation.normalize(); // 归一化四元数
			result.push_back(current_rp); // 添加第一个航点到结果
			LOG_TRACE("  添加起始点: t={:.4f}, Pos({})",	current_rp.time_stamp, current_rp.position.toString());

			double current_time = start_time; // 初始化当前时间
			// 遍历几何路径段（使用ECEF坐标进行精确几何运算）
			for (size_t i = 0; i < path_geometry.size() - 1; ++i) {
				const WGS84Point& wgs84_p1 = path_geometry[i]; // 当前段起点
				const WGS84Point& wgs84_p2 = path_geometry[i + 1]; // 当前段终点

				// 转换为ECEF坐标进行几何运算
				EcefPoint ecef_p1 = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_p1);
				EcefPoint ecef_p2 = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_p2);

				Vector3D segment_vec = ecef_p2.toVector3D() - ecef_p1.toVector3D(); // 计算段向量
				double segment_len = segment_vec.norm(); // 计算段长度

				// 如果段长度过小，跳过此段，避免除零或时间戳不增加
				if (segment_len < Constants::GEOMETRY_EPSILON) {
					LOG_TRACE("  跳过零长度段 [{} -> {}]", i, i + 1);
					continue; // 跳到下一段
				}

				Vector3D segment_dir = segment_vec.normalized(); // 计算段方向（单位向量）
				Time segment_time = segment_len / actual_speed; // 计算通过此段所需时间
				current_time += segment_time; // 更新当前时间戳

				RoutePoint next_rp; // 创建下一个航路点
				next_rp.position = wgs84_p2;              // 位置为段终点（WGS84坐标）
				next_rp.time_stamp = current_time;        // 时间戳为累加时间
				next_rp.velocity = segment_dir * actual_speed; // 速度为期望速度和段方向
				// 计算姿态
				ref_vec = Vector3D::UnitX();
				if ((ref_vec - segment_dir).norm() < Constants::ANGLE_EPSILON || (ref_vec + segment_dir).norm() < Constants::ANGLE_EPSILON) {
					ref_vec = Vector3D::UnitY();
				}
				next_rp.orientation = Orientation::FromTwoVectors(ref_vec, segment_dir); // 姿态朝向段方向
				next_rp.orientation.normalize(); // 归一化姿态
				// payload_actions 默认为空

				result.push_back(next_rp); // 添加到结果航段
				LOG_TRACE("  添加航点 {}: t={:.4f}, Pos({}, Δt={:.4f}",
					i + 1, next_rp.time_stamp, next_rp.position.toString(),
					segment_len, segment_time);
			}

			// 确保结果非空
			if (result.empty()) {
				LOG_ERROR("时间参数化未能生成任何航路点。");
				return false;
			}
			// 如果因为所有段都太短导致只有一个点，需要添加终点
			if (result.size() == 1 && path_geometry.size() > 1) {
				LOG_WARN("所有路径段长度均接近零，仅生成包含首尾点的航段。");
				RoutePoint end_rp = result.back(); // 复制起点信息
				end_rp.position = path_geometry.back(); // 更新为终点位置
				// 速度和姿态保持不变（因为没有移动）
				result.push_back(end_rp);
			}

			LOG_DEBUG("匀速时间参数化完成，生成 {} 个航点。", result.size());
			return true; // 参数化成功
		}

	} // namespace NSPlanning
} // namespace NSDrones