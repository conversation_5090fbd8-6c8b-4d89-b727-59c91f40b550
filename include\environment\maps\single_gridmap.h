#pragma once

#include "environment/maps/igridmap.h"
#include "core/types.h"
#include "params/parameters.h"
#include <gdal_priv.h>
#include <GeographicLib/LocalCartesian.hpp>
#include <string>
#include <memory>
#include <vector>
#include <utility>
#include <optional>
#include <array>
#include <mutex>
#include <unordered_map>  // 添加缺失的头文件
#include "Eigen/Core"     // 添加Eigen头文件

namespace NSDrones {
	namespace NSEnvironment {

		/**
		 * @class SingleGridMap
		 * @brief IGridMap 接口的具体实现，基于LocalCartesian坐标系和Eigen矩阵的高效地图数据结构。
		 *
		 * 这个类负责将外部通过 IGridMap 接口传入的 WGS84 地理坐标查询，
		 * 转换为LocalCartesian坐标系下的查询，然后通过相对于左上角基准点的偏移量，
		 * 直接访问Eigen矩阵中存储的地图数据。支持多图层数据存储。
		 * 提供了多种加载方法，包括从 GeoTIFF 文件和 PNG 文件加载数据。
		 */
		class SingleGridMap : public IGridMap {
		public:
			/**
			 * @brief 构造函数。
			 *
			 * 创建一个未初始化的 SingleGridMap 实例，只进行基本的成员变量初始化。
			 * 不加载任何地图数据，地图数据的加载应该通过调用 initialize() 方法来完成。
			 */
			SingleGridMap();

			/**
			 * @brief 虚析构函数。
			 * 确保正确的资源清理。
			 */
			~SingleGridMap() override;

			// --- 禁止拷贝构造和拷贝赋值，以防止资源管理问题 ---
			SingleGridMap(const SingleGridMap&) = delete;
			SingleGridMap& operator=(const SingleGridMap&) = delete;
			// --- 允许移动构造和移动赋值 ---
			SingleGridMap(SingleGridMap&&) = default; // 支持高效的资源转移
			SingleGridMap& operator=(SingleGridMap&&) = default;

			// --- 覆写 IGridMap 接口中的纯虚方法（igridmap.h） ---
			/**
			 * @brief 初始化单一地图
			 * @param global_params 全局参数对象，用于获取地图相关配置
			 * @return 如果成功初始化则返回 true，否则返回 false
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> global_params) override;

			/**
			 * @brief 直接从指定文件初始化单一地图（用于 TiledGridMap）
			 * @param file_path 要加载的地图文件路径
			 * @param load_mode 加载模式（"geotiff" 或 "tiff"）
			 * @return 如果成功初始化则返回 true，否则返回 false
			 */
			bool initializeFromFile(const std::string& file_path, const std::string& load_mode = "geotiff");
			std::optional<double> getElevation(double latitude, double longitude) const override;
			std::optional<FeatureType> getFeature(double latitude, double longitude) const override;
			std::optional<double> getFeatureHeight(double latitude, double longitude) const override;
			bool isInitialized() const override;
			bool isCovered(double latitude, double longitude) const override;

			// --- SingleGridMap 特有的公共方法 ---

			/**
			 * @brief 检查指定图层是否存在
			 * @param layer_name 图层名称
			 * @return 图层存在返回true，否则返回false
			 */
			bool hasLayer(const std::string& layer_name) const;

			/**
			 * @brief 获取地图的参考原点（左上角坐标）
			 * @return WGS84Point 地图的参考原点
			 */
			WGS84Point getReferenceOrigin() const;

			/**
			 * @brief 将WGS84坐标转换为局部坐标
			 * @param wgs84_point WGS84坐标点
			 * @return 局部坐标点（米）
			 */
			Vector3D wgs84ToLocal(const WGS84Point& wgs84_point) const;

			/**
			 * @brief 将局部坐标转换为WGS84坐标
			 * @param local_point 局部坐标点（米）
			 * @return WGS84坐标点
			 */
			WGS84Point localToWgs84(const Vector3D& local_point) const;

			/**
			 * @brief 从 GeoTIFF 文件加载地图数据（实例方法）
			 * @param file_path GeoTIFF 文件路径
			 * @return 加载成功返回 true
			 *
			 * @details 该方法会：
			 * 1. 从 GeoTIFF 文件中提取角点坐标作为参考原点
			 * 2. 创建坐标转换器
			 * 3. 构建 GridMap 并填充数据
			 * 4. 将角点信息存储到元数据中
			 */
			bool loadFromTif(const std::string& file_path);

			/**
			 * @brief 从 TIFF 文件加载地图数据（实例方法）
			 * @param tiff_file_path TIFF 文件路径
			 * @return 加载成功返回 true
			 */
			bool loadFromPng(const std::string& tiff_file_path);

			/**
			 * @brief 保存地图数据到PNG文件
			 * @param output_path 输出文件路径
			 * @return 保存成功返回 true
			 */
			bool saveToPng(const std::string& output_path);

			/**
			 * @brief 获取指定图层的数据矩阵（可选返回）
			 * @param layer_name 图层名称
			 * @return 图层数据矩阵，如果图层不存在则返回nullopt
			 */
			std::optional<Eigen::MatrixXf> getLayerMatrix(const std::string& layer_name) const;

			/**
			 * @brief 获取基于WGS84Point的高程查询
			 * @param wgs84_point WGS84坐标点
			 * @return 高程值，如果无效则返回nullopt
			 */
			std::optional<float> getElevation(const WGS84Point& wgs84_point) const;

			/**
			 * @brief 获取基于WGS84Point的地物类型查询
			 * @param wgs84_point WGS84坐标点
			 * @return 地物类型值，如果无效则返回nullopt
			 */
			std::optional<float> getFeature(const WGS84Point& wgs84_point) const;

			/**
			 * @brief 获取基于WGS84Point的地物高度查询
			 * @param wgs84_point WGS84坐标点
			 * @return 地物高度值，如果无效则返回nullopt
			 */
			std::optional<float> getFeatureHeight(const WGS84Point& wgs84_point) const;

			/**
			 * @brief 获取地图元数据
			 * @return 地图元数据
			 */
			MapMetadata getMetadata() const override;

			/**
			 * @brief 获取可用图层列表
			 * @return 图层名称列表
			 */
			std::vector<std::string> getAvailableLayers() const;

		private:
			// --- 私有成员变量 ---
			// 基于Eigen矩阵的地图数据存储
			std::unordered_map<std::string, Eigen::MatrixXf> data_layers_; ///< 地图数据图层（图层名 -> 数据矩阵）
			MapMetadata metadata_; ///< 地图元数据

			// 局部坐标系相关
			WGS84Point map_origin_; ///< 地图原点（左上角）WGS84坐标
			std::shared_ptr<GeographicLib::LocalCartesian> local_cartesian_; ///< 局部笛卡尔坐标系转换器
			std::pair<double, double> native_resolution_; ///< 原始分辨率（X方向，Y方向）米/像素
			std::pair<int, int> grid_size_; ///< 网格尺寸（宽度，高度）像素数

			mutable std::mutex coordinate_mutex_; ///< 坐标转换的线程安全锁
			bool initialized_ = false; ///< 标志适配器是否成功初始化

			// --- 已实现的私有辅助函数 ---

			/**
			 * @brief 智能检测和设置图层名称（配置优先 + 自动回退）
			 * @param global_params 全局参数对象
			 * @return 检测成功返回 true（至少找到高程图层）
			 */
			bool detectAndSetLayers(std::shared_ptr<NSParams::ParamValues> global_params);

			/**
			 * @brief 计算地图分辨率（米/像素）
			 * @param geoTransform 地理变换参数
			 * @return 分辨率对 (X方向, Y方向)（米/像素）
			 */
			std::pair<double, double> calculateResolution(const double* geoTransform);

			/**
			 * @brief 计算GeoTIFF四个角点的WGS84坐标
			 * @param dataset GDAL数据集指针
			 * @param geoTransform 地理变换参数
			 * @return 四个角点的WGS84坐标数组
			 */
			std::array<WGS84Point, 4> calculateCorners(GDALDataset* dataset, const double* geoTransform);

			/**
			 * @brief 将WGS84坐标转换为矩阵索引
			 * @param wgs84_point WGS84坐标点
			 * @param row 输出：矩阵行索引
			 * @param col 输出：矩阵列索引
			 * @return 转换成功且索引在有效范围内返回true
			 */
			bool wgs84ToMatrixIndex(const WGS84Point& wgs84_point, int& row, int& col) const;

			/**
			 * @brief 从 GeoTIFF 数据集加载数据到矩阵
			 * @param dataset GDAL 数据集指针
			 * @param geoTransform 地理变换参数
			 * @return 加载成功返回 true
			 */
			bool loadDataFromGeoTiff(GDALDataset* dataset, const double* geoTransform);

			/**
			 * @brief 填充元数据结构
			 * @param file_path 文件路径
			 * @param resolution 分辨率对 (X方向, Y方向)
			 * @param width 栅格宽度
			 * @param height 栅格高度
			 * @param corners 四个角点坐标
			 */
			void populateMetadataFromGeoTiff(const std::string& file_path, const std::pair<double, double>& resolution,
											 int width, int height, const std::array<WGS84Point, 4>& corners);

			// --- PNG格式的元数据和矩阵数据编码/解码方法 ---
			/**
			 * @brief 将元数据保存到PNG文件的元数据标签中
			 * @param dataset GDAL数据集指针
			 * @return 保存成功返回true
			 */
			bool saveMetadataToPng(GDALDataset* dataset);

			/**
			 * @brief 从PNG文件的元数据标签中加载元数据
			 * @param dataset GDAL数据集指针
			 * @return 加载成功返回true
			 */
			bool loadMetadataFromPng(GDALDataset* dataset);

			/**
			 * @brief 将矩阵数据编码到PNG的像素数据中
			 * @param dataset GDAL数据集指针
			 * @return 编码成功返回true
			 */
			bool saveMatrixDataToPng(GDALDataset* dataset);

			/**
			 * @brief 从PNG的像素数据中解码矩阵数据
			 * @param dataset GDAL数据集指针
			 * @return 解码成功返回true
			 */
			bool loadMatrixDataFromPng(GDALDataset* dataset);

			/**
			 * @brief 将浮点高程值编码为RGBA像素
			 * @param elevation 高程值（米）
			 * @param r 输出：红色分量
			 * @param g 输出：绿色分量
			 * @param b 输出：蓝色分量
			 * @param a 输出：透明度分量
			 */
			void encodeElevationToRGBA(float elevation, uint8_t& r, uint8_t& g, uint8_t& b, uint8_t& a);

			/**
			 * @brief 从RGBA像素解码浮点高程值
			 * @param r 红色分量
			 * @param g 绿色分量
			 * @param b 蓝色分量
			 * @param a 透明度分量
			 * @return 解码的高程值（米）
			 */
			float decodeElevationFromRGBA(uint8_t r, uint8_t g, uint8_t b, uint8_t a);
		};

	} // namespace NSEnvironment
} // namespace NSDrones
