﻿  Building Custom Rule E:/source/dronesplanning/CMakeLists.txt
  task_allocator.cpp
  energy_evaluator.cpp
  ipath_planner.cpp
  rrtstar_planner.cpp
  trajectory_optimizer.cpp
  config.cpp
  base_object.cpp
  entity_object.cpp
  entity_state.cpp
  ishape.cpp
  shape_factory.cpp
  box_shape.cpp
  capsule_shape.cpp
  compound_shape.cpp
  cone_shape.cpp
  convex_hull_shape.cpp
  cylinder_shape.cpp
  ellipsoid_shape.cpp
  line_shape.cpp
  mesh_shape.cpp
  正在生成代码...
  正在编译...
  plane_shape.cpp
  point_shape.cpp
  polygon_shape.cpp
  sphere_shape.cpp
  movement_strategy.cpp
  collision_engine.cpp
  coordinate_converter.cpp
  coordinate_manager.cpp
  task_space.cpp
  obstacle.cpp
  zone.cpp
  environment.cpp
E:\source\dronesplanning\src\environment\environment.cpp(1766,39): error C2664: “std::vector<std::shared_ptr<const NSDrones::NSEnvironment::Zone>,std::allocator<std::shared_ptr<const NSDrones::NSEnvironment::Zone>>> NSDrones::NSEnvironment::Environment::getViolatedZones(const NSDrones::NSCore::EcefPoint &) const”: 无法将参数 1 从“NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\environment\environment.cpp(1766,58): message : 原因如下: 无法从“NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\environment\environment.cpp(1766,58): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\src\environment\environment.cpp(1587,40): message : 参见“NSDrones::NSEnvironment::Environment::getViolatedZones”的声明
E:\source\dronesplanning\src\environment\environment.cpp(1766,39): message : 尝试匹配参数列表“(NSDrones::NSCore::WGS84Point)”时
E:\source\dronesplanning\src\environment\environment.cpp(1767): error C3536: “violated_zones”: 初始化之前无法使用
E:\source\dronesplanning\src\environment\environment.cpp(1923,19): error C2511: “bool NSDrones::NSEnvironment::Environment::checkCollisionWithDynamicObstacles(const NSDrones::NSCore::WGS84Point &,NSDrones::NSCore::Time,double,const std::unordered_set<std::string,std::hash<NSDrones::NSUtils::ObjectID>,std::equal_to<NSDrones::NSUtils::ObjectID>,std::allocator<std::string>> &) const”:“NSDrones::NSEnvironment::Environment”中没有找到重载的成员函数
E:\source\dronesplanning\include\environment/entities/obstacle.h(10,34): message : 参见“NSDrones::NSEnvironment::Environment”的声明
E:\source\dronesplanning\src\environment\environment.cpp(1930,46): error C2597: 对非静态成员“NSDrones::NSEnvironment::Environment::objects_mutex_”的非法引用
E:\source\dronesplanning\include\environment/environment.h(150,30): message : 参见“NSDrones::NSEnvironment::Environment::objects_mutex_”的声明
E:\source\dronesplanning\src\environment\environment.cpp(1933,5): error C2530: “obj_ptr”: 必须初始化引用
E:\source\dronesplanning\src\environment\environment.cpp(1933,5): error C3531: “obj_ptr”: 类型包含“auto”的符号必须具有初始值设定项
E:\source\dronesplanning\src\environment\environment.cpp(1933,30): error C2143: 语法错误: 缺少“;”(在“:”的前面)
E:\source\dronesplanning\src\environment\environment.cpp(1933,5): error C3536: “movable_objects”: 初始化之前无法使用
E:\source\dronesplanning\src\environment\environment.cpp(1933,47): error C2143: 语法错误: 缺少“;”(在“)”的前面)
E:\source\dronesplanning\src\environment\environment.cpp(1934,31): error C2672: “std::_Hash<std::_Uset_traits<_Kty,std::_Uhash_compare<_Kty,_Hasher,_Keyeq>,_Alloc,false>>::count”: 未找到匹配的重载函数
          with
          [
              _Kty=std::string,
              _Hasher=std::hash<NSDrones::NSUtils::ObjectID>,
              _Keyeq=std::equal_to<NSDrones::NSUtils::ObjectID>,
              _Alloc=std::allocator<std::string>
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xhash(1243,26): message : 可能是“unsigned __int64 std::_Hash<std::_Uset_traits<_Kty,std::_Uhash_compare<_Kty,_Hasher,_Keyeq>,_Alloc,false>>::count(const _Kty &) const”
          with
          [
              _Kty=std::string,
              _Hasher=std::hash<NSDrones::NSUtils::ObjectID>,
              _Keyeq=std::equal_to<NSDrones::NSUtils::ObjectID>,
              _Alloc=std::allocator<std::string>
          ]
E:\source\dronesplanning\src\environment\environment.cpp(1975,19): error C2511: “bool NSDrones::NSEnvironment::Environment::checkCollisionWithDynamicObstacles(const NSDrones::NSCore::WGS84Point &,const NSDrones::NSCore::WGS84Point &,NSDrones::NSCore::Time,NSDrones::NSCore::Time,double,const std::unordered_set<std::string,std::hash<NSDrones::NSUtils::ObjectID>,std::equal_to<NSDrones::NSUtils::ObjectID>,std::allocator<std::string>> &) const”:“NSDrones::NSEnvironment::Environment”中没有找到重载的成员函数
E:\source\dronesplanning\include\environment/entities/obstacle.h(10,34): message : 参见“NSDrones::NSEnvironment::Environment”的声明
E:\source\dronesplanning\src\environment\environment.cpp(1993,16): error C2665: “NSDrones::NSEnvironment::Environment::checkCollisionWithDynamicObstacles”: 没有重载函数可以转换所有参数类型
E:\source\dronesplanning\include\environment/environment.h(234,9): message : 可能是“bool NSDrones::NSEnvironment::Environment::checkCollisionWithDynamicObstacles(const NSDrones::NSCore::EcefPoint &,NSDrones::NSCore::Time,double,const std::unordered_set<std::string,std::hash<NSDrones::NSUtils::ObjectID>,std::equal_to<NSDrones::NSUtils::ObjectID>,std::allocator<std::string>> &) const”
E:\source\dronesplanning\src\environment\environment.cpp(1993,16): message : “bool NSDrones::NSEnvironment::Environment::checkCollisionWithDynamicObstacles(const NSDrones::NSCore::EcefPoint &,NSDrones::NSCore::Time,double,const std::unordered_set<std::string,std::hash<NSDrones::NSUtils::ObjectID>,std::equal_to<NSDrones::NSUtils::ObjectID>,std::allocator<std::string>> &) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\environment\environment.cpp(1993,51): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\environment\environment.cpp(1993,51): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\src\environment\environment.cpp(1993,16): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, NSDrones::NSCore::Time, double, const std::unordered_set<std::string,std::hash<NSDrones::NSUtils::ObjectID>,std::equal_to<NSDrones::NSUtils::ObjectID>,std::allocator<std::string>>)”时
E:\source\dronesplanning\src\environment\environment.cpp(2008,13): error C2665: “NSDrones::NSEnvironment::Environment::checkCollisionWithDynamicObstacles”: 没有重载函数可以转换所有参数类型
E:\source\dronesplanning\include\environment/environment.h(234,9): message : 可能是“bool NSDrones::NSEnvironment::Environment::checkCollisionWithDynamicObstacles(const NSDrones::NSCore::EcefPoint &,NSDrones::NSCore::Time,double,const std::unordered_set<std::string,std::hash<NSDrones::NSUtils::ObjectID>,std::equal_to<NSDrones::NSUtils::ObjectID>,std::allocator<std::string>> &) const”
E:\source\dronesplanning\src\environment\environment.cpp(2008,13): message : “bool NSDrones::NSEnvironment::Environment::checkCollisionWithDynamicObstacles(const NSDrones::NSCore::EcefPoint &,NSDrones::NSCore::Time,double,const std::unordered_set<std::string,std::hash<NSDrones::NSUtils::ObjectID>,std::equal_to<NSDrones::NSUtils::ObjectID>,std::allocator<std::string>> &) const”: 无法将参数 1 从“NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\environment\environment.cpp(2008,48): message : 原因如下: 无法从“NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\environment\environment.cpp(2008,48): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\src\environment\environment.cpp(2008,13): message : 尝试匹配参数列表“(NSDrones::NSCore::WGS84Point, NSDrones::NSCore::Time, double, const std::unordered_set<std::string,std::hash<NSDrones::NSUtils::ObjectID>,std::equal_to<NSDrones::NSUtils::ObjectID>,std::allocator<std::string>>)”时
E:\source\dronesplanning\src\environment\environment.cpp(2572,52): error C2039: "getBoundingBox": 不是 "NSDrones::NSCore::IShape" 的成员
E:\source\dronesplanning\include\core/geometry/ishape.h(56,9): message : 参见“NSDrones::NSCore::IShape”的声明
E:\source\dronesplanning\src\environment\environment.cpp(2575,27): error C3536: “bounds”: 初始化之前无法使用
E:\source\dronesplanning\src\environment\environment.cpp(2613,53): error C2039: "getBoundingBox": 不是 "NSDrones::NSCore::IShape" 的成员
E:\source\dronesplanning\include\core/geometry/ishape.h(56,9): message : 参见“NSDrones::NSCore::IShape”的声明
E:\source\dronesplanning\src\environment\environment.cpp(2614,67): error C3536: “temp_bounds”: 初始化之前无法使用
E:\source\dronesplanning\src\environment\environment.cpp(2653,41): error C2664: “std::vector<NSDrones::NSEnvironment::CollisionResult,std::allocator<NSDrones::NSEnvironment::CollisionResult>> NSDrones::NSEnvironment::CollisionEngine<NSDrones::NSEnvironment::ObjectMap>::checkGeometryAgainstEnvironment(const NSDrones::NSCore::IShape &,const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::Orientation &,const NSDrones::NSEnvironment::CollisionOptions &)”: 无法将参数 1 从“std::shared_ptr<NSDrones::NSCore::SphereShape>”转换为“const NSDrones::NSCore::IShape &”
E:\source\dronesplanning\src\environment\environment.cpp(2654,13): message : 原因如下: 无法从“std::shared_ptr<NSDrones::NSCore::SphereShape>”转换为“const NSDrones::NSCore::IShape”
E:\source\dronesplanning\src\environment\environment.cpp(2654,13): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/collision/collision_engine.h(88,33): message : 参见“NSDrones::NSEnvironment::CollisionEngine<NSDrones::NSEnvironment::ObjectMap>::checkGeometryAgainstEnvironment”的声明
E:\source\dronesplanning\src\environment\environment.cpp(2653,41): message : 尝试匹配参数列表“(std::shared_ptr<NSDrones::NSCore::SphereShape>, NSDrones::NSCore::EcefPoint, Eigen::Quaternion<double,0>, NSDrones::NSEnvironment::CollisionOptions)”时
E:\source\dronesplanning\src\environment\environment.cpp(2657,23): error C3536: “results”: 初始化之前无法使用
E:\source\dronesplanning\src\environment\environment.cpp(2693,36): error C2511: “std::optional<double> NSDrones::NSEnvironment::Environment::getAdjustedHeightAtPoint(const NSDrones::NSCore::WGS84Point &,NSDrones::NSCore::AltitudeType,double) const”:“NSDrones::NSEnvironment::Environment”中没有找到重载的成员函数
E:\source\dronesplanning\include\environment/entities/obstacle.h(10,34): message : 参见“NSDrones::NSEnvironment::Environment”的声明
E:\source\dronesplanning\src\environment\environment.cpp(2710,43): error C2352: “NSDrones::NSEnvironment::Environment::getGroundAltitude”: 调用非静态成员函数需要一个对象
E:\source\dronesplanning\src\environment\environment.cpp(2281,38): message : 参见“NSDrones::NSEnvironment::Environment::getGroundAltitude”的声明
E:\source\dronesplanning\src\environment\environment.cpp(2710,43): error C2119: "<structured binding>": 无法从空的初始值设定项推导 "auto" 的类型
E:\source\dronesplanning\src\environment\environment.cpp(2710,43): error C3617: 结构化绑定的初始值设定项必须是数组或非联合类类型；不允许 "int" 类型
E:\source\dronesplanning\src\environment\environment.cpp(2715,22): error C2597: 对非静态成员“NSDrones::NSEnvironment::Environment::mapDataSource_”的非法引用
E:\source\dronesplanning\include\environment/environment.h(141,30): message : 参见“NSDrones::NSEnvironment::Environment::mapDataSource_”的声明
E:\source\dronesplanning\src\environment\environment.cpp(2723): error C3536: “metadata”: 初始化之前无法使用
E:\source\dronesplanning\src\environment\environment.cpp(2735,13): error C2672: “fmt::v11::format”: 未找到匹配的重载函数
e:\source\third_party\spdlog\include\spdlog\fmt\bundled\format.h(4198,31): message : 可能是“std::string fmt::v11::format(fstring<T...>::t,T &&...)”
E:\source\dronesplanning\src\environment\environment.cpp(2735,13): message : 未能使函数模板“std::string fmt::v11::format(fstring<T...>::t,T &&...)”专用化
E:\source\dronesplanning\src\environment\environment.cpp(2735,13): message : 用下列模板参数:
E:\source\dronesplanning\src\environment\environment.cpp(2735,13): message : “T={std::string, double &, unknown-type &, double &}”
e:\source\third_party\spdlog\include\spdlog\fmt\bundled\format.h(4153,17): message : 或    “std::string fmt::v11::format(const Locale &,fstring<T...>::t,T &&...)”
E:\source\dronesplanning\src\environment\environment.cpp(2736,1): message : “std::string fmt::v11::format(const Locale &,fstring<T...>::t,T &&...)”: 无法推导“__formal”的 模板 参数
  geometry_manager.cpp
  bvh_spatial_index.cpp
  igridmap.cpp
  single_gridmap.cpp
  tiled_gridmap.cpp
  main.cpp
  mission.cpp
  task.cpp
  正在生成代码...
  正在编译...
  param_defs.cpp
  param_json.cpp
  parameters.cpp
  paramregistry.cpp
  itask_planner.cpp
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): error C2065: “Environment”: 未声明的标识符
E:\source\dronesplanning\include\planning/itask_planner.h(89,9): error C2923: "std::shared_ptr": "Environment" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): message : 参见“Environment”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(89,55): error C2955: “std::shared_ptr”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C2143: 语法错误: 缺少“,”(在“&”的前面)
E:\source\dronesplanning\src\planning\itask_planner.cpp(70,68): error C2556: “std::shared_ptr<NSDrones::NSEnvironment::Environment> NSDrones::NSPlanning::ITaskPlanner::getEnvironment(void) const”: 重载函数与“std::shared_ptr NSDrones::NSPlanning::ITaskPlanner::getEnvironment(void) const”只是在返回类型上不同
E:\source\dronesplanning\include\planning/itask_planner.h(89,33): message : 参见“NSDrones::NSPlanning::ITaskPlanner::getEnvironment”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(70,45): error C2371: “NSDrones::NSPlanning::ITaskPlanner::getEnvironment”: 重定义；不同的基类型
E:\source\dronesplanning\include\planning/itask_planner.h(89,33): message : 参见“NSDrones::NSPlanning::ITaskPlanner::getEnvironment”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(70,45): error C2079: “NSDrones::NSPlanning::ITaskPlanner::getEnvironment”使用未定义的 class“std::shared_ptr”
E:\source\dronesplanning\src\planning\itask_planner.cpp(85,22): error C2065: “getEnvironment”: 未声明的标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(85,38): error C3867: “NSDrones::NSPlanning::ITaskPlanner::getEnvironment”: 非标准语法；请使用 "&" 来创建指向成员的指针
E:\source\dronesplanning\src\planning\itask_planner.cpp(112,64): error C2065: “requested_point”: 未声明的标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(112,98): error C2065: “requested_point”: 未声明的标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(113): error C3536: “adjusted_height”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(114,28): error C2100: 非法的间接寻址
E:\source\dronesplanning\src\planning\itask_planner.cpp(114,23): error C2106: “=”: 左操作数必须为左值
E:\source\dronesplanning\src\planning\itask_planner.cpp(122,29): error C2065: “requested_point”: 未声明的标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(125,29): error C2065: “requested_point”: 未声明的标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(161,27): error C2065: “end_ecef”: 未声明的标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(161,38): error C2065: “start_ecef”: 未声明的标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(204,22): error C2065: “getEnvironment”: 未声明的标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(204,38): error C3867: “NSDrones::NSPlanning::ITaskPlanner::getEnvironment”: 非标准语法；请使用 "&" 来创建指向成员的指针
E:\source\dronesplanning\src\planning\itask_planner.cpp(211,21): error C2530: “zones”: 必须初始化引用
E:\source\dronesplanning\src\planning\itask_planner.cpp(212): error C3536: “zones”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(292,21): error C2664: “bool NSDrones::NSEnvironment::Zone::intersects(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,double) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\itask_planner.cpp(292,34): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\itask_planner.cpp(292,34): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/entities/zone.h(91,17): message : 参见“NSDrones::NSEnvironment::Zone::intersects”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(292,21): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, const NSDrones::NSCore::WGS84Point)”时
E:\source\dronesplanning\src\planning\itask_planner.cpp(305,21): error C2664: “bool NSDrones::NSEnvironment::Zone::intersects(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,double) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\itask_planner.cpp(305,34): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\itask_planner.cpp(305,34): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/entities/zone.h(91,17): message : 参见“NSDrones::NSEnvironment::Zone::intersects”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(305,21): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, const NSDrones::NSCore::WGS84Point)”时
E:\source\dronesplanning\src\planning\itask_planner.cpp(398,32): error C2065: “getEnvironment”: 未声明的标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(398,48): error C3867: “NSDrones::NSPlanning::ITaskPlanner::getEnvironment”: 非标准语法；请使用 "&" 来创建指向成员的指针
E:\source\dronesplanning\src\planning\itask_planner.cpp(445,21): error C2511: “bool NSDrones::NSPlanning::ITaskPlanner::isZoneConstraintSatisfied(const NSDrones::NSPlanning::RouteSegment &,const NSDrones::NSEnvironment::ConstZonePtr &) const”:“NSDrones::NSPlanning::ITaskPlanner”中没有找到重载的成员函数
E:\source\dronesplanning\include\planning/itask_planner.h(32,9): message : 参见“NSDrones::NSPlanning::ITaskPlanner”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(469,13): error C2664: “bool NSDrones::NSEnvironment::Zone::isInside(const NSDrones::NSCore::EcefPoint &,double) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\itask_planner.cpp(469,24): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\itask_planner.cpp(469,24): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/entities/zone.h(75,17): message : 参见“NSDrones::NSEnvironment::Zone::isInside”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(469,13): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, double)”时
E:\source\dronesplanning\src\planning\itask_planner.cpp(470,6): error C2039: "x": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(470,6): error C2039: "y": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(470,6): error C2039: "z": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(476,14): error C2664: “bool NSDrones::NSEnvironment::Zone::intersects(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,double) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\itask_planner.cpp(476,27): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\itask_planner.cpp(476,27): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/entities/zone.h(91,17): message : 参见“NSDrones::NSEnvironment::Zone::intersects”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(476,14): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, const NSDrones::NSCore::WGS84Point, double)”时
E:\source\dronesplanning\src\planning\itask_planner.cpp(492,14): error C2664: “bool NSDrones::NSEnvironment::Zone::isInside(const NSDrones::NSCore::EcefPoint &,double) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\itask_planner.cpp(492,25): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\itask_planner.cpp(492,25): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/entities/zone.h(75,17): message : 参见“NSDrones::NSEnvironment::Zone::isInside”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(492,14): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, double)”时
E:\source\dronesplanning\src\planning\itask_planner.cpp(493,6): error C2039: "x": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(493,6): error C2039: "y": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(493,6): error C2039: "z": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(511,22): error C2065: “getEnvironment”: 未声明的标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(511,38): error C3867: “NSDrones::NSPlanning::ITaskPlanner::getEnvironment”: 非标准语法；请使用 "&" 来创建指向成员的指针
E:\source\dronesplanning\src\planning\itask_planner.cpp(520,25): error C2530: “all_zones”: 必须初始化引用
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,42): error C3536: “all_zones”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): error C2672: “begin”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(609,23): message : 可能是“const _Ty *std::begin(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : “const _Ty *std::begin(const std::valarray<_Ty> &)”: 无法从“int”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(604,17): message : 或    “_Ty *std::begin(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : “_Ty *std::begin(std::valarray<_Ty> &)”: 无法从“int”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1589,27): message : 或    “_Ty *std::begin(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : “_Ty *std::begin(_Ty (&)[_Size]) noexcept”: 无法从“int”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1571,30): message : 或    “unknown-type std::begin(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : 未能使函数模板“unknown-type std::begin(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1565,30): message : 或    “unknown-type std::begin(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : 未能使函数模板“unknown-type std::begin(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(55,35): message : 或    “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”: 无法从“int”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): error C2672: “end”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(619,23): message : 可能是“const _Ty *std::end(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : “const _Ty *std::end(const std::valarray<_Ty> &)”: 无法从“int”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(614,17): message : 或    “_Ty *std::end(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : “_Ty *std::end(std::valarray<_Ty> &)”: 无法从“int”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1594,27): message : 或    “_Ty *std::end(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : “_Ty *std::end(_Ty (&)[_Size]) noexcept”: 无法从“int”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1583,30): message : 或    “unknown-type std::end(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : 未能使函数模板“unknown-type std::end(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1577,30): message : 或    “unknown-type std::end(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : 未能使函数模板“unknown-type std::end(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(60,35): message : 或    “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,31): message : “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”: 无法从“int”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,42): error C3536: “<begin>$L0”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,42): error C3536: “<end>$L0”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(521,40): error C2100: 非法的间接寻址
E:\source\dronesplanning\src\planning\itask_planner.cpp(548,3): error C2039: "x": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(548,3): error C2039: "y": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(548,3): error C2039: "z": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(559,22): error C2065: “getEnvironment”: 未声明的标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(559,38): error C3867: “NSDrones::NSPlanning::ITaskPlanner::getEnvironment”: 非标准语法；请使用 "&" 来创建指向成员的指针
E:\source\dronesplanning\src\planning\itask_planner.cpp(563,123): error C2065: “p1”: 未声明的标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(604,25): error C2530: “all_zones”: 必须初始化引用
E:\source\dronesplanning\src\planning\itask_planner.cpp(605): error C3536: “all_zones”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): error C2672: “begin”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(609,23): message : 可能是“const _Ty *std::begin(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : “const _Ty *std::begin(const std::valarray<_Ty> &)”: 无法从“int”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(604,17): message : 或    “_Ty *std::begin(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : “_Ty *std::begin(std::valarray<_Ty> &)”: 无法从“int”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1589,27): message : 或    “_Ty *std::begin(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : “_Ty *std::begin(_Ty (&)[_Size]) noexcept”: 无法从“int”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1571,30): message : 或    “unknown-type std::begin(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : 未能使函数模板“unknown-type std::begin(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1565,30): message : 或    “unknown-type std::begin(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : 未能使函数模板“unknown-type std::begin(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(55,35): message : 或    “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : “const _Elem *std::begin(std::initializer_list<_Elem>) noexcept”: 无法从“int”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): error C2672: “end”: 未找到匹配的重载函数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(619,23): message : 可能是“const _Ty *std::end(const std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : “const _Ty *std::end(const std::valarray<_Ty> &)”: 无法从“int”推导出“const std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\valarray(614,17): message : 或    “_Ty *std::end(std::valarray<_Ty> &)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : “_Ty *std::end(std::valarray<_Ty> &)”: 无法从“int”推导出“std::valarray<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1594,27): message : 或    “_Ty *std::end(_Ty (&)[_Size]) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : “_Ty *std::end(_Ty (&)[_Size]) noexcept”: 无法从“int”推导出“_Ty (&)[_Size]”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1583,30): message : 或    “unknown-type std::end(const _Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : 未能使函数模板“unknown-type std::end(const _Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(1577,30): message : 或    “unknown-type std::end(_Container &) noexcept(<expr>)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : 未能使函数模板“unknown-type std::end(_Container &) noexcept(<expr>)”专用化
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : 用下列模板参数:
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : “_Container=int”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\initializer_list(60,35): message : 或    “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,32): message : “const _Elem *std::end(std::initializer_list<_Elem>) noexcept”: 无法从“int”推导出“std::initializer_list<_Elem>”的 模板 参数
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,43): error C3536: “<begin>$L0”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,43): error C3536: “<end>$L0”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\itask_planner.cpp(609,41): error C2100: 非法的间接寻址
E:\source\dronesplanning\src\planning\itask_planner.cpp(614,7): error C2672: “NSDrones::NSUtils::enumToString”: 未找到匹配的重载函数
E:\source\dronesplanning\include\utils/enum_utils.h(21,22): message : 可能是“std::string NSDrones::NSUtils::enumToString(EnumType)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(619,48): error C2672: “NSDrones::NSUtils::enumToString”: 未找到匹配的重载函数
E:\source\dronesplanning\include\utils/enum_utils.h(21,22): message : 可能是“std::string NSDrones::NSUtils::enumToString(EnumType)”
E:\source\dronesplanning\src\planning\itask_planner.cpp(685,7): error C2039: "orientation": 不是 "NSDrones::NSPlanning::RoutePoint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(32,10): message : 参见“NSDrones::NSPlanning::RoutePoint”的声明
  mission_planner.cpp
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): error C2065: “Environment”: 未声明的标识符
E:\source\dronesplanning\include\planning/itask_planner.h(89,9): error C2923: "std::shared_ptr": "Environment" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): message : 参见“Environment”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(89,55): error C2955: “std::shared_ptr”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C2143: 语法错误: 缺少“,”(在“&”的前面)
E:\source\dronesplanning\src\planning\mission_planner.cpp(180,6): error C2039: "x": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\mission_planner.cpp(180,6): error C2039: "y": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\mission_planner.cpp(180,6): error C2039: "z": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\mission_planner.cpp(257,7): error C2039: "x": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\mission_planner.cpp(257,7): error C2039: "y": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\mission_planner.cpp(257,7): error C2039: "z": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\mission_planner.cpp(295,10): error C2039: "x": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\mission_planner.cpp(295,10): error C2039: "y": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\mission_planner.cpp(295,10): error C2039: "z": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\mission_planner.cpp(796,6): error C2059: 语法错误:“else”
E:\source\dronesplanning\src\planning\mission_planner.cpp(796,11): error C2143: 语法错误: 缺少“;”(在“{”的前面)
E:\source\dronesplanning\src\planning\mission_planner.cpp(796,11): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
E:\source\dronesplanning\src\planning\mission_planner.cpp(803,57): error C2065: “task”: 未声明的标识符
E:\source\dronesplanning\src\planning\mission_planner.cpp(803,31): error C3861: “getTaskPayloadRequirement”: 找不到标识符
E:\source\dronesplanning\src\planning\mission_planner.cpp(804,4): error C2059: 语法错误:“if”
E:\source\dronesplanning\src\planning\mission_planner.cpp(804,41): error C2143: 语法错误: 缺少“;”(在“{”的前面)
E:\source\dronesplanning\src\planning\mission_planner.cpp(804,41): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
E:\source\dronesplanning\src\planning\mission_planner.cpp(808,6): error C2059: 语法错误:“else”
E:\source\dronesplanning\src\planning\mission_planner.cpp(808,11): error C2143: 语法错误: 缺少“;”(在“{”的前面)
E:\source\dronesplanning\src\planning\mission_planner.cpp(808,11): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
E:\source\dronesplanning\src\planning\mission_planner.cpp(815,4): error C2059: 语法错误:“switch”
E:\source\dronesplanning\src\planning\mission_planner.cpp(815,28): error C2143: 语法错误: 缺少“;”(在“{”的前面)
E:\source\dronesplanning\src\planning\mission_planner.cpp(815,28): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
E:\source\dronesplanning\src\planning\mission_planner.cpp(825,4): error C3928: "->": 带圆括号的声明符后不允许尾随返回类型
E:\source\dronesplanning\src\planning\mission_planner.cpp(825,4): error C3613: “->”后缺少返回类型(假定为“int”)
E:\source\dronesplanning\src\planning\mission_planner.cpp(825,4): error C3646: “log”: 未知重写说明符
E:\source\dronesplanning\src\planning\mission_planner.cpp(826,1): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\src\planning\mission_planner.cpp(826,1): error C2556: “int NSDrones::Logger::get_logger(void)”: 重载函数与“std::shared_ptr<spdlog::logger> &NSDrones::Logger::get_logger(void)”只是在返回类型上不同
E:\source\dronesplanning\include\utils/logging.h(43,43): message : 参见“NSDrones::Logger::get_logger”的声明
E:\source\dronesplanning\src\planning\mission_planner.cpp(825,4): error C2040: “NSDrones::Logger::get_logger”:“int (void)”与“std::shared_ptr<spdlog::logger> &(void)”的间接寻址级别不同
E:\source\dronesplanning\src\planning\mission_planner.cpp(826,1): error C2888: “std::shared_ptr<spdlog::logger> &NSDrones::Logger::get_logger(void)”: 不能在命名空间“NSPlanning”内定义符号
E:\source\dronesplanning\src\planning\mission_planner.cpp(825,4): error C2059: 语法错误:“(”
E:\source\dronesplanning\src\planning\mission_planner.cpp(825,4): error C2143: 语法错误: 缺少“)”(在“{”的前面)
E:\source\dronesplanning\src\planning\mission_planner.cpp(826,1): error C2888: “spdlog::source_loc”: 不能在命名空间“NSPlanning”内定义符号
E:\source\dronesplanning\src\planning\mission_planner.cpp(826,1): error C2457: “__FUNCTION__”: 预定义的宏不能出现在函数体的外部
E:\source\dronesplanning\src\planning\mission_planner.cpp(825,4): error C2440: “初始化”: 无法从“initializer list”转换为“int”
E:\source\dronesplanning\src\planning\mission_planner.cpp(825,4): message : 初始值设定项包含过多元素
E:\source\dronesplanning\src\planning\mission_planner.cpp(826,1): error C2365: “spdlog::level::trace”: 重定义；以前的定义是“枚举数”
e:\source\third_party\spdlog\include\spdlog/common.h(246,5): message : 参见“spdlog::level::trace”的声明
E:\source\dronesplanning\src\planning\mission_planner.cpp(826,1): error C2244: “spdlog::level::trace”: 无法将函数定义与现有的声明匹配
E:\source\dronesplanning\src\planning\mission_planner.cpp(825,4): message : 参见“spdlog::level::trace”的声明
E:\source\dronesplanning\src\planning\mission_planner.cpp(825,4): error C2059: 语法错误:“字符串”
E:\source\dronesplanning\src\planning\mission_planner.cpp(826,1): error C2059: 语法错误:“)”
E:\source\dronesplanning\src\planning\mission_planner.cpp(828,4): error C2059: 语法错误:“return”
E:\source\dronesplanning\src\planning\mission_planner.cpp(832,1): error C2059: 语法错误:“}”
E:\source\dronesplanning\src\planning\mission_planner.cpp(832,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
  planning_result.cpp
  planning_types.cpp
E:\source\dronesplanning\src\planning\planning_types.cpp(57,30): error C2653: “CoordinateConverter”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\planning_types.cpp(57,51): error C3861: “wgs84ToECEF”: 找不到标识符
E:\source\dronesplanning\src\planning\planning_types.cpp(58,27): error C2653: “CoordinateConverter”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\planning_types.cpp(58,48): error C3861: “wgs84ToECEF”: 找不到标识符
E:\source\dronesplanning\src\planning\planning_types.cpp(98,27): error C2653: “CoordinateConverter”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\planning_types.cpp(98,48): error C3861: “wgs84ToECEF”: 找不到标识符
E:\source\dronesplanning\src\planning\planning_types.cpp(99,28): error C2653: “CoordinateConverter”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\planning_types.cpp(99,49): error C3861: “wgs84ToECEF”: 找不到标识符
E:\source\dronesplanning\src\planning\planning_types.cpp(144,30): error C2653: “CoordinateConverter”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\planning_types.cpp(144,51): error C3861: “wgs84ToECEF”: 找不到标识符
E:\source\dronesplanning\src\planning\planning_types.cpp(145,27): error C2653: “CoordinateConverter”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\planning_types.cpp(145,48): error C3861: “wgs84ToECEF”: 找不到标识符
E:\source\dronesplanning\src\planning\planning_types.cpp(296,28): error C2653: “CoordinateConverter”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\planning_types.cpp(296,49): error C3861: “wgs84ToECEF”: 找不到标识符
E:\source\dronesplanning\src\planning\planning_types.cpp(297,27): error C2653: “CoordinateConverter”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\planning_types.cpp(297,48): error C3861: “wgs84ToECEF”: 找不到标识符
E:\source\dronesplanning\src\planning\planning_types.cpp(315,4): error C2039: "x": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\planning_types.cpp(315,4): error C2039: "y": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\planning_types.cpp(315,4): error C2039: "z": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\planning_types.cpp(325,25): error C2653: “CoordinateConverter”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\planning_types.cpp(325,46): error C3861: “wgs84ToECEF”: 找不到标识符
E:\source\dronesplanning\src\planning\planning_types.cpp(326,25): error C2653: “CoordinateConverter”: 不是类或命名空间名称
E:\source\dronesplanning\src\planning\planning_types.cpp(326,46): error C3861: “wgs84ToECEF”: 找不到标识符
E:\source\dronesplanning\src\planning\planning_types.cpp(355,5): error C2039: "x": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\planning_types.cpp(355,5): error C2039: "y": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\planning_types.cpp(355,5): error C2039: "z": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
  followpath_taskplanner.cpp
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): error C2065: “Environment”: 未声明的标识符
E:\source\dronesplanning\include\planning/itask_planner.h(89,9): error C2923: "std::shared_ptr": "Environment" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): message : 参见“Environment”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(89,55): error C2955: “std::shared_ptr”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C2143: 语法错误: 缺少“,”(在“&”的前面)
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2641: 无法推导“std::shared_ptr”的模板参数
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::weak_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::weak_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1764,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ty,_Dx>)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ty,_Dx>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1767,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ux,_Dx> &&)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ux,_Dx> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1562,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty0> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1538,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1533,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty0> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1528,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1523,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1518,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1512,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1507,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1501,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1495,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1487,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,39): error C2783: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t) noexcept”: 无法推导“_Ty”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1467,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(void) noexcept”: 应输入 0 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1465,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(38,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(39,9): error C2678: 二进制“!”: 没有找到接受“std::shared_ptr”类型的左操作数的运算符(或没有可接受的转换)
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(39,9): message : 可以是“内置 C++ operator!(bool)”
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(39,9): message : 尝试匹配参数列表“(std::shared_ptr)”时
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(39,8): error C2088: “!”: 对于 class 非法
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2641: 无法推导“std::shared_ptr”的模板参数
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::weak_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::weak_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1764,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ty,_Dx>)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ty,_Dx>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1767,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ux,_Dx> &&)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ux,_Dx> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1562,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty0> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1538,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1533,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty0> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1528,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1523,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1518,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1512,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1507,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1501,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1495,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1487,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,39): error C2783: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t) noexcept”: 无法推导“_Ty”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1467,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(void) noexcept”: 应输入 0 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1465,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(106,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(107,9): error C2678: 二进制“!”: 没有找到接受“std::shared_ptr”类型的左操作数的运算符(或没有可接受的转换)
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(107,9): message : 可以是“内置 C++ operator!(bool)”
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(107,9): message : 尝试匹配参数列表“(std::shared_ptr)”时
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(107,8): error C2088: “!”: 对于 class 非法
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(146,5): error C2039: "x": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(146,5): error C2039: "y": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(146,5): error C2039: "z": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(202,38): error C2664: “bool NSDrones::NSAlgorithm::IPathPlanner::findPath(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,const NSDrones::NSUav::IDynamicModel *,const NSDrones::NSMission::PathConstraintStrategy *,std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> &)”: 无法将参数 1 从“NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(202,62): message : 原因如下: 无法从“NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(202,62): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\algorithm/path_planner/ipath_planner.h(55,17): message : 参见“NSDrones::NSAlgorithm::IPathPlanner::findPath”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(202,38): message : 尝试匹配参数列表“(NSDrones::NSCore::WGS84Point, NSDrones::NSCore::WGS84Point, const NSDrones::NSUav::IDynamicModel *, const NSDrones::NSMission::PathConstraintStrategy *, std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>>)”时
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(276,8): error C2039: "x": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(276,8): error C2039: "y": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(276,8): error C2039: "z": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
  loiterpoint_taskplanner.cpp
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): error C2065: “Environment”: 未声明的标识符
E:\source\dronesplanning\include\planning/itask_planner.h(89,9): error C2923: "std::shared_ptr": "Environment" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): message : 参见“Environment”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(89,55): error C2955: “std::shared_ptr”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C2143: 语法错误: 缺少“,”(在“&”的前面)
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2641: 无法推导“std::shared_ptr”的模板参数
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::weak_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::weak_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1764,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ty,_Dx>)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ty,_Dx>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1767,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ux,_Dx> &&)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ux,_Dx> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1562,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty0> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1538,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1533,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty0> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1528,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1523,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1518,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1512,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1507,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1501,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1495,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1487,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,39): error C2783: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t) noexcept”: 无法推导“_Ty”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1467,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(void) noexcept”: 应输入 0 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1465,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(37,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(38,9): error C2678: 二进制“!”: 没有找到接受“std::shared_ptr”类型的左操作数的运算符(或没有可接受的转换)
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(38,9): message : 可以是“内置 C++ operator!(bool)”
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(38,9): message : 尝试匹配参数列表“(std::shared_ptr)”时
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(38,8): error C2088: “!”: 对于 class 非法
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2641: 无法推导“std::shared_ptr”的模板参数
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::weak_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::weak_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1764,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ty,_Dx>)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ty,_Dx>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1767,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ux,_Dx> &&)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ux,_Dx> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1562,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty0> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1538,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1533,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty0> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1528,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1523,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1518,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1512,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1507,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1501,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1495,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1487,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,39): error C2783: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t) noexcept”: 无法推导“_Ty”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1467,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(void) noexcept”: 应输入 0 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1465,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(116,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(117,9): error C2678: 二进制“!”: 没有找到接受“std::shared_ptr”类型的左操作数的运算符(或没有可接受的转换)
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(117,9): message : 可以是“内置 C++ operator!(bool)”
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(117,9): message : 尝试匹配参数列表“(std::shared_ptr)”时
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(117,8): error C2088: “!”: 对于 class 非法
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(152,5): error C2039: "x": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(152,5): error C2039: "y": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(152,5): error C2039: "z": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(201,37): error C2664: “bool NSDrones::NSAlgorithm::IPathPlanner::findPath(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,const NSDrones::NSUav::IDynamicModel *,const NSDrones::NSMission::PathConstraintStrategy *,std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> &)”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(201,59): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(201,59): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\algorithm/path_planner/ipath_planner.h(55,17): message : 参见“NSDrones::NSAlgorithm::IPathPlanner::findPath”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(201,37): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, NSDrones::NSCore::WGS84Point, const NSDrones::NSUav::IDynamicModel *, const NSDrones::NSMission::PathConstraintStrategy *, std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>>)”时
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(214,74): error C2065: “loiter_center”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(237,44): error C2440: “初始化”: 无法从“std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>>”转换为“std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>>”
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(237,44): message : 无构造函数可以接受源类型，或构造函数重载决策不明确
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,25): error C2039: "Geodesic": 不是 "GeographicLib" 的成员
E:/source/third_party/install/geographiclib/include\GeographicLib/LocalCartesian.hpp(16,11): message : 参见“GeographicLib”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,33): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,33): error C2143: 语法错误: 缺少“;”(在“&”的前面)
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,35): error C2065: “geod”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,57): error C2039: "Geodesic": 不是 "GeographicLib" 的成员
E:/source/third_party/install/geographiclib/include\GeographicLib/LocalCartesian.hpp(16,11): message : 参见“GeographicLib”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,57): error C3083: “Geodesic”:“::”左侧的符号必须是一种类型
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,67): error C2039: "WGS84": 不是 "GeographicLib" 的成员
E:/source/third_party/install/geographiclib/include\GeographicLib/LocalCartesian.hpp(16,11): message : 参见“GeographicLib”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,72): error C3861: “WGS84”: 找不到标识符
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(358,5): error C2065: “geod”: 未声明的标识符
  scanarea_taskplanner.cpp
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): error C2065: “Environment”: 未声明的标识符
E:\source\dronesplanning\include\planning/itask_planner.h(89,9): error C2923: "std::shared_ptr": "Environment" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): message : 参见“Environment”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(89,55): error C2955: “std::shared_ptr”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C2143: 语法错误: 缺少“,”(在“&”的前面)
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2641: 无法推导“std::shared_ptr”的模板参数
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::weak_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::weak_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1764,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ty,_Dx>)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ty,_Dx>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1767,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ux,_Dx> &&)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ux,_Dx> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1562,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty0> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1538,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1533,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty0> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1528,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1523,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1518,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1512,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1507,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1501,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1495,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1487,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,39): error C2783: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t) noexcept”: 无法推导“_Ty”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1467,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(void) noexcept”: 应输入 0 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1465,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(124,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(125,9): error C2678: 二进制“!”: 没有找到接受“std::shared_ptr”类型的左操作数的运算符(或没有可接受的转换)
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(125,9): message : 可以是“内置 C++ operator!(bool)”
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(125,9): message : 尝试匹配参数列表“(std::shared_ptr)”时
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(125,8): error C2088: “!”: 对于 class 非法
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(135,28): error C2675: 一元“->”:“std::shared_ptr”不定义该运算符或到预定义运算符可接收类型的转换
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1661,22): message : 可能是“_Ty2 *std::shared_ptr<_Ty>::operator ->(void) noexcept const”
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(135,39): message : “_Ty2 *std::shared_ptr<_Ty>::operator ->(void) noexcept const”: 无法推导“__formal”的 模板 参数
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(135,41): error C2039: "getGeometryManager": 不是 "std::shared_ptr" 的成员
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(136): error C3536: “geometry_manager”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(215,5): error C2039: "x": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(215,5): error C2039: "y": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(215,5): error C2039: "z": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(220,33): error C2065: “scan_path_geom”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(228,37): error C2664: “bool NSDrones::NSAlgorithm::IPathPlanner::findPath(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,const NSDrones::NSUav::IDynamicModel *,const NSDrones::NSMission::PathConstraintStrategy *,std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> &)”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(228,59): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(228,59): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\algorithm/path_planner/ipath_planner.h(55,17): message : 参见“NSDrones::NSAlgorithm::IPathPlanner::findPath”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(228,37): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, NSDrones::NSCore::WGS84Point, const NSDrones::NSUav::IDynamicModel *, const NSDrones::NSMission::PathConstraintStrategy *, std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>>)”时
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(246,61): error C2065: “scan_path_geom”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(250,49): error C2065: “scan_path_geom”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(250,73): error C2065: “scan_path_geom”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(250,19): error C2661: “std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>>::insert”: 没有重载函数接受 1 个参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(1289,27): message : 可能是“std::_Vector_iterator<std::_Vector_val<std::_Simple_types<_Ty>>> std::vector<_Ty,std::allocator<_Ty>>::insert(std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<_Ty>>>,_Iter,_Iter)”
          with
          [
              _Ty=NSDrones::NSCore::EcefPoint
          ]
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(250,19): message : “std::_Vector_iterator<std::_Vector_val<std::_Simple_types<_Ty>>> std::vector<_Ty,std::allocator<_Ty>>::insert(std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<_Ty>>>,_Iter,_Iter)”: 应输入 3 个参数，却提供了 1 个
          with
          [
              _Ty=NSDrones::NSCore::EcefPoint
          ]
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(250,19): message : 尝试匹配参数列表“(std::_Vector_iterator<std::_Vector_val<std::_Simple_types<_Ty>>>)”时
          with
          [
              _Ty=NSDrones::NSCore::EcefPoint
          ]
  surveycylinder_taskplanner.cpp
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): error C2065: “Environment”: 未声明的标识符
E:\source\dronesplanning\include\planning/itask_planner.h(89,9): error C2923: "std::shared_ptr": "Environment" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): message : 参见“Environment”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(89,55): error C2955: “std::shared_ptr”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C2143: 语法错误: 缺少“,”(在“&”的前面)
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2641: 无法推导“std::shared_ptr”的模板参数
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::weak_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::weak_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1764,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ty,_Dx>)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ty,_Dx>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1767,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ux,_Dx> &&)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ux,_Dx> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1562,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty0> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1538,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1533,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty0> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1528,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1523,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1518,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1512,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1507,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1501,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1495,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1487,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,39): error C2783: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t) noexcept”: 无法推导“_Ty”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1467,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(void) noexcept”: 应输入 0 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1465,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(95,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(96,9): error C2678: 二进制“!”: 没有找到接受“std::shared_ptr”类型的左操作数的运算符(或没有可接受的转换)
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(96,9): message : 可以是“内置 C++ operator!(bool)”
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(96,9): message : 尝试匹配参数列表“(std::shared_ptr)”时
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(96,8): error C2088: “!”: 对于 class 非法
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(105,47): error C2440: “初始化”: 无法从“std::pair<NSDrones::NSCore::EcefPoint,bool>”转换为“std::pair<NSDrones::NSCore::WGS84Point,bool>”
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(105,47): message : 无构造函数可以接受源类型，或构造函数重载决策不明确
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(153,4): error C2039: "x": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(153,4): error C2039: "y": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(153,4): error C2039: "z": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(158,28): error C2675: 一元“->”:“std::shared_ptr”不定义该运算符或到预定义运算符可接收类型的转换
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1661,22): message : 可能是“_Ty2 *std::shared_ptr<_Ty>::operator ->(void) noexcept const”
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(158,39): message : “_Ty2 *std::shared_ptr<_Ty>::operator ->(void) noexcept const”: 无法推导“__formal”的 模板 参数
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(158,41): error C2039: "getGeometryManager": 不是 "std::shared_ptr" 的成员
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(159,25): error C3536: “geometry_manager”: 初始化之前无法使用
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(221,5): error C2039: "x": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(221,5): error C2039: "y": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(221,5): error C2039: "z": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(233,37): error C2664: “bool NSDrones::NSAlgorithm::IPathPlanner::findPath(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,const NSDrones::NSUav::IDynamicModel *,const NSDrones::NSMission::PathConstraintStrategy *,std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> &)”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(233,59): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(233,59): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\algorithm/path_planner/ipath_planner.h(55,17): message : 参见“NSDrones::NSAlgorithm::IPathPlanner::findPath”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(233,37): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, NSDrones::NSCore::EcefPoint, const NSDrones::NSUav::IDynamicModel *, const NSDrones::NSMission::PathConstraintStrategy *, std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>>)”时
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(253,56): error C2679: 二元“-”: 没有找到接受“_Ty”类型的右操作数的运算符(或没有可接受的转换)
          with
          [
              _Ty=NSDrones::NSCore::EcefPoint
          ]
E:\source\dronesplanning\include\core/types.h(524,13): message : 可能是“NSDrones::NSCore::Vector3D NSDrones::NSCore::WGS84Point::operator -(const NSDrones::NSCore::WGS84Point &) const”
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(253,56): message : 尝试匹配参数列表“(_Ty, _Ty)”时
          with
          [
              _Ty=NSDrones::NSCore::WGS84Point
          ]
          and
          [
              _Ty=NSDrones::NSCore::EcefPoint
          ]
  surveymultipoints_taskplanner.cpp
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): error C2065: “Environment”: 未声明的标识符
E:\source\dronesplanning\include\planning/itask_planner.h(89,9): error C2923: "std::shared_ptr": "Environment" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): message : 参见“Environment”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(89,55): error C2955: “std::shared_ptr”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C2143: 语法错误: 缺少“,”(在“&”的前面)
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2641: 无法推导“std::shared_ptr”的模板参数
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::weak_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::weak_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1764,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ty,_Dx>)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ty,_Dx>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1767,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ux,_Dx> &&)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ux,_Dx> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1562,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty0> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1538,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1533,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty0> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1528,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1523,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1518,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1512,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1507,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1501,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1495,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1487,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,39): error C2783: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t) noexcept”: 无法推导“_Ty”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1467,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(void) noexcept”: 应输入 0 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1465,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(69,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(70,9): error C2678: 二进制“!”: 没有找到接受“std::shared_ptr”类型的左操作数的运算符(或没有可接受的转换)
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(70,9): message : 可以是“内置 C++ operator!(bool)”
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(70,9): message : 尝试匹配参数列表“(std::shared_ptr)”时
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(70,8): error C2088: “!”: 对于 class 非法
  surveysphere_taskplanner.cpp
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): error C2065: “Environment”: 未声明的标识符
E:\source\dronesplanning\include\planning/itask_planner.h(89,9): error C2923: "std::shared_ptr": "Environment" 不是参数 "_Ty" 的有效 模板 类型参数
E:\source\dronesplanning\include\planning/itask_planner.h(89,20): message : 参见“Environment”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(89,55): error C2955: “std::shared_ptr”: 使用 类 模板 需要 模板 参数列表
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\planning/itask_planner.h(171,90): error C2143: 语法错误: 缺少“,”(在“&”的前面)
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2641: 无法推导“std::shared_ptr”的模板参数
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::weak_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::weak_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1764,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ty,_Dx>)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ty,_Dx>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1767,1): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::unique_ptr<_Ux,_Dx> &&)”: 无法从“std::shared_ptr”推导出“std::unique_ptr<_Ux,_Dx> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1562,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty0> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1538,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty> &&) noexcept”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty> &&”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1533,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty0> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1528,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty> &) noexcept”: 无法从“std::shared_ptr”推导出“const std::shared_ptr<_Ty> &”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1523,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty0> &&,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1518,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(const std::shared_ptr<_Ty0> &,remove_extent<_Ty>::type *) noexcept”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1512,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1507,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1501,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx,_Alloc)”: 应输入 3 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1495,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(_Ux *,_Dx)”: 应输入 2 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1487,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,39): error C2783: “std::shared_ptr<_Ty> std::shared_ptr(std::nullptr_t) noexcept”: 无法推导“_Ty”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1467,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2780: “std::shared_ptr<_Ty> std::shared_ptr(void) noexcept”: 应输入 0 个参数，却提供了 1 个
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1465,5): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(87,21): error C2784: “std::shared_ptr<_Ty> std::shared_ptr(std::shared_ptr<_Ty>)”: 无法从“std::shared_ptr”推导出“std::shared_ptr<_Ty>”的 模板 参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1454,18): message : 参见“std::shared_ptr”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(88,9): error C2678: 二进制“!”: 没有找到接受“std::shared_ptr”类型的左操作数的运算符(或没有可接受的转换)
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(88,9): message : 可以是“内置 C++ operator!(bool)”
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(88,9): message : 尝试匹配参数列表“(std::shared_ptr)”时
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(88,8): error C2088: “!”: 对于 class 非法
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(176,37): error C2664: “bool NSDrones::NSAlgorithm::IPathPlanner::findPath(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,const NSDrones::NSUav::IDynamicModel *,const NSDrones::NSMission::PathConstraintStrategy *,std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> &)”: 无法将参数 1 从“NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(176,61): message : 原因如下: 无法从“NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(176,61): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\algorithm/path_planner/ipath_planner.h(55,17): message : 参见“NSDrones::NSAlgorithm::IPathPlanner::findPath”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(176,37): message : 尝试匹配参数列表“(NSDrones::NSCore::WGS84Point, NSDrones::NSCore::WGS84Point, const NSDrones::NSUav::IDynamicModel *, const NSDrones::NSMission::PathConstraintStrategy *, std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>>)”时
  fixedwing_dynamics.cpp
  multirotor_dynamics.cpp
  vtol_dynamics.cpp
  fixedwing_energies.cpp
E:\source\dronesplanning\include\environment/collision/collision_types.h(5,10): fatal  error C1083: 无法打开包括文件: “environment/coordinate/coordinate_converter.h”: No such file or directory
  multirotor_energies.cpp
E:\source\dronesplanning\include\environment/collision/collision_types.h(5,10): fatal  error C1083: 无法打开包括文件: “environment/coordinate/coordinate_converter.h”: No such file or directory
  vtol_energies.cpp
E:\source\dronesplanning\include\environment/collision/collision_types.h(5,10): fatal  error C1083: 无法打开包括文件: “environment/coordinate/coordinate_converter.h”: No such file or directory
  正在生成代码...
  正在编译...
  flight_strategy.cpp
E:\source\dronesplanning\include\environment/collision/collision_types.h(5,10): fatal  error C1083: 无法打开包括文件: “environment/coordinate/coordinate_converter.h”: No such file or directory
  idynamic_model.cpp
E:\source\dronesplanning\include\environment/collision/collision_types.h(5,10): fatal  error C1083: 无法打开包括文件: “environment/coordinate/coordinate_converter.h”: No such file or directory
  ienergy_model.cpp
E:\source\dronesplanning\include\environment/collision/collision_types.h(5,10): fatal  error C1083: 无法打开包括文件: “environment/coordinate/coordinate_converter.h”: No such file or directory
  uav.cpp
E:\source\dronesplanning\include\environment/collision/collision_types.h(5,10): fatal  error C1083: 无法打开包括文件: “environment/coordinate/coordinate_converter.h”: No such file or directory
  uav_config.cpp
E:\source\dronesplanning\include\environment/collision/collision_types.h(5,10): fatal  error C1083: 无法打开包括文件: “environment/coordinate/coordinate_converter.h”: No such file or directory
  enum_utils.cpp
  file_utils.cpp
  logging.cpp
  object_id.cpp
  orientation_utils.cpp
  正在生成代码...
