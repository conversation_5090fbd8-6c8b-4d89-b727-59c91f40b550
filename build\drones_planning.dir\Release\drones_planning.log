﻿  itask_planner.cpp
E:\source\dronesplanning\src\planning\itask_planner.cpp(161,27): error C2065: “end_ecef”: 未声明的标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(161,38): error C2065: “start_ecef”: 未声明的标识符
E:\source\dronesplanning\src\planning\itask_planner.cpp(226,55): error C2664: “std::vector<std::shared_ptr<const NSDrones::NSEnvironment::Zone>,std::allocator<std::shared_ptr<const NSDrones::NSEnvironment::Zone>>> NSDrones::NSEnvironment::Environment::getViolatedZones(const NSDrones::NSCore::EcefPoint &) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\itask_planner.cpp(226,74): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\itask_planner.cpp(226,74): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/environment.h(1120,30): message : 参见“NSDrones::NSEnvironment::Environment::getViolatedZones”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(226,55): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point)”时
E:\source\dronesplanning\src\planning\itask_planner.cpp(227,55): error C2664: “std::vector<std::shared_ptr<const NSDrones::NSEnvironment::Zone>,std::allocator<std::shared_ptr<const NSDrones::NSEnvironment::Zone>>> NSDrones::NSEnvironment::Environment::getViolatedZones(const NSDrones::NSCore::EcefPoint &) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\itask_planner.cpp(227,74): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\itask_planner.cpp(227,74): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/environment.h(1120,30): message : 参见“NSDrones::NSEnvironment::Environment::getViolatedZones”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(227,55): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point)”时
E:\source\dronesplanning\src\planning\itask_planner.cpp(228,62): error C2665: “NSDrones::NSEnvironment::Environment::getIntersectingZones”: 没有重载函数可以转换所有参数类型
E:\source\dronesplanning\include\environment/environment.h(1113,30): message : 可能是“std::vector<std::shared_ptr<const NSDrones::NSEnvironment::Zone>,std::allocator<std::shared_ptr<const NSDrones::NSEnvironment::Zone>>> NSDrones::NSEnvironment::Environment::getIntersectingZones(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &) const”
E:\source\dronesplanning\src\planning\itask_planner.cpp(228,62): message : “std::vector<std::shared_ptr<const NSDrones::NSEnvironment::Zone>,std::allocator<std::shared_ptr<const NSDrones::NSEnvironment::Zone>>> NSDrones::NSEnvironment::Environment::getIntersectingZones(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\itask_planner.cpp(228,85): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\itask_planner.cpp(228,85): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\src\planning\itask_planner.cpp(228,62): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, const NSDrones::NSCore::WGS84Point)”时
E:\source\dronesplanning\src\planning\itask_planner.cpp(292,21): error C2664: “bool NSDrones::NSEnvironment::Zone::intersects(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,double) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\itask_planner.cpp(292,34): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\itask_planner.cpp(292,34): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/entities/zone.h(91,17): message : 参见“NSDrones::NSEnvironment::Zone::intersects”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(292,21): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, const NSDrones::NSCore::WGS84Point)”时
E:\source\dronesplanning\src\planning\itask_planner.cpp(305,21): error C2664: “bool NSDrones::NSEnvironment::Zone::intersects(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,double) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\itask_planner.cpp(305,34): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\itask_planner.cpp(305,34): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/entities/zone.h(91,17): message : 参见“NSDrones::NSEnvironment::Zone::intersects”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(305,21): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, const NSDrones::NSCore::WGS84Point)”时
E:\source\dronesplanning\src\planning\itask_planner.cpp(469,13): error C2664: “bool NSDrones::NSEnvironment::Zone::isInside(const NSDrones::NSCore::EcefPoint &,double) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\itask_planner.cpp(469,24): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\itask_planner.cpp(469,24): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/entities/zone.h(75,17): message : 参见“NSDrones::NSEnvironment::Zone::isInside”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(469,13): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, double)”时
E:\source\dronesplanning\src\planning\itask_planner.cpp(476,14): error C2664: “bool NSDrones::NSEnvironment::Zone::intersects(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,double) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\itask_planner.cpp(476,27): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\itask_planner.cpp(476,27): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/entities/zone.h(91,17): message : 参见“NSDrones::NSEnvironment::Zone::intersects”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(476,14): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, const NSDrones::NSCore::WGS84Point, double)”时
E:\source\dronesplanning\src\planning\itask_planner.cpp(492,14): error C2664: “bool NSDrones::NSEnvironment::Zone::isInside(const NSDrones::NSCore::EcefPoint &,double) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\itask_planner.cpp(492,25): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\itask_planner.cpp(492,25): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/entities/zone.h(75,17): message : 参见“NSDrones::NSEnvironment::Zone::isInside”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(492,14): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, double)”时
E:\source\dronesplanning\src\planning\itask_planner.cpp(539,18): error C2664: “bool NSDrones::NSEnvironment::Zone::isInside(const NSDrones::NSCore::EcefPoint &,double) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\itask_planner.cpp(539,34): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\itask_planner.cpp(539,34): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/entities/zone.h(75,17): message : 参见“NSDrones::NSEnvironment::Zone::isInside”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(539,18): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, double)”时
E:\source\dronesplanning\src\planning\itask_planner.cpp(614,18): error C2664: “bool NSDrones::NSEnvironment::Zone::intersects(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,double) const”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\itask_planner.cpp(614,31): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\itask_planner.cpp(614,31): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/entities/zone.h(91,17): message : 参见“NSDrones::NSEnvironment::Zone::intersects”的声明
E:\source\dronesplanning\src\planning\itask_planner.cpp(614,18): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, const NSDrones::NSCore::WGS84Point)”时
E:\source\dronesplanning\src\planning\itask_planner.cpp(686,7): error C2039: "orientation": 不是 "NSDrones::NSPlanning::RoutePoint" 的成员
E:\source\dronesplanning\include\planning/planning_types.h(32,10): message : 参见“NSDrones::NSPlanning::RoutePoint”的声明
  mission_planner.cpp
  planning_types.cpp
  followpath_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(202,38): error C2664: “bool NSDrones::NSAlgorithm::IPathPlanner::findPath(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,const NSDrones::NSUav::IDynamicModel *,const NSDrones::NSMission::PathConstraintStrategy *,std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> &)”: 无法将参数 1 从“NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(202,62): message : 原因如下: 无法从“NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(202,62): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\algorithm/path_planner/ipath_planner.h(55,17): message : 参见“NSDrones::NSAlgorithm::IPathPlanner::findPath”的声明
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(202,38): message : 尝试匹配参数列表“(NSDrones::NSCore::WGS84Point, NSDrones::NSCore::WGS84Point, const NSDrones::NSUav::IDynamicModel *, const NSDrones::NSMission::PathConstraintStrategy *, std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>>)”时
  loiterpoint_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(201,37): error C2664: “bool NSDrones::NSAlgorithm::IPathPlanner::findPath(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,const NSDrones::NSUav::IDynamicModel *,const NSDrones::NSMission::PathConstraintStrategy *,std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> &)”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(201,59): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(201,59): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\algorithm/path_planner/ipath_planner.h(55,17): message : 参见“NSDrones::NSAlgorithm::IPathPlanner::findPath”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(201,37): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, NSDrones::NSCore::WGS84Point, const NSDrones::NSUav::IDynamicModel *, const NSDrones::NSMission::PathConstraintStrategy *, std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>>)”时
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(214,74): error C2065: “loiter_center”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(237,44): error C2440: “初始化”: 无法从“std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>>”转换为“std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>>”
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(237,44): message : 无构造函数可以接受源类型，或构造函数重载决策不明确
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,25): error C2039: "Geodesic": 不是 "GeographicLib" 的成员
E:/source/third_party/install/geographiclib/include\GeographicLib/LocalCartesian.hpp(16,11): message : 参见“GeographicLib”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,33): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,33): error C2143: 语法错误: 缺少“;”(在“&”的前面)
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,35): error C2065: “geod”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,57): error C2039: "Geodesic": 不是 "GeographicLib" 的成员
E:/source/third_party/install/geographiclib/include\GeographicLib/LocalCartesian.hpp(16,11): message : 参见“GeographicLib”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,57): error C3083: “Geodesic”:“::”左侧的符号必须是一种类型
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,67): error C2039: "WGS84": 不是 "GeographicLib" 的成员
E:/source/third_party/install/geographiclib/include\GeographicLib/LocalCartesian.hpp(16,11): message : 参见“GeographicLib”的声明
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(347,72): error C3861: “WGS84”: 找不到标识符
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(358,5): error C2065: “geod”: 未声明的标识符
  scanarea_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(220,33): error C2065: “scan_path_geom”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(228,37): error C2664: “bool NSDrones::NSAlgorithm::IPathPlanner::findPath(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,const NSDrones::NSUav::IDynamicModel *,const NSDrones::NSMission::PathConstraintStrategy *,std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> &)”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(228,59): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(228,59): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\algorithm/path_planner/ipath_planner.h(55,17): message : 参见“NSDrones::NSAlgorithm::IPathPlanner::findPath”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(228,37): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, NSDrones::NSCore::WGS84Point, const NSDrones::NSUav::IDynamicModel *, const NSDrones::NSMission::PathConstraintStrategy *, std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>>)”时
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(246,61): error C2065: “scan_path_geom”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(250,49): error C2065: “scan_path_geom”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(250,73): error C2065: “scan_path_geom”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(250,19): error C2661: “std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>>::insert”: 没有重载函数接受 1 个参数
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\vector(1289,27): message : 可能是“std::_Vector_iterator<std::_Vector_val<std::_Simple_types<_Ty>>> std::vector<_Ty,std::allocator<_Ty>>::insert(std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<_Ty>>>,_Iter,_Iter)”
          with
          [
              _Ty=NSDrones::NSCore::EcefPoint
          ]
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(250,19): message : “std::_Vector_iterator<std::_Vector_val<std::_Simple_types<_Ty>>> std::vector<_Ty,std::allocator<_Ty>>::insert(std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<_Ty>>>,_Iter,_Iter)”: 应输入 3 个参数，却提供了 1 个
          with
          [
              _Ty=NSDrones::NSCore::EcefPoint
          ]
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(250,19): message : 尝试匹配参数列表“(std::_Vector_iterator<std::_Vector_val<std::_Simple_types<_Ty>>>)”时
          with
          [
              _Ty=NSDrones::NSCore::EcefPoint
          ]
  surveycylinder_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(105,47): error C2440: “初始化”: 无法从“std::pair<NSDrones::NSCore::EcefPoint,bool>”转换为“std::pair<NSDrones::NSCore::WGS84Point,bool>”
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(105,47): message : 无构造函数可以接受源类型，或构造函数重载决策不明确
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(221,5): error C2039: "x": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(221,5): error C2039: "y": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(221,5): error C2039: "z": 不是 "NSDrones::NSCore::WGS84Point" 的成员
E:\source\dronesplanning\include\core/types.h(470,10): message : 参见“NSDrones::NSCore::WGS84Point”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(233,37): error C2664: “bool NSDrones::NSAlgorithm::IPathPlanner::findPath(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,const NSDrones::NSUav::IDynamicModel *,const NSDrones::NSMission::PathConstraintStrategy *,std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> &)”: 无法将参数 1 从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(233,59): message : 原因如下: 无法从“const NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(233,59): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\algorithm/path_planner/ipath_planner.h(55,17): message : 参见“NSDrones::NSAlgorithm::IPathPlanner::findPath”的声明
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(233,37): message : 尝试匹配参数列表“(const NSDrones::NSCore::WGS84Point, NSDrones::NSCore::EcefPoint, const NSDrones::NSUav::IDynamicModel *, const NSDrones::NSMission::PathConstraintStrategy *, std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>>)”时
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(253,56): error C2679: 二元“-”: 没有找到接受“_Ty”类型的右操作数的运算符(或没有可接受的转换)
          with
          [
              _Ty=NSDrones::NSCore::EcefPoint
          ]
E:\source\dronesplanning\include\core/types.h(524,13): message : 可能是“NSDrones::NSCore::Vector3D NSDrones::NSCore::WGS84Point::operator -(const NSDrones::NSCore::WGS84Point &) const”
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(253,56): message : 尝试匹配参数列表“(_Ty, _Ty)”时
          with
          [
              _Ty=NSDrones::NSCore::WGS84Point
          ]
          and
          [
              _Ty=NSDrones::NSCore::EcefPoint
          ]
  surveysphere_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(176,37): error C2664: “bool NSDrones::NSAlgorithm::IPathPlanner::findPath(const NSDrones::NSCore::EcefPoint &,const NSDrones::NSCore::EcefPoint &,const NSDrones::NSUav::IDynamicModel *,const NSDrones::NSMission::PathConstraintStrategy *,std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> &)”: 无法将参数 1 从“NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint &”
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(176,61): message : 原因如下: 无法从“NSDrones::NSCore::WGS84Point”转换为“const NSDrones::NSCore::EcefPoint”
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(176,61): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\algorithm/path_planner/ipath_planner.h(55,17): message : 参见“NSDrones::NSAlgorithm::IPathPlanner::findPath”的声明
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(176,37): message : 尝试匹配参数列表“(NSDrones::NSCore::WGS84Point, NSDrones::NSCore::WGS84Point, const NSDrones::NSUav::IDynamicModel *, const NSDrones::NSMission::PathConstraintStrategy *, std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>>)”时
  uav.cpp
E:\source\dronesplanning\src\uav\uav.cpp(136,21): error C2679: 二元“=”: 没有找到接受“_Ty”类型的右操作数的运算符(或没有可接受的转换)
          with
          [
              _Ty=NSDrones::NSCore::NedPoint
          ]
E:\source\dronesplanning\include\core/types.h(549,3): message : 可能是“NSDrones::NSCore::WGS84Point &NSDrones::NSCore::WGS84Point::operator =(NSDrones::NSCore::WGS84Point &&)”
E:\source\dronesplanning\include\core/types.h(549,3): message : 或    “NSDrones::NSCore::WGS84Point &NSDrones::NSCore::WGS84Point::operator =(const NSDrones::NSCore::WGS84Point &)”
E:\source\dronesplanning\src\uav\uav.cpp(136,21): message : 尝试匹配参数列表“(NSDrones::NSCore::WGS84Point, _Ty)”时
          with
          [
              _Ty=NSDrones::NSCore::NedPoint
          ]
  正在生成代码...
