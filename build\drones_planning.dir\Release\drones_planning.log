﻿  surveycylinder_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(253,44): error C2440: “初始化”: 无法从“std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>>”转换为“std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>>”
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(253,44): message : 无构造函数可以接受源类型，或构造函数重载决策不明确
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(255,56): error C2679: 二元“-”: 没有找到接受“_Ty”类型的右操作数的运算符(或没有可接受的转换)
          with
          [
              _Ty=NSDrones::NSCore::EcefPoint
          ]
E:\source\dronesplanning\include\core/types.h(524,13): message : 可能是“NSDrones::NSCore::Vector3D NSDrones::NSCore::WGS84Point::operator -(const NSDrones::NSCore::WGS84Point &) const”
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(255,56): message : 尝试匹配参数列表“(_Ty, _Ty)”时
          with
          [
              _Ty=NSDrones::NSCore::WGS84Point
          ]
          and
          [
              _Ty=NSDrones::NSCore::EcefPoint
          ]
