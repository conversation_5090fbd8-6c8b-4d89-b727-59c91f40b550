// src/params/param_json.cpp

#include "params/param_json.h"
#include "params/paramregistry.h"
#include "utils/logging.h"
#include <fstream>
#include <variant>
#include <map>
#include <sstream>
#include <type_traits>

namespace NSDrones {
    namespace NSParams {

        // --- 错误处理实现 ---
        const char* ParamJsonCategory::name() const noexcept { return "ParamJsonError"; }
        std::string ParamJsonCategory::message(int ev) const {
            switch (static_cast<ParamJsonError>(ev)) {
            case ParamJsonError::Success: return "成功";
            case ParamJsonError::FileReadError: return "文件读取错误";
            case ParamJsonError::FileWriteError: return "文件写入错误";
            case ParamJsonError::ParseError: return "JSON解析错误";
            case ParamJsonError::ValidationError: return "验证失败";
            case ParamJsonError::InvalidType: return "无效类型";
            case ParamJsonError::InvalidValueFormat: return "无效值格式";
            case ParamJsonError::MissingRequiredField: return "缺少必填字段";
            case ParamJsonError::KeyNotFound: return "未找到键";
            case ParamJsonError::ValueConversionFailed: return "值转换失败";
            case ParamJsonError::TypeConversionFailed: return "类型转换失败";
            case ParamJsonError::OutputFormattingError: return "输出格式错误";
            case ParamJsonError::UnknownError: return "未知错误";
            default: return "未定义错误";
            }
        }

        std::error_code make_error_code(ParamJsonError e) {
            return {static_cast<int>(e), ParamJsonCategory::get()};
        }

        // --- 类型转换辅助函数 ---
        std::string typeIndexToString(const std::type_index& type) {
            if (type == typeid(int)) return "int";
            else if (type == typeid(double)) return "double";
            else if (type == typeid(bool)) return "bool";
            else if (type == typeid(std::string)) return "string";
            else if (type == typeid(Eigen::Vector2d)) return "vector2d";
            else if (type == typeid(Eigen::Vector3d)) return "vector3d";
            else if (type == typeid(Eigen::Quaterniond)) return "quaterniond";
            else if (type == typeid(WGS84Point)) return "wgs84point";
            else if (type == typeid(std::vector<int>)) return "int[]";
            else if (type == typeid(std::vector<double>)) return "double[]";
            else if (type == typeid(std::vector<bool>)) return "bool[]";
            else if (type == typeid(std::vector<std::string>)) return "string[]";
            else if (type == typeid(std::vector<std::vector<double>>)) return "double[][]";
            else if (type == typeid(std::vector<std::vector<std::string>>)) return "string[][]";
            else return "unknown";
        }

        std::optional<std::type_index> stringToTypeIndex(const std::string& type_str) {
            if (type_str == "int") return typeid(int);
            else if (type_str == "double") return typeid(double);
            else if (type_str == "bool") return typeid(bool);
            else if (type_str == "string") return typeid(std::string);
            else if (type_str == "vector2d") return typeid(Eigen::Vector2d);
            else if (type_str == "vector3d") return typeid(Eigen::Vector3d);
            else if (type_str == "quaterniond") return typeid(Eigen::Quaterniond);
            else if (type_str == "wgs84point") return typeid(WGS84Point);
            else if (type_str == "int[]") return typeid(std::vector<int>);
            else if (type_str == "double[]") return typeid(std::vector<double>);
            else if (type_str == "bool[]") return typeid(std::vector<bool>);
            else if (type_str == "string[]") return typeid(std::vector<std::string>);
            else if (type_str == "double[][]") return typeid(std::vector<std::vector<double>>);
            else if (type_str == "string[][]") return typeid(std::vector<std::vector<std::string>>);
            else return std::nullopt;
        }

        // --- 帮助函数的实现 ---
        std::optional<WGS84Point> jsonToWGS84Point(const json& j) {
            if (!j.is_array()) {
                LOG_WARN("NSParams::jsonToWGS84Point: 输入不是数组。JSON: {}", j.dump(2));
                return std::nullopt;
            }
            if (j.size() < 2 || j.size() > 3) {
                LOG_WARN("NSParams::jsonToWGS84Point: 数组大小不正确，应为 2 或 3 个元素 [经度, 纬度, (可选)高度]。得到: {}. JSON: {}", j.size(), j.dump(2));
                return std::nullopt;
            }

            // 检查数组元素类型
            for (size_t i = 0; i < j.size(); ++i) {
                if (!j[i].is_number()) {
                    LOG_WARN("JSON转WGS84Point失败：数组元素{}不是数字。JSON：{}", i, j.dump(2));
                    return std::nullopt;
                }
            }

            try {
                // JSON 数组中存储为 [经度, 纬度, (可选)高度]
                double lon = j.at(0).get<double>();
                double lat = j.at(1).get<double>();
                double alt = (j.size() == 3) ? j.at(2).get<double>() : 0.0;
                // WGS84Point 构造函数现在是 WGS84Point(longitude, latitude, altitude)
                return WGS84Point(lon, lat, alt);
            } catch (const nlohmann::json::exception& e) {
                LOG_ERROR("NSParams::jsonToWGS84Point: JSON 异常转换到 WGS84Point: {}. JSON: {}", e.what(), j.dump(2));
                return std::nullopt;
            } catch (const std::exception& e) {
                LOG_ERROR("NSParams::jsonToWGS84Point: 标准异常转换到 WGS84Point: {}. JSON: {}", e.what(), j.dump(2));
                return std::nullopt;
            }
        }

        // --- ParamValue <-> JSON 实现 ---
        json paramValueToJson(const ParamValue& value_variant) {
            return std::visit([](const auto& value) -> json {
                using T = std::decay_t<decltype(value)>;
                if constexpr (std::is_same_v<T, int> || std::is_same_v<T, double> ||
                                    std::is_same_v<T, bool> || std::is_same_v<T, std::string>) {
                    return value;
                }
                else if constexpr (std::is_same_v<T, Eigen::Vector2d> || std::is_same_v<T, Eigen::Vector3d>) {
                    return eigenVectorToJson(value);
                }
                else if constexpr (std::is_same_v<T, Eigen::Quaterniond>) {
                    return json::array({ value.w(), value.x(), value.y(), value.z() });
                }
                else if constexpr (std::is_same_v<T, WGS84Point>) {
                    // WGS84Point 序列化为 [纬度, 经度, 高度] 数组
                    return json::array({ value.latitude, value.longitude, value.altitude });
                }
                else if constexpr (std::is_same_v<T, std::vector<int>> || std::is_same_v<T, std::vector<double>> ||
                                    std::is_same_v<T, std::vector<bool>> || std::is_same_v<T, std::vector<std::string>>) {
                    return value;
                }
                else if constexpr (std::is_same_v<T, std::vector<std::vector<double>>>) {
                    json j_outer_array = json::array();
                    for (const auto& inner_vec : value) {
                        j_outer_array.push_back(json(inner_vec));
                    }
                    return j_outer_array;
                }
                else if constexpr (std::is_same_v<T, std::vector<std::vector<std::string>>>) {
                    json j_outer_array = json::array();
                    for (const auto& inner_vec : value) {
                        j_outer_array.push_back(json(inner_vec));
                    }
                    return j_outer_array;
                }
                else if constexpr (std::is_same_v<T, std::monostate>) {
                    LOG_TRACE("paramValueToJson: 遇到 std::monostate，序列化为 null。");
                    return nullptr;
                }
                else {
                    LOG_WARN("paramValueToJson: 变体中的未处理类型: {}. 序列化为 null。", typeid(T).name());
                    return nullptr;
                }
            }, value_variant);
        }

        std::pair<ParamValue, ParamJsonError> jsonToParamValue(const json& j, const std::type_index& expected_type, const std::optional<std::string>& enum_type_name) {
            ParamValue result;
            ParamJsonError err = ParamJsonError::Success;

            // 如果提供了 enum_type_name 并且期望类型是字符串，这可能是一个枚举字符串。
            // 当前逻辑：直接尝试将其作为字符串处理。
            if (enum_type_name.has_value() && expected_type == typeid(std::string)) {
                const std::string& type_name = enum_type_name.value();
                LOG_DEBUG("尝试将JSON值作为枚举字符串处理，枚举类型: {}", type_name);

                if (!j.is_string()) {
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }
                result = j.get<std::string>();
                return {result, err};
            }

            // 其他类型的处理
            if (expected_type == typeid(int)) {
                if (!j.is_number_integer()) {
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }
                result = j.get<int>();
            }
            else if (expected_type == typeid(double)) {
                if (!j.is_number()) {
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }
                result = j.get<double>();
            }
            else if (expected_type == typeid(bool)) {
                if (!j.is_boolean()) {
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }
                result = j.get<bool>();
            }
            else if (expected_type == typeid(std::string)) {
                if (!j.is_string()) {
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }
                result = j.get<std::string>();
            }
            else if (expected_type == typeid(Eigen::Vector2d)) {
                auto vec_opt = jsonToEigenVector<Eigen::Vector2d>(j);
                if (!vec_opt) {
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }
                result = *vec_opt;
            }
            else if (expected_type == typeid(Eigen::Vector3d)) {
                auto vec_opt = jsonToEigenVector<Eigen::Vector3d>(j);
                if (!vec_opt) {
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }
                result = *vec_opt;
            }
            else if (expected_type == typeid(Eigen::Quaterniond)) {
                auto quat_opt = jsonToEigenVector<Eigen::Quaterniond>(j);
                if (!quat_opt) {
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }
                result = *quat_opt;
            }
            else if (expected_type == typeid(WGS84Point)) {
                auto wgs84_opt = jsonToWGS84Point(j);
                if (!wgs84_opt) {
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }
                result = *wgs84_opt;
            }
            else if (expected_type == typeid(std::vector<int>)) {
                if (!j.is_array()) {
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }

                try {
                    result = j.get<std::vector<int>>();
                } catch (const nlohmann::json::exception& e) {
                    LOG_WARN("jsonToParamValue: 无法将 JSON 转换为 std::vector<int>: {}", e.what());
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }
            }
            else if (expected_type == typeid(std::vector<double>)) {
                if (!j.is_array()) {
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }

                try {
                    result = j.get<std::vector<double>>();
                } catch (const nlohmann::json::exception& e) {
                    LOG_WARN("jsonToParamValue: 无法将 JSON 转换为 std::vector<double>: {}", e.what());
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }
            }
            else if (expected_type == typeid(std::vector<bool>)) {
                if (!j.is_array()) {
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }

                try {
                    result = j.get<std::vector<bool>>();
                } catch (const nlohmann::json::exception& e) {
                    LOG_WARN("jsonToParamValue: 无法将 JSON 转换为 std::vector<bool>: {}", e.what());
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }
            }
            else if (expected_type == typeid(std::vector<std::string>)) {
                if (!j.is_array()) {
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }

                try {
                    result = j.get<std::vector<std::string>>();
                } catch (const nlohmann::json::exception& e) {
                    LOG_WARN("jsonToParamValue: 无法将 JSON 转换为 std::vector<std::string>: {}", e.what());
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }
            }
            else if (expected_type == typeid(std::vector<std::vector<double>>)) {
                if (!j.is_array()) {
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }

                try {
                    result = j.get<std::vector<std::vector<double>>>();
                } catch (const nlohmann::json::exception& e) {
                    LOG_WARN("jsonToParamValue: 无法将 JSON 转换为 std::vector<std::vector<double>>: {}", e.what());
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }
            }
            else if (expected_type == typeid(std::vector<std::vector<std::string>>)) {
                if (!j.is_array()) {
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }

                try {
                    result = j.get<std::vector<std::vector<std::string>>>();
                } catch (const nlohmann::json::exception& e) {
                    LOG_WARN("jsonToParamValue: 无法将 JSON 转换为 std::vector<std::vector<std::string>>: {}", e.what());
                    return {std::monostate{}, ParamJsonError::InvalidValueFormat};
                }
            }
            else {
                LOG_WARN("jsonToParamValue: 不支持的类型: {}", typeIndexToString(expected_type));
                return {std::monostate{}, ParamJsonError::InvalidType};
            }

            return {result, err};
        }

        // 其他非模板函数的实现可以在此添加
        // 例如 paramConstraintToJson, jsonToParamConstraint, paramConditionToJson, jsonToParamCondition 等

    // --- ParamConstraint 和 JSON 之间的转换 ---
    std::pair<ParamConstraint, ParamJsonError> jsonToParamConstraint(const json& j) {
        if (!j.is_object()) {
            LOG_WARN("jsonToParamConstraint: 输入不是JSON对象");
            return {std::monostate{}, ParamJsonError::InvalidValueFormat};
        }

        if (!j.contains("type")) {
            LOG_WARN("jsonToParamConstraint: 缺少必需的'type'字段");
            return {std::monostate{}, ParamJsonError::MissingRequiredField};
        }

        const std::string& constraint_type = j["type"].get<std::string>();

        try {
            if (constraint_type == "numeric") {
                NumericConstraint constraint;

                if (j.contains("min") && j["min"].is_number()) {
                    constraint.min_value = j["min"].get<double>();
                }

                if (j.contains("max") && j["max"].is_number()) {
                    constraint.max_value = j["max"].get<double>();
                }

                return {constraint, ParamJsonError::Success};
            }
            else if (constraint_type == "string") {
                StringConstraint constraint;

                if (j.contains("max_length") && j["max_length"].is_number_integer()) {
                    constraint.max_length = j["max_length"].get<size_t>();
                }

                if (j.contains("allowed_values") && j["allowed_values"].is_array()) {
                    std::vector<std::string> allowed;
                    for (const auto& val : j["allowed_values"]) {
                        if (val.is_string()) {
                            allowed.push_back(val.get<std::string>());
                        }
                    }
                    constraint.allowed_values = allowed;
                }

                return {constraint, ParamJsonError::Success};
            }
            else if (constraint_type == "vector") {
                VectorConstraint constraint;

                if (j.contains("dimension") && j["dimension"].is_number_integer()) {
                    constraint.expected_dimension = j["dimension"].get<int>();
                }

                if (j.contains("min_norm") && j["min_norm"].is_number()) {
                    constraint.min_norm = j["min_norm"].get<double>();
                }

                if (j.contains("max_norm") && j["max_norm"].is_number()) {
                    constraint.max_norm = j["max_norm"].get<double>();
                }

                return {constraint, ParamJsonError::Success};
            }
            else if (constraint_type == "vector_size") {
                VectorSizeConstraint constraint;

                if (j.contains("min_size") && j["min_size"].is_number_integer()) {
                    constraint.min_size = j["min_size"].get<size_t>();
                }

                if (j.contains("max_size") && j["max_size"].is_number_integer()) {
                    constraint.max_size = j["max_size"].get<size_t>();
                }

                return {constraint, ParamJsonError::Success};
            }
            else {
                LOG_WARN("jsonToParamConstraint: 不支持的约束类型: {}", constraint_type);
                return {std::monostate{}, ParamJsonError::InvalidType};
            }
        } catch (const json::exception& e) {
            LOG_ERROR("jsonToParamConstraint: JSON异常: {}", e.what());
            return {std::monostate{}, ParamJsonError::ValueConversionFailed};
        } catch (const std::exception& e) {
            LOG_ERROR("jsonToParamConstraint: 标准异常: {}", e.what());
            return {std::monostate{}, ParamJsonError::ValueConversionFailed};
        }
    }

    json paramConstraintToJson(const ParamConstraint& constraint) {
        return std::visit([](const auto& c) -> json {
            using T = std::decay_t<decltype(c)>;

            if constexpr (std::is_same_v<T, NumericConstraint>) {
                json j = {{
                    {"type", "numeric"}
                }};

                if (c.min_value.has_value()) j["min"] = *c.min_value;
                if (c.max_value.has_value()) j["max"] = *c.max_value;

                return j;
            }
            else if constexpr (std::is_same_v<T, StringConstraint>) {
                json j = {{
                    {"type", "string"}
                }};

                if (c.max_length.has_value()) {
                    j["max_length"] = *c.max_length;
                }

                if (c.allowed_values.has_value() && !c.allowed_values->empty()) {
                    j["allowed_values"] = *c.allowed_values;
                }

                return j;
            }
            else if constexpr (std::is_same_v<T, VectorConstraint>) {
                json j = {{
                    {"type", "vector"}
                }};

                if (c.expected_dimension.has_value()) {
                    j["dimension"] = *c.expected_dimension;
                }

                if (c.min_norm.has_value()) {
                    j["min_norm"] = *c.min_norm;
                }

                if (c.max_norm.has_value()) {
                    j["max_norm"] = *c.max_norm;
                }

                return j;
            }
            else if constexpr (std::is_same_v<T, VectorSizeConstraint>) {
                json j = {{
                    {"type", "vector_size"}
                }};

                if (c.min_size.has_value()) j["min_size"] = *c.min_size;
                if (c.max_size.has_value()) j["max_size"] = *c.max_size;

                return j;
            }
            else {
                return json{{
                    {"type", "unknown"}
                }};
            }
        }, constraint);
    }

    // 实现 ParamCondition 和 JSON 之间的转换
    json paramConditionToJson(const ParamCondition& pc) {
        json j = {{
            {"parameter", pc.dependent_param_key_}
        }};

        // 设置值（直接比较相等性）
        j["value"] = paramValueToJson(pc.required_value_);

        // 如果有描述，添加到JSON中
        if (pc.description_.has_value()) {
            j["description"] = *pc.description_;
        }

        return j;
    }

    std::pair<ParamCondition, ParamJsonError> jsonToParamCondition(const json& j) {
        ParamCondition pc;

        // 检查必填字段
        if (!j.is_object()) {
            LOG_WARN("jsonToParamCondition: 输入不是JSON对象");
            return {pc, ParamJsonError::InvalidValueFormat};
        }

        if (!j.contains("parameter") || !j["parameter"].is_string()) {
            LOG_WARN("jsonToParamCondition: 缺少必需的'parameter'字段或格式不正确");
            return {pc, ParamJsonError::MissingRequiredField};
        }

        if (!j.contains("value")) {
            LOG_WARN("jsonToParamCondition: 缺少必需的'value'字段");
            return {pc, ParamJsonError::MissingRequiredField};
        }

        // 设置依赖参数键
        pc.dependent_param_key_ = j["parameter"].get<std::string>();

        // 尝试解析值类型
        try {
            if (j["value"].is_boolean()) {
                pc.required_value_ = j["value"].get<bool>();
            }
            else if (j["value"].is_number_integer()) {
                pc.required_value_ = j["value"].get<int>();
            }
            else if (j["value"].is_number_float()) {
                pc.required_value_ = j["value"].get<double>();
            }
            else if (j["value"].is_string()) {
                pc.required_value_ = j["value"].get<std::string>();
            }
            else if (j["value"].is_array()) {
                // 根据数组元素类型判断
                if (j["value"].size() > 0) {
                    if (j["value"][0].is_number_integer()) {
                        pc.required_value_ = j["value"].get<std::vector<int>>();
                    }
                    else if (j["value"][0].is_number_float()) {
                        pc.required_value_ = j["value"].get<std::vector<double>>();
                    }
                    else if (j["value"][0].is_boolean()) {
                        pc.required_value_ = j["value"].get<std::vector<bool>>();
                    }
                    else if (j["value"][0].is_string()) {
                        pc.required_value_ = j["value"].get<std::vector<std::string>>();
                    }
                    else if (j["value"][0].is_array()) {
                        // 检查是否为二维数组
                        if (j["value"][0].size() > 0) {
                            if (j["value"][0][0].is_number_float()) {
                                pc.required_value_ = j["value"].get<std::vector<std::vector<double>>>();
                            }
                            else if (j["value"][0][0].is_string()) {
                                pc.required_value_ = j["value"].get<std::vector<std::vector<std::string>>>();
                            }
                            else {
                                LOG_WARN("jsonToParamCondition: 不支持的二维数组元素类型");
                                return {pc, ParamJsonError::InvalidValueFormat};
                            }
                        } else {
                            // 空的内层数组，默认使用二维字符串数组
                            pc.required_value_ = std::vector<std::vector<std::string>>();
                        }
                    }
                    else {
                        LOG_WARN("jsonToParamCondition: 不支持的数组元素类型");
                        return {pc, ParamJsonError::InvalidValueFormat};
                    }
                } else {
                    // 空数组处理，默认使用字符串数组
                    pc.required_value_ = std::vector<std::string>();
                }
            }
            else {
                LOG_WARN("jsonToParamCondition: 不支持的值类型");
                return {pc, ParamJsonError::InvalidValueFormat};
            }
        }
        catch (const json::exception& e) {
            LOG_ERROR("jsonToParamCondition: JSON异常: {}", e.what());
            return {pc, ParamJsonError::ValueConversionFailed};
        }
        catch (const std::exception& e) {
            LOG_ERROR("jsonToParamCondition: 标准异常: {}", e.what());
            return {pc, ParamJsonError::ValueConversionFailed};
        }

        // 如果描述字段存在，设置描述
        if (j.contains("description") && j["description"].is_string()) {
            pc.description_ = j["description"].get<std::string>();
        }

        return {pc, ParamJsonError::Success};
    }

    } // namespace NSParams
} // namespace NSDrones
