// include/planning/task_planners/task_planner_surveysphere.h
#pragma once

#include "planning/itask_planner.h"
#include "environment/environment_fwd.h"
#include "mission/task.h"
#include "mission/task_params.h"

namespace NSDrones {
	namespace NSPlanning {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class SurveySphereTaskPlanner
		 * @brief 负责规划 SURVEY_SPHERE (球面勘察) 任务。
		 */
		class SurveySphereTaskPlanner : public ITaskPlanner {
		public:
			explicit SurveySphereTaskPlanner(
				IPathPlannerPtr path_planner,
				ITrajectoryOptimizerPtr traj_optimizer);

			// 新增: 初始化方法
			bool initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) override;

			/** @brief 检查是否支持 SURVEY_SPHERE 类型。 */
			bool isTaskTypeSupported(TaskType task_type) const override {
				return task_type == TaskType::SURVEY_SPHERE;
			}

			/**
			 * @brief 规划 SURVEY_SPHERE 任务。
			 * @param task 通用任务对象 (期望类型为 SURVEY_SPHERE)。
			 * @param assigned_uavs 分配给此任务的无人机列表。
			 * @param start_states 每个分配无人机的起始状态。
			 * @return 规划结果 (航线、告警等)。
			 */
			PlanningResult planTask(const NSMission::Task& task,
				const std::vector<NSUav::UavPtr>& assigned_uavs,
				const std::map<ObjectID, NSUav::UavState>& start_states) override;

		};

	} // namespace NSPlanning
} // namespace NSDrones