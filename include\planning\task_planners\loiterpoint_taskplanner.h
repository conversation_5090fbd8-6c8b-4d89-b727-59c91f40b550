// include/planning/task_planners/task_planner_loiterpoint.h
#pragma once

#include "planning/itask_planner.h"
#include "environment/environment_fwd.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/control_point.h"

namespace NSDrones {
	namespace NSPlanning {
		using namespace NSDrones::NSUtils;
		using namespace NSDrones::NSCore;

		/**
		 * @class LoiterPointTaskPlanner
		 * @brief 负责规划 LOITER_POINT (定点盘旋) 任务。
		 */
		class LoiterPointTaskPlanner : public ITaskPlanner {
		public:
			explicit LoiterPointTaskPlanner(
				IPathPlannerPtr path_planner,
				ITrajectoryOptimizerPtr traj_optimizer);

			/** @brief 检查是否支持 LOITER_POINT 类型。 */
			bool isTaskTypeSupported(TaskType task_type) const override {
				return task_type == TaskType::LOITER_POINT;
			}

			/**
			 * @brief 规划 LOITER_POINT 任务。
			 * @param task 通用任务对象 (期望类型为 LOITER_POINT)。
			 * @param assigned_uavs 分配给此任务的无人机列表。
			 * @param start_states 每个分配无人机的起始状态。
			 * @return 规划结果 (航线、告警等)。
			 */
			PlanningResult planTask(const NSMission::Task& task,
				const std::vector<NSUav::UavPtr>& assigned_uavs,
				const std::map<ObjectID, NSUav::UavState>& start_states) override;

			/**
			 * @brief 初始化任务规划器。
			 * @param params 包含配置参数的 ParamValues 对象。
			 * @param raw_config 实例的原始JSON配置。
			 * @return 如果初始化成功则返回 true，否则返回 false。
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) override;

		private:
			// 盘旋参数
			int points_per_circle_ = 36;  ///< 每圈航点数

			/**
			 * @brief 生成盘旋圆周的几何航路点。
			 * @param center 圆心位置 (世界坐标)。
			 * @param radius 圆半径 (米)。
			 * @param altitude 飞行高度 (世界坐标 Z 值)。
			 * @param num_loiter_points (可选) 生成的圆周点数量。默认为 36。
			 * @param clockwise (可选) 盘旋方向，默认为 true (顺时针)。
			 * @return 几何航路点列表 (世界坐标)。如果参数无效则列表可能只包含中心点。
			 */
			std::vector<WGS84Point> genLoiterWaypoints(const WGS84Point& center, double radius, double altitude, int num_loiter_points = 36, bool clockwise = true) const;
		};

	} // namespace NSPlanning
} // namespace NSDrones