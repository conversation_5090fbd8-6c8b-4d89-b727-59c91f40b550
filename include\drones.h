// include/drones.h
#pragma once

/**
 * @file drones.h
 * @brief 统一包含框架核心类型和常用工具。
 *        旨在简化常用功能的包含。
 * @details 这个文件作为一个便捷的入口点，包含了大部分模块都会用到的核心定义和工具。
 *          注意：为了减少编译依赖，不建议在所有文件中都包含此文件，
 *          仅在需要广泛访问核心功能的文件中使用。
 */

 // --- 核心类型 ---
#include "core/types.h"
#include "core/geometry/ishape.h"     // 新几何形状系统
#include "core/base_object.h"
#include "core/entity_object.h"// 基础对象类
#include "core/movement_strategy.h" // 移动策略接口
#include "core/entity_state.h" // 对象状态

// --- Utilities(工具类) ---
#include "utils/logging.h"             // 日志工具
#include "utils/object_id.h"           // 对象 ID 生成与检查
#include "utils/file_utils.h"          // 文件操作
#include "utils/enum_utils.h"
#include "utils/coordinate_converter.h"
#include "utils/thread_safe_cache.h"
#include "utils/orientation_utils.h"

// --- 参数系统 ---
#include "params/parameters.h"      // 参数系统 (ParamValues)
#include "params/paramregistry.h"   // 参数注册表
#include "params/param_defs.h"      // 具体参数结构体定义

// --- 环境系统 ---
#include "environment/environment.h"    // 环境类
#include "environment/entities/zone.h"           // 区域类
#include "environment/entities/obstacle.h"	// 动态障碍物类
#include "environment/collision/collision_engine.h" // 碰撞检测器

// --- 无人机系统 ---
#include "uav/uav_types.h"        // UAV 状态和模式
#include "uav/uav.h"              // UAV 类
#include "uav/idynamic_model.h"   // 动力学模型接口
#include "uav/ienergy_model.h"    // 能量模型接口
#include "uav/dynamics/multirotor_dynamics.h"
#include "uav/dynamics/fixedwing_dynamics.h"
#include "uav/dynamics/vtol_dynamics.h"
#include "uav/energies/multirotor_energies.h"
#include "uav/energies/fixedwing_energies.h"
#include "uav/energies/vtol_energies.h"

// --- 算法系统 ---
#include "algorithm/algorithm_object.h"
#include "algorithm/allocator/itask_allocator.h"
#include "algorithm/allocator/task_allocator.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/path_planner/rrtstar_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "algorithm/trajectory_optimizer/trajectory_optimizer.h"
#include "algorithm/evaluator/itrajectory_evaluator.h"
#include "algorithm/evaluator/energy_evaluator.h"

// --- 任务系统 ---
#include "mission/control_point.h"    // 控制点和载荷动作
#include "mission/capability_requirement.h" // 能力需求
#include "mission/task_targets.h"      // 任务目标
#include "mission/task_strategies.h" // 执行策略
#include "mission/task_params.h" // 具体任务参数
#include "mission/task.h"             // Task 类
#include "mission/mission.h"          // Mission 类

// --- 规划系统 ---
#include "planning/planning_types.h"  // 规划类型 (RoutePoint, PlannedRoute, WarningEvent)
#include "planning/planning_result.h" // 规划结果
#include "planning/mission_planner.h" // 顶层任务规划器
#include "planning/itask_planner.h"   // 任务规划器接口
#include "planning/task_planners/followpath_taskplanner.h"
#include "planning/task_planners/loiterpoint_taskplanner.h"
#include "planning/task_planners/scanarea_taskplanner.h"
#include "planning/task_planners/surveymultipoints_taskplanner.h"
#include "planning/task_planners/surveysphere_taskplanner.h"
#include "planning/task_planners/surveycylinder_taskplanner.h"


