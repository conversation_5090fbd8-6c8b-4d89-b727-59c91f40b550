// src/planning/planning_result.cpp
#include "planning/planning_result.h"
#include "utils/logging.h" 
#include "utils/object_id.h" 
#include <stdexcept> 
#include <utility> 

namespace NSDrones {
	namespace NSPlanning {

		/**
		 * @brief 设置规划结果的状态和消息。
		 * @param success 规划是否成功。
		 * @param message 相关消息或错误描述。
		 */
		void PlanningResult::setStatus(bool success, std::string message) {
			success_ = success; // 设置成功标志
			message_ = std::move(message); // 使用移动赋值优化字符串复制
			LOG_DEBUG("规划结果状态设置: 成功={}, 消息='{}'", success_, message_);
		}

		/**
		 * @brief 设置规划成功状态。
		 * @param success 规划是否成功。
		 */
		void PlanningResult::setSuccess(bool success) {
			success_ = success;
			LOG_DEBUG("规划结果成功状态设置: {}", success_);
		}

		/**
		 * @brief 添加错误消息。
		 * @param error_message 错误消息。
		 */
		void PlanningResult::addError(const std::string& error_message) {
			success_ = false; // 有错误时设置为失败
			if (message_.empty() || message_ == "规划未执行或未设置状态") {
				message_ = error_message;
			} else {
				message_ += "; " + error_message; // 追加错误消息
			}
			LOG_ERROR("规划结果添加错误: {}", error_message);
		}

		/** @brief 检查规划是否成功。 */
		bool PlanningResult::wasSuccessful() const {
			return success_;
		}

		/** @brief 获取与规划结果相关的消息。 */
		const std::string& PlanningResult::getMessage() const {
			return message_;
		}

		/**
		 * @brief 添加或更新指定无人机的规划航线。
		 * @param route 要添加的规划航线对象 (移动语义)。
		 */
		void PlanningResult::addRoute(PlannedRoute route) {
			const ObjectID& uav_id = route.getUavId(); // 获取 ID
			// 检查 ID 是否有效
			if (!NSUtils::isValidObjectID(uav_id)) {
				LOG_WARN("尝试添加关联到无效 ID ('{}') 的规划路径，已忽略。", uav_id);
				return;
			}
			// 使用 insert_or_assign (C++17) 来插入或更新映射中的元素
			auto [it, inserted] = routes_.insert_or_assign(uav_id, std::move(route)); // 使用移动赋值
			if (!inserted) { // 如果键已存在，insert_or_assign 会覆盖旧值
				LOG_WARN("覆盖已存在的无人机 ID '{}' 的规划路径。", uav_id);
			}
			LOG_DEBUG("已添加或更新无人机 ID '{}' 的规划路径，包含 {} 个航点。", uav_id, it->second.getWaypoints().size());
		}

		/** @brief 获取所有规划航线的映射 (只读)。 */
		const std::map<ObjectID, PlannedRoute>& PlanningResult::getAllRoutes() const {
			return routes_;
		}

		/** @brief 获取指定无人机的规划航线指针 (const 版本)。 */
		const PlannedRoute* PlanningResult::getRouteForUav(const ObjectID& uav_id) const {
			auto it = routes_.find(uav_id); // 查找 ID
			return (it != routes_.end()) ? &(it->second) : nullptr; // 找到则返回指针，否则返回 nullptr
		}

		/** @brief 获取指定无人机的规划航线指针 (可修改版本)。 */
		PlannedRoute* PlanningResult::getRouteForUav(const ObjectID& uav_id) {
			auto it = routes_.find(uav_id); // 查找 ID
			return (it != routes_.end()) ? &(it->second) : nullptr; // 找到则返回指针，否则返回 nullptr
		}

		/** @brief 添加一条告警事件。 */
		void PlanningResult::addWarning(WarningEvent warning) {
			// 记录告警信息，包含更多细节
			LOG_DEBUG("添加告警事件: 类型={}, 描述='{}', 时间={:.2f}, UAV={}, 区域={}, 任务={}",
				static_cast<int>(warning.wtype), warning.description, warning.time_stamp,
				NSUtils::isValidObjectID(warning.related_uav_id) ? warning.related_uav_id : "N/A",
				NSUtils::isValidObjectID(warning.related_zone_id) ? warning.related_zone_id : "N/A",
				NSUtils::isValidObjectID(warning.related_task_id) ? warning.related_task_id : "N/A");
			warnings_.emplace_back(std::move(warning)); // 使用 emplace_back 和移动语义高效添加
		}

		/** @brief 获取所有告警事件的列表 (const 引用)。 */
		const std::vector<WarningEvent>& PlanningResult::getWarnings() const {
			return warnings_;
		}

		/** @brief 清空所有结果并重置状态。 */
		void PlanningResult::clear() {
			routes_.clear(); // 清空航线映射
			warnings_.clear(); // 清空告警列表
			success_ = false; // 重置状态为失败
			message_ = "规划结果已被清除。"; // 重置消息
			LOG_INFO("规划结果已清除。");
		}

		/**
		 * @brief 获取指定无人机的规划航线引用。
		 * @param uav_id 无人机 ID。
		 * @return 对 PlannedRoute 的 const 引用。
		 * @throws DroneException 如果未找到指定 ID 的航线。
		 */
		const PlannedRoute& PlanningResult::getRouteOrThrow(const ObjectID& uav_id) const {
			auto it = routes_.find(uav_id); // 查找 ID
			if (it == routes_.end()) { // 如果未找到
				std::string error_msg = "在规划结果中未找到无人机 ID '" + uav_id + "' 的规划路径。";
				LOG_ERROR(error_msg);
				throw DroneException(error_msg, ErrorCode::NotFound); // 抛出异常
			}
			return it->second; // 返回找到的航线引用
		}

		/** @brief 检查规划结果是否有效（成功且至少有一条非空航线）。 */
		bool PlanningResult::isEmpty() const {
			// 结果有效需要规划成功
			if (!success_) return false;
			// 并且至少包含一条非空航线
			for (const auto& pair : routes_) {
				if (!pair.second.isEmpty()) return true; // 找到一条非空航线即可
			}
			// 如果成功但所有航线都为空，则认为无效
			LOG_WARN("规划结果标记为成功，但未包含任何有效的无人机航线。");
			return false;
		}

	} // namespace NSPlanning
} // namespace NSDrones