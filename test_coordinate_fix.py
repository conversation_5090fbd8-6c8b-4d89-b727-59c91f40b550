#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试坐标修复的脚本
验证WGS84坐标配置的一致性
"""

import json
import os
from pathlib import Path

def check_coordinate_consistency():
    """检查坐标配置的一致性"""
    print("=== 坐标配置一致性检查 ===")
    
    # 检查配置文件
    config_files = [
        "data/configfile.json",
        "build/Release/data/configfile.json"
    ]
    
    uav_files = [
        "data/objects/uavs.json", 
        "build/Release/data/objects/uavs.json"
    ]
    
    define_files = [
        "data/defines/EntityObject.json",
        "build/Release/data/defines/EntityObject.json"
    ]
    
    print("\n1. 检查全局WGS84原点配置:")
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    wgs84_origin = config.get('global_parameter_values', {}).get('wgs84_origin')
                    print(f"  {config_file}: {wgs84_origin} (格式: [纬度, 经度, 高度])")
            except Exception as e:
                print(f"  {config_file}: 读取失败 - {e}")
        else:
            print(f"  {config_file}: 文件不存在")
    
    print("\n2. 检查UAV初始位置配置:")
    for uav_file in uav_files:
        if os.path.exists(uav_file):
            try:
                with open(uav_file, 'r', encoding='utf-8') as f:
                    uavs = json.load(f)
                    for uav in uavs:
                        if uav.get('id') == 'AlphaMR001':
                            pos = uav.get('parameters', {}).get('initial_position_wgs84')
                            print(f"  {uav_file}: AlphaMR001 = {pos} (格式: [纬度, 经度, 高度])")
                            break
            except Exception as e:
                print(f"  {uav_file}: 读取失败 - {e}")
        else:
            print(f"  {uav_file}: 文件不存在")
    
    print("\n3. 检查参数定义文件:")
    for define_file in define_files:
        if os.path.exists(define_file):
            try:
                with open(define_file, 'r', encoding='utf-8') as f:
                    defines = json.load(f)
                    for param in defines.get('parameters', []):
                        if param.get('key') == 'initial_position_wgs84':
                            default = param.get('default')
                            desc = param.get('description')
                            print(f"  {define_file}:")
                            print(f"    默认值: {default}")
                            print(f"    描述: {desc}")
                            break
            except Exception as e:
                print(f"  {define_file}: 读取失败 - {e}")
        else:
            print(f"  {define_file}: 文件不存在")

def calculate_ned_offset():
    """计算NED坐标偏移"""
    print("\n=== NED坐标偏移计算 ===")
    
    # WGS84原点 (纬度, 经度, 高度)
    origin_lat, origin_lon, origin_alt = 32.10, 118.80, 10.0
    
    # UAV位置 (纬度, 经度, 高度) - 修复后应该与原点相同
    uav_lat, uav_lon, uav_alt = 32.10, 118.80, 15.0
    
    print(f"WGS84原点: 纬度={origin_lat:.6f}°, 经度={origin_lon:.6f}°, 高度={origin_alt:.1f}m")
    print(f"UAV位置:   纬度={uav_lat:.6f}°, 经度={uav_lon:.6f}°, 高度={uav_alt:.1f}m")
    
    # 简化的NED计算 (仅用于估算)
    lat_diff = uav_lat - origin_lat
    lon_diff = uav_lon - origin_lon
    alt_diff = uav_alt - origin_alt
    
    # 近似转换 (1度纬度 ≈ 111km, 1度经度 ≈ 111km * cos(纬度))
    import math
    lat_to_m = 111000  # 1度纬度约111km
    lon_to_m = 111000 * math.cos(math.radians(origin_lat))  # 1度经度
    
    north_m = lat_diff * lat_to_m
    east_m = lon_diff * lon_to_m
    down_m = -alt_diff  # NED坐标系中，向下为正
    
    print(f"\n估算的NED偏移:")
    print(f"  北向 (North): {north_m:.1f}m")
    print(f"  东向 (East):  {east_m:.1f}m") 
    print(f"  下向 (Down):  {down_m:.1f}m")
    print(f"  NED坐标: ({north_m:.1f}, {east_m:.1f}, {down_m:.1f})")

def main():
    """主函数"""
    print("坐标修复验证脚本")
    print("=" * 50)
    
    check_coordinate_consistency()
    calculate_ned_offset()
    
    print("\n=== 修复总结 ===")
    print("1. 统一WGS84坐标格式为 [纬度, 经度, 高度]")
    print("2. 修复UAV初始位置配置，使其与全局原点一致")
    print("3. 更新参数定义文件中的格式说明")
    print("4. 增强环境有效性检查的容错性")
    print("5. 改进高度调整策略的回退机制")
    
    print("\n预期结果:")
    print("- UAV初始位置应该在NED坐标系中接近 (0, 0, -5)")
    print("- 路径规划应该能够成功找到从起点到目标点的路径")
    print("- 不再出现 '起点在环境中无效' 的错误")

if __name__ == "__main__":
    main()
