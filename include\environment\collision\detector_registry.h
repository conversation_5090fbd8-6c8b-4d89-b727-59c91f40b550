// include/environment/collision/detector_registry.h
#pragma once

#include "environment/collision/collision_types.h"
// #include "environment/collision/shape_detector.h" // 移除，不再使用自定义形状检测器基类
// #include "environment/collision/sphere_detectors.h" // 移除
// #include "environment/collision/capsule_detectors.h" // 移除
#include "core/geometry/ishape.h"  // 新形状系统
#include "utils/logging.h"
#include <fcl/fcl.h>
#include <array>
#include <functional>
#include <memory>
#include <typeindex> // 保留，虽然当前版本未使用，但未来可能有基于type_index的扩展

namespace NSDrones {
	namespace NSEnvironment {

		/**
		 * @class DetectorRegistry
		 * @brief 碰撞检测器注册表，完全依赖FCL进行碰撞检测
		 *
		 * 此版本简化了注册表，移除了特定形状对的优化检测器，
		 * 所有碰撞检测请求都将通过通用的FCL接口处理。
		 */
		class DetectorRegistry {
		public:
			/**
			 * @brief 定义检测器函数类型
			 */
			using DetectorFunction = std::function<CollisionResult(
				const fcl::CollisionObjectd*,
				const fcl::CollisionObjectd*,
				const CollisionOptions&)>;

			/**
			 * @brief 默认构造函数
			 */
			DetectorRegistry() {
				LOG_INFO("检测器注册表：初始化。将使用通用FCL进行所有检测。");
				// 不再需要 initializeBuiltInDetectors()，因为我们总是返回通用检测器
			}

			/**
			 * @brief 查找检测器 (始终返回通用FCL检测器)
			 * @param shapeType1 第一个形状类型 (当前未使用，因为总是返回通用检测器)
			 * @param shapeType2 第二个形状类型 (当前未使用)
			 * @return 通用FCL检测器函数
			 */
			DetectorFunction findDetector(ShapeType shapeType1, ShapeType shapeType2) const {
				LOG_TRACE("检测器注册表：为 {} vs {} 请求检测器，返回通用FCL检测器。",
					getShapeTypeName(shapeType1), getShapeTypeName(shapeType2));

				// 返回通用FCL检测器作为回退
				return []( // 注意：捕获列表，确保 lambda 可以访问 options
					const fcl::CollisionObjectd* obj1,
					const fcl::CollisionObjectd* obj2,
					const CollisionOptions& options	) {
						LOG_TRACE("检测器注册表：执行通用FCL碰撞检测...");
						CollisionResult result;
						result.collisionSource = "通用FCL检测器";

						// 准备 FCL 请求
						fcl::CollisionRequestd collision_request(options.maxContacts, options.enableContact);
						fcl::CollisionResultd fcl_collision_result;

						// 执行 FCL 碰撞检测
						fcl::collide(obj1, obj2, collision_request, fcl_collision_result);

						if (fcl_collision_result.isCollision()) {
							result.hasCollision = true;
							LOG_DEBUG("通用FCL检测器：检测到碰撞。");

							// 提取接触点和穿透深度
							std::vector<fcl::Contactd> contacts;
							fcl_collision_result.getContacts(contacts);
							result.penetrationDepth = 0.0; // 初始化

							unsigned int num_contacts_to_process = std::min(static_cast<unsigned int>(contacts.size()), options.maxContacts);
							for (unsigned int i = 0; i < num_contacts_to_process; ++i) {
								const auto& contact = contacts[i];
								result.addContact(
									EcefPoint(contact.pos[0], contact.pos[1], contact.pos[2]),
									Vector3D(contact.normal[0], contact.normal[1], contact.normal[2])
								);
								if (contact.penetration_depth > result.penetrationDepth) {
									result.penetrationDepth = contact.penetration_depth;
								}
							}
							LOG_TRACE("通用FCL检测器：处理了 {} 个接触点，穿透深度: {:.4f}", num_contacts_to_process, result.penetrationDepth);
						} else {
							LOG_TRACE("通用FCL检测器：未检测到碰撞。");
						}

						// 如果没有碰撞 或者 需要计算距离 (即使有碰撞，有时也需要距离信息)
						if (options.enableDistance) {
							fcl::DistanceRequestd distance_request(options.enableNearestPoints);
							fcl::DistanceResultd fcl_distance_result;
							distance_request.gjk_solver_type = fcl::GJKSolverType::GST_LIBCCD; // 尝试指定求解器
							distance_request.enable_signed_distance = true; // 获取有符号距离

							double dist_val = fcl::distance(obj1, obj2, distance_request, fcl_distance_result);
							result.distance = fcl_distance_result.min_distance; // min_distance 应该是计算出的距离

							LOG_TRACE("通用FCL检测器：计算距离: {:.4f} (FCL原始返回值: {:.4f})", result.distance, dist_val);

							if (options.enableNearestPoints && result.distance < std::numeric_limits<double>::infinity()) {
								result.setNearestPoints(
									EcefPoint(fcl_distance_result.nearest_points[0][0],
														fcl_distance_result.nearest_points[0][1],
														fcl_distance_result.nearest_points[0][2]),
									EcefPoint(fcl_distance_result.nearest_points[1][0],
														fcl_distance_result.nearest_points[1][1],
														fcl_distance_result.nearest_points[1][2]),
									result.distance
								);
								LOG_TRACE("通用FCL检测器：最近点1: ({:.2f},{:.2f},{:.2f}), 最近点2: ({:.2f},{:.2f},{:.2f})",
									fcl_distance_result.nearest_points[0][0], fcl_distance_result.nearest_points[0][1], fcl_distance_result.nearest_points[0][2],
									fcl_distance_result.nearest_points[1][0], fcl_distance_result.nearest_points[1][1], fcl_distance_result.nearest_points[1][2]);
							}
						}
						return result;
				};
			}

			/**
			 * @brief 执行两个碰撞对象之间的碰撞检测
			 * @param obj1 第一个碰撞对象
			 * @param obj2 第二个碰撞对象
			 * @param shapeType1 第一个形状类型
			 * @param shapeType2 第二个形状类型
			 * @param options 碰撞检测选项
			 * @return 碰撞结果
			 */
			CollisionResult detectCollision(
				const fcl::CollisionObjectd* obj1,
				const fcl::CollisionObjectd* obj2,
				ShapeType shapeType1,
				ShapeType shapeType2,
				const CollisionOptions& options
			) const {
				// 查找检测器 (将总是返回通用FCL检测器)
				auto detector = findDetector(shapeType1, shapeType2);

				// 执行检测
				return detector(obj1, obj2, options);
			}

		private:
			/**
			 * @brief 获取形状类型的名称 (用于日志记录)
			 * @param shapeType 形状类型
			 * @return 形状类型名称
			 */
			std::string getShapeTypeName(ShapeType shapeType) const {
				switch (shapeType) {
				case ShapeType::POINT: return "点";
				case ShapeType::LINE: return "线段";
				case ShapeType::POLYGON: return "多边形";
				case ShapeType::SPHERE: return "球体";
				case ShapeType::BOX: return "盒子";
				case ShapeType::CAPSULE: return "胶囊体";
				case ShapeType::CYLINDER: return "圆柱体";
				case ShapeType::CONE: return "圆锥体";
				case ShapeType::CONVEX_HULL: return "凸包体";
				case ShapeType::MESH: return "网格";
				case ShapeType::COMPOUND: return "复合形状";
				default: return "未知形状";
				}
			}
		};

	} // namespace NSEnvironment
} // namespace NSDrones
