// src/mission/mission.cpp
#include "mission/mission.h"
#include "mission/task.h"            
#include "utils/logging.h"
#include "utils/object_id.h"       
#include <algorithm>            
#include <stdexcept>            

namespace NSDrones {
	namespace NSMission {

		// --- Mission 类成员函数实现 ---
		/**
		 * @brief Mission 构造函数。
		 */
		Mission::Mission(ObjectID id, ITaskStrategyMap default_strategies)
			: mission_id_(std::move(id)), strategies_(std::move(default_strategies))
		{
			if (!NSUtils::isValidObjectID(mission_id_)) {
				LOG_CRITICAL("尝试使用无效 ID 创建任务计划: '{}'", mission_id_);
				throw DroneException("任务计划 ID 不能为空或无效。", ErrorCode::InvalidArgument);
			}
			LOG_INFO("任务计划 [{}] 已创建，包含 {} 条默认策略。", mission_id_, strategies_.size());
		}

		/** @brief 获取任务计划 ID。 */
		const ObjectID& Mission::getId() const { return mission_id_; }

		/** @brief 在末尾添加一个任务。 */
		void Mission::addTask(TaskPtr task) {
			if (task && NSUtils::isValidObjectID(task->getId())) {
				LOG_INFO("任务计划 [{}]: 添加任务 ID '{}', 类型={}", mission_id_, task->getId(), NSUtils::enumToString(task->getType()));
				task_list_.push_back(std::move(task));
			}
			else {
				if (!task) { LOG_WARN("任务计划 [{}]: 尝试添加空任务指针，已忽略。", mission_id_); }
				else { LOG_WARN("任务计划 [{}]: 尝试添加 ID 无效的任务 (ID='{}')，已忽略。", mission_id_, task->getId()); }
			}
		}

		/** @brief 在指定索引处插入一个任务。 */
		bool Mission::insertTask(size_t index, TaskPtr task) {
			if (!task || !NSUtils::isValidObjectID(task->getId())) {
				if (!task) { LOG_WARN("任务计划 [{}]: 尝试在索引 {} 插入空任务指针，已忽略。", mission_id_, index); }
				else { LOG_WARN("任务计划 [{}]: 尝试在索引 {} 插入 ID 无效的任务 (ID='{}')，已忽略。", mission_id_, index, task->getId()); }
				return false;
			}
			if (index > task_list_.size()) {
				LOG_WARN("任务计划 [{}]: 插入任务索引 {} 超出范围 [0, {}]，插入失败。", mission_id_, index, task_list_.size()); return false;
			}
			LOG_INFO("任务计划 [{}]: 在索引 {} 插入任务 ID '{}', 类型={}", mission_id_, index, task->getId(), NSUtils::enumToString(task->getType()));
			task_list_.insert(task_list_.begin() + index, std::move(task));
			return true;
		}

		/** @brief 移除指定索引处的任务。 */
		bool Mission::removeTask(size_t index) {
			if (index >= task_list_.size()) {
				LOG_WARN("任务计划 [{}]: 移除任务索引 {} 超出范围 [0, {})，移除失败。", mission_id_, index, task_list_.size());
				return false;
			}
			std::string removed_task_info = "[索引处的任务指针为空]";
			if (task_list_[index]) {
				removed_task_info = "ID='" + task_list_[index]->getId()
					+ "', Type=" + NSUtils::enumToString(task_list_[index]->getType());
			}
			else {
				LOG_WARN("任务计划 [{}]: 索引 {} 处的任务指针为空，但仍会尝试移除该位置。", mission_id_, index);
			}
			LOG_INFO("任务计划 [{}]: 移除索引 {} 处的任务 ({})", mission_id_, index, removed_task_info);
			task_list_.erase(task_list_.begin() + index);
			return true;
		}

		/** @brief 获取任务列表 (const 引用)。 */
		const std::vector<TaskPtr>& Mission::getTasks() const {
			LOG_TRACE("获取任务计划 [{}] 的任务列表 ({} 个任务)", mission_id_, task_list_.size());
			return task_list_;
		}
		/** @brief 获取任务数量。 */
		size_t Mission::getTaskCount() const {
			LOG_TRACE("Mission [{}]: getTaskCount() 请求，任务数量: {}。", mission_id_, task_list_.size());
			return task_list_.size();
		}
		/** @brief 清空所有任务。 */
		void Mission::clearTasks() {
			LOG_DEBUG("Mission [{}]: clearTasks() 请求，清空 {} 个任务。", mission_id_, task_list_.size());
			task_list_.clear();
		}
		/** @brief 检查任务计划是否为空。 */
		bool Mission::isEmpty() const {
			bool empty = task_list_.empty();
			LOG_TRACE("Mission [{}]: isEmpty() 请求，是否为空: {}。", mission_id_, empty);
			return empty;
		}

		TaskPtr Mission::getTaskById(const ObjectID& task_id) const {
			LOG_DEBUG("Mission [{}]: getTaskById() 请求查找任务 ID '{}'。", mission_id_, task_id);
			auto it = std::find_if(task_list_.begin(), task_list_.end(),
				[&task_id](const TaskPtr& task) {
					return task && task->getId() == task_id;
				});
			if (it != task_list_.end()) {
				LOG_TRACE("  任务 ID '{}' 找到。", task_id);
				return *it;
			}
			LOG_WARN("  任务 ID '{}' 未在任务计划 [{}] 中找到。", task_id, mission_id_);
			return nullptr;
		}

	} // namespace NSMission
} // namespace NSDrones