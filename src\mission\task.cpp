// src/mission/task.cpp
#include "mission/task.h"
#include "mission/mission.h"
#include "utils/logging.h"
#include "utils/object_id.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include <algorithm>          
#include <stdexcept>          
#include <optional>          

namespace NSDrones {
	namespace NSMission {

		/**
		 * @brief Task 构造函数。
		 */
		Task::Task(ObjectID id, TaskType type,
			TaskTargetVariant target, // 使用 Variant 存储目标
			CapabilityRequirement requirements,
			ITaskStrategyMap strategies, // 存储策略
			ITaskParamsPtr task_params, // 存储特定参数
			std::string description)
			: id_(std::move(id)),
			type_(type),
			target_(std::move(target)),
			requirements_(std::move(requirements)),
			strategies_(std::move(strategies)),
			params_(std::move(task_params)),
			description_(std::move(description))
			// --- 移除旧成员变量的初始化 ---
		{
			// 验证任务 ID
			if (!NSUtils::isValidObjectID(id_)) {
				throw DroneException("任务 ID 不能为空或无效。", ErrorCode::InvalidArgument);
			}
			// 验证任务目标
			if (target_.valueless_by_exception() || std::visit([](const auto& ptr) { return !ptr; }, target_)) {
				throw DroneException("任务 [" + id_ + "] 必须有关联的有效目标对象。", ErrorCode::InvalidArgument);
			}
			// 验证能力需求
			if (!requirements_.isEmpty()) {
				throw DroneException("任务 [" + id_ + "] 的能力需求无效。", ErrorCode::InvalidArgument);
			}
			// **验证任务参数指针是否有效**
			if (!params_) {
				throw DroneException("任务 [" + id_ + "] (类型 " + NSUtils::enumToString(type) + ") 的特定参数指针不能为空。", ErrorCode::InvalidArgument);
			}
			LOG_INFO("任务对象已创建: ID='{}', Type={}", id_, NSUtils::enumToString(type_));
			LOG_TRACE("  Task Params Type: {}", typeid(*params_).name());
			LOG_TRACE("  Task Target Type Index: {}", target_.index());
			LOG_TRACE("  Task Strategies Count: {}", strategies_.size());
		}

		// --- 策略访问辅助函数 ---
		std::optional<AltitudeStrategy> Task::getAltitudeStrategy() const {
			auto it = strategies_.find("Altitude");
			if (it != strategies_.end()) {
				if (auto ptr = std::dynamic_pointer_cast<AltitudeStrategy>(it->second)) {
					LOG_TRACE("任务 [{}] 找到 Altitude 策略。", id_);
					return *ptr; // 返回策略对象的副本
				}
				else {
					LOG_WARN("任务 [{}] 中找到 'Altitude' 策略，但类型转换失败！", id_);
				}
			}
			LOG_TRACE("任务 [{}] 未找到 Altitude 策略。", id_);
			return std::nullopt; // 未找到或转换失败
		}

		std::optional<SpeedStrategy> Task::getSpeedStrategy() const {
			auto it = strategies_.find("Speed");
			if (it != strategies_.end()) {
				if (auto ptr = std::dynamic_pointer_cast<SpeedStrategy>(it->second)) {
					LOG_TRACE("任务 [{}] 找到 Speed 策略。", id_);
					return *ptr;
				}
				else {
					LOG_WARN("任务 [{}] 中找到 'Speed' 策略，但类型转换失败！", id_);
				}
			}
			LOG_TRACE("任务 [{}] 未找到 Speed 策略。", id_);
			return std::nullopt;
		}

		std::optional<PathConstraintStrategy> Task::getPathConstraintStrategy() const {
			auto it = strategies_.find("PathConstraint");
			if (it != strategies_.end()) {
				if (auto ptr = std::dynamic_pointer_cast<PathConstraintStrategy>(it->second)) {
					LOG_TRACE("任务 [{}] 找到 PathConstraint 策略。", id_);
					return *ptr;
				}
				else {
					LOG_WARN("任务 [{}] 中找到 'PathConstraint' 策略，但类型转换失败！", id_);
				}
			}
			LOG_TRACE("任务 [{}] 未找到 PathConstraint 策略。", id_);
			return std::nullopt;
		}

		// --- 期望状态获取 ---
		double Task::getDesiredSpeed(double default_speed) const {
			if (auto speed_strat = getSpeedStrategy()) {
				LOG_TRACE("任务 [{}]: 使用速度策略值: {:.2f} m/s", id_, speed_strat->desired_speed);
				return speed_strat->desired_speed;
			}
			LOG_TRACE("任务 [{}]: 未找到速度策略，使用默认值: {:.2f} m/s", id_, default_speed);
			return default_speed;
		}

		double Task::getDesiredAltitude(double default_altitude) const {
			if (auto alt_strat = getAltitudeStrategy()) {
				LOG_TRACE("任务 [{}]: 使用高度策略值: {:.2f} m", id_, alt_strat->value);
				return alt_strat->value;
			}
			LOG_TRACE("任务 [{}]: 未找到高度策略，使用默认值: {:.2f} m", id_, default_altitude);
			return default_altitude;
		}

		AltitudeType Task::getDesiredHeightType(AltitudeType default_type) const {
			if (auto alt_strat = getAltitudeStrategy()) {
				LOG_TRACE("任务 [{}]: 使用高度策略类型: {}", id_, NSUtils::enumToString(alt_strat->height_type));
				return alt_strat->height_type;
			}
			LOG_TRACE("任务 [{}]: 未找到高度策略，使用默认类型: {}", id_, NSUtils::enumToString(default_type));
			return default_type;
		}

		// --- 策略修改 ---
		void Task::setStrategy(ITaskStrategyPtr strategy) {
			if (strategy) {
				std::string type = strategy->getName(); // 获取策略类型字符串
				if (!type.empty()) {
					LOG_DEBUG("任务 [{}]: 设置或更新策略 '{}'", id_, type);
					strategies_[type] = std::move(strategy); // 插入或覆盖
				}
				else {
					LOG_WARN("任务 [{}]: 尝试设置类型为空的策略，已忽略。", id_);
				}
			}
			else {
				LOG_WARN("任务 [{}]: 尝试设置空策略指针，已忽略。", id_);
			}
		}

		void Task::removeStrategy(const std::string& strategy_type) {
			if (!strategy_type.empty()) {
				size_t erased_count = strategies_.erase(strategy_type); // 从 map 中移除
				if (erased_count > 0) {
					LOG_DEBUG("任务 [{}]: 已移除策略 '{}'", id_, strategy_type);
				}
				else {
					LOG_TRACE("任务 [{}]: 未找到要移除的策略 '{}'", id_, strategy_type);
				}
			}
			else {
				LOG_WARN("任务 [{}]: 尝试移除类型为空的策略，已忽略。", id_);
			}
		}

	} // namespace NSMission
} // namespace NSDrones