// include/core/types.h
#pragma once

#include <string>
#include <vector>
#include <limits>
#include <cstdint>
#include <stdexcept>
#include <map>
#include <sstream>
#include <cmath>
#include <Eigen/Core>
#include <Eigen/Geometry>
#include <fcl/fcl.h>  // 添加FCL支持
#include <variant>
#include <memory>
#include <optional>
#include <iomanip>
#include <spdlog/fmt/fmt.h>

#include "utils/object_id.h"
#include "magic_enum.hpp"

namespace NSDrones {
	namespace NSCore {
		using Time = double;          // 时间类型 (秒)
		using Number = double;        // 通用数值类型

		// --- ObjectID 类型别名 ---
		// using ObjectID = NSUtils::ObjectID;
		// 注意：不创建INVALID_OBJECT_ID别名，直接使用NSUtils::INVALID_OBJECT_ID避免歧义

		// --- Eigen 类型别名 ---
		using Vector3 = Eigen::Vector3d; // 3D 向量 (double)
		using Vector2 = Eigen::Vector2d; // 2D 向量 (double)

		// 内部计算全部使用ECEF坐标系，外部接口使用WGS84坐标系
		using Point2D = Eigen::Vector2d; // 2D 点 (double)
		using Orientation = Eigen::Quaterniond; // 姿态 (四元数, double)

		// --- FCL 类型别名 ---
		using BoundingBox = fcl::AABBd;     // 轴对齐包围盒 (使用FCL)
		using Transform3d = fcl::Transform3d; // 3D变换矩阵 (使用FCL)

		// --- 为了兼容性，保留别名 ---
		using Vector3D = Vector3;
		using Vector2D = Vector2;

		// --- 通用常量 ---
		namespace Constants {
			constexpr double PI = 3.14159265358979323846; // 圆周率 PI
			constexpr double TWO_PI = 2.0 * PI;         // 2 * PI
			constexpr double HALF_PI = PI / 2.0;        // PI / 2
			constexpr double DEG_TO_RAD = PI / 180.0;   // 角度转弧度因子
			constexpr double RAD_TO_DEG = 180.0 / PI;   // 弧度转角度因子
			constexpr double INF = std::numeric_limits<double>::infinity(); // 无穷大
			constexpr double EARTH_RADIUS = 6371000.0; // 地球平均半径 (米)
			constexpr double EARTH_EQUATORIAL_RADIUS_WGS84 = 6378137.0; // WGS84椭球体赤道半径 (米)
			constexpr double GRAVITY = 9.80665;        // 标准重力加速度 (m/s^2)
			constexpr double AIR_DENSITY_SEA_LEVEL_ISA = 1.225; // 标准海平面空气密度 (kg/m^3)

			// 精度与容差常量
			constexpr double EPSILON = 1e-9;            // 通用小量，用于浮点数比较
			constexpr double ZERO_TOLERANCE = 1e-9;     // 判断是否接近零的容差
			constexpr Time   TIME_EPSILON = 1e-6;       // 时间精度 (1 微秒)
			constexpr Number GEOMETRY_EPSILON = 1e-6;   // 几何精度 (1 微米)
			constexpr double GEOMETRY_EPSILON_SQ = GEOMETRY_EPSILON * GEOMETRY_EPSILON; // **添加：几何精度平方**
			constexpr double VELOCITY_EPSILON = 1e-6;   // 速度精度 (1 微米/秒)
			constexpr double VELOCITY_EPSILON_SQ = VELOCITY_EPSILON * VELOCITY_EPSILON;
			constexpr double ACCEL_EPSILON = 1e-6;      // 加速度精度 (1 微米/秒^2)
			constexpr double ANGLE_EPSILON = 1e-6;      // 角度精度 (弧度)
			constexpr double DEGREES_EPSILON = 1e-9; // 用于比较地理度数的小量 (例如 WGS84 坐标)
			constexpr double EPSILON_OFFSET_FOR_MAX_BOUND = 1e-9;

			// 用于判断速度是否极低的阈值
			constexpr double VERY_LOW_SPEED_THRESHOLD = 0.01;      // 单位: 米/秒
			constexpr double VERY_LOW_SPEED_THRESHOLD_SQ = VERY_LOW_SPEED_THRESHOLD * VERY_LOW_SPEED_THRESHOLD; // 单位: (米/秒)^2

			// 任务空间常量
			constexpr const char* GLOBAL_TASK_SPACE_ID = "global_space"; // 全局任务空间ID
		} // namespace Constants

		// --- 全局状态与错误码 ---
		/** @brief 通用操作状态枚举 */
		enum class Status {
			SUCCESS,     // 操作成功
			FAILURE,     // 操作失败
			IN_PROGRESS, // 操作进行中
			UNKNOWN      // 状态未知
		};

		/** @brief 全局错误码枚举 */
		enum class ErrorCode {
			Success = 0,            // 成功
			UnknownError,           // 未知错误
			InvalidArgument,        // 无效参数或输入
			INVALID_STATE,           // 对象或系统处于无效状态
			NotFound,               // 未找到请求的资源或对象
			FileAccessError,		// 文件访问失败
			Timeout,                // 操作超时
			ResourceUnavailable,    // 所需资源不可用
			PlanningFailed,         // 规划过程失败
			ExecutionFailed,        // 执行过程失败
			IoError,                // 文件或网络 IO 错误
			ConfigError,            // 配置加载或解析错误
			ParameterNotFound,      // 参数未找到
			ParameterTypeMismatch,  // 参数类型不匹配
			ParameterConstraintError,// 参数违反约束
			CollisionDetected,      // 检测到碰撞或潜在碰撞
			NotImplemented,         // 请求的功能尚未实现
			DependencyError,        // 外部依赖项错误 (例如，库加载失败)
			ConversionError         // 数据格式或类型转换错误
		};

		// --- 基础异常类 ---
		/**
		 * @class DroneException
		 * @brief 框架自定义异常基类，包含错误码。
		 */
		class DroneException : public std::runtime_error {
		public:
			/**
			 * @brief 构造函数。
			 * @param message 错误描述信息。
			 * @param code (可选) 对应的错误码，默认为 UnknownError。
			 */
			explicit DroneException(const std::string& message, ErrorCode code = ErrorCode::UnknownError)
				: std::runtime_error(message), error_code_(code) {}

			/** @brief 获取错误码。 */
			ErrorCode code() const { return error_code_; }

			/**
			 * @brief 获取带错误码前缀的错误信息。
			 * @return 包含错误码和原始消息的 C 风格字符串。
			 */
			virtual const char* what() const noexcept override {
				thread_local static std::string formatted_message;
				try {
					std::stringstream ss;
					// 使用 magic_enum 来获取枚举名称字符串
					ss << "[" << magic_enum::enum_name(error_code_) << " (" << static_cast<int>(error_code_) << ")] " << std::runtime_error::what();
					formatted_message = ss.str();
					return formatted_message.c_str();
				}
				catch (...) {
					// 如果 magic_enum 或其他部分抛出异常，也回退
					return std::runtime_error::what();
				}
			}

		private:
			ErrorCode error_code_; // 存储错误码
		};

		/**
		 * @class EnvironmentException
		 * @brief 环境相关的异常类
		 */
		class EnvironmentException : public DroneException {
		public:
			explicit EnvironmentException(const std::string& message, ErrorCode code = ErrorCode::UnknownError)
				: DroneException("环境错误: " + message, code) {}
		};

		/**
		 * @class PlanningException
		 * @brief 规划相关的异常类
		 */
		class PlanningException : public DroneException {
		public:
			explicit PlanningException(const std::string& message, ErrorCode code = ErrorCode::PlanningFailed)
				: DroneException("规划错误: " + message, code) {}
		};

		/**
		 * @class CollisionException
		 * @brief 碰撞检测相关的异常类
		 */
		class CollisionException : public DroneException {
		public:
			explicit CollisionException(const std::string& message, ErrorCode code = ErrorCode::CollisionDetected)
				: DroneException("碰撞检测错误: " + message, code) {}
		};

		/**
		 * @class ParameterException
		 * @brief 参数相关的异常类
		 */
		class ParameterException : public DroneException {
		public:
			explicit ParameterException(const std::string& message, ErrorCode code = ErrorCode::ParameterNotFound)
				: DroneException("参数错误: " + message, code) {}
		};


		// --- 全局共享枚举 (跨模块使用) ---
		// 注意：ShapeType 枚举现在定义在 core/geometry/ishape.h 中

		/** @brief 无人机类型枚举 */
		enum class UavType {
			UNKNOWN,        // 未知类型
			MULTIROTOR,     // 多旋翼
			FIXED_WING,     // 固定翼
			VTOL_FIXED_WING // 垂直起降固定翼
		};

		/** @brief 日志级别枚举 */
		enum class LogLevel {
			trace,
			debug,
			info,
			warn,
			error,
			critical,
			off
		};

		/** @brief 推进系统类型枚举 */
		enum class PropulsionType {
			UNKNOWN,    // 未知或未指定
			PROPELLER,  // 螺旋桨
			JET,        // 喷气式
			EDF,        // 电动涵道风扇 (Electric Ducted Fan)
			OTHER       // 其他类型
		};

		/** @brief 告警的严重性级别枚举 */
		enum class SeverityType {
			UNKNOWN,	// 未知或未指定
			ALARM_INFO,  // 通知
			ALARM_WARN,	// 告警
			ALARM_ERROR, // 报错
			ALARM_CRITICAL // 致命
		};

		/**
		 * @enum ZoneType
		 * @brief 定义区域的类型，例如禁飞区、任务区等。
		 */
		enum class ZoneType {
			KEEPOUT,			// 禁飞区
			SAFETY,				// 安全飞行区
			ENTER_WARNING,		// 进入警告区
			LEAVE_WARNING,		// 离开警告区
			THREAT,				// 威胁区 (可能需要特殊行为或避让)
			OPERATIONAL,		// 可操作区域 (例如任务执行区域)
			COMMUNICATION,		// 通信区域
			GEOFENCE,			// 地理围栏 (通用)
			NAVIGATION_HAZARD,	// 导航危险区
			CORRIDOR,			// 安全飞行走廊
			TERRAIN_FLAT,		// 平地区域
			TERRAIN_MOUNTAIN,	// 山地区域
			UNKNOWN				// 未知类型
		};

		/** @brief 地图地物特征类型枚举 */
		enum class FeatureType {
			UNKNOWN,    // 未知
			BUILDING,   // 建筑物
			TREE,       // 树木
			WATER,      // 水体
			ROAD,       // 道路
			TERRAIN,    // 地形/裸土
			OTHER       // 其他
		};

		/** @brief 高度参考类型枚举 */
		enum class AltitudeType {
			ABSOLUTE_ALTITUDE,  // 绝对海拔高度 (MSL - Mean Sea Level)
			ABOVE_GROUND_LEVEL, // 相对地面高度 (AGL - Above Ground Level)
			ABOVE_LAUNCH_POINT  // 相对于起飞点高度
		};

		/** @brief 任务控制点类型枚举 */
		enum class ControlPointType {
			START,              // 任务起始点
			END,                // 任务结束点
			WAYPOINT_MUST_PASS, // 必须通过的航路点
			WAYPOINT_CONSTRAINT // 约束点 (例如区域边界, 盘旋中心, 非强制通过)
		};

		/** @brief 任务类型枚举 */
		enum class TaskType {
			UNKNOWN,                 // 无任务
			LOITER_POINT,         // 定点盘旋
			FOLLOW_PATH,          // 沿航线飞行
			SURVEY_SPHERE,         // 球面勘察 (绕点拍照)
			SURVEY_CYLINDER,       // 圆柱面勘察 (绕柱飞行)
			SCAN_AREA,            // 区域覆盖扫描
			SURVEY_MULTIPOINTS   // 多点勘察 (访问离散点)
		};

		// --- 新增的核心枚举类型 ---
		/** @brief 建筑材料类型枚举 */
		enum class BuildingMaterialType {
			UNKNOWN,
			CONCRETE,
			BRICK,
			WOOD,
			METAL,
			GLASS
		};

		/** @brief 能量消耗模型类型枚举 */
		enum class EnergyModelType {
			SIMPLE,       // 简化模型 (例如，基于固定功率或简单系数)
			DETAILED,     // 详细模型 (例如，考虑动力学、气动等)
			LEARNED,      // 基于学习的模型
			HYBRID,       // 混合模型
			UNKNOWN       // 未知或未指定
		};

		/** @brief 飞控类型枚举 */
		enum class FlightControllerType {
			PID,          // PID 控制器
			LQR,          // LQR 控制器
			MPC,          // 模型预测控制
			ADAPTIVE,     // 自适应控制
			NEURAL,       // 基于神经网络的控制
			MANUAL,       // 手动控制 (或外部飞控)
			UNKNOWN       // 未知或未指定
		};



		/** @brief 移动策略类型枚举 */
		enum class MovementStrategyType {
			STATIC,           // 静止策略
			LINEAR,           // 线性移动策略
			CIRCULAR_PATH,    // 圆形路径策略
			SCRIPTED_PATH,    // 脚本路径策略
			ATTACHED,         // 附着移动策略
			FLIGHT_STRATEGY,  // 飞行策略 (UAV专用)
			UNKNOWN           // 未知或未指定
		};

		// --- 地理坐标点结构体 ---
			/**
	 * @brief ECEF坐标点的强类型包装
	 *
	 * 这是一个轻量级的强类型包装，用于在类型层面区分ECEF坐标和其他坐标系。
	 * 内部使用Vector3D存储，但提供类型安全的接口。
	 */
		class EcefPoint {
		public:
			// 构造函数
			EcefPoint() : point_(Vector3D::Zero()) {}
			explicit EcefPoint(const Vector3D& point) : point_(point) {}
			EcefPoint(double x, double y, double z) : point_(x, y, z) {}

			// 访问器
			double x() const { return point_.x(); }
			double y() const { return point_.y(); }
			double z() const { return point_.z(); }

			// 修改器
			void setX(double x) { point_.x() = x; }
			void setY(double y) { point_.y() = y; }
			void setZ(double z) { point_.z() = z; }

			// 转换方法
			/**
			 * @brief 转换为Vector3D类型
			 * @return Vector3D表示的ECEF坐标
			 */
			Vector3D toVector3D() const {
				return point_;
			}

			// 数学运算
			EcefPoint operator+(const Vector3D& vec) const {
				return EcefPoint(point_ + vec);
			}

			EcefPoint operator-(const Vector3D& vec) const {
				return EcefPoint(point_ - vec);
			}

			Vector3D operator-(const EcefPoint& other) const {
				return point_ - other.point_;
			}

			EcefPoint& operator+=(const Vector3D& vec) {
				point_ += vec;
				return *this;
			}

			EcefPoint& operator-=(const Vector3D& vec) {
				point_ -= vec;
				return *this;
			}

			// 比较运算
			bool operator==(const EcefPoint& other) const {
				return point_ == other.point_;
			}

			bool operator!=(const EcefPoint& other) const {
				return point_ != other.point_;
			}

			// 字符串表示
			std::string toString() const {
				return "ECEF(" + std::to_string(x()) + ", " + std::to_string(y()) + ", " + std::to_string(z()) + ")";
			}

			// 静态工厂方法
			static EcefPoint Zero() {
				return EcefPoint(Vector3D::Zero());
			}

		private:
			Vector3D point_;
		};

		/**
		 * @brief NED坐标点的强类型包装
		 *
		 * 用于明确表示NED局部坐标系中的点
		 */
		class NedPoint {
		public:
			// 构造函数
			NedPoint() : point_(Vector3D::Zero()) {}
			explicit NedPoint(const Vector3D& point) : point_(point) {}
			NedPoint(double north, double east, double down) : point_(north, east, down) {}

			// 访问器（使用NED命名）
			double north() const { return point_.x(); }
			double east() const { return point_.y(); }
			double down() const { return point_.z(); }

			// 修改器
			void setNorth(double north) { point_.x() = north; }
			void setEast(double east) { point_.y() = east; }
			void setDown(double down) { point_.z() = down; }

			// 数学运算
			NedPoint operator+(const Vector3D& vec) const {
				return NedPoint(point_ + vec);
			}

			NedPoint operator-(const Vector3D& vec) const {
				return NedPoint(point_ - vec);
			}

			Vector3D operator-(const NedPoint& other) const {
				return point_ - other.point_;
			}

			// 字符串表示
			std::string toString() const {
				return fmt::format("NED({:.6f}, {:.6f}, {:.6f})", north(), east(), down());
			}

			// 静态工厂方法
			static NedPoint Zero() {
				return NedPoint(Vector3D::Zero());
			}

		private:
			Vector3D point_;
		};

		/**
		 * @struct WGS84Point
		 * @brief WGS84 椰球坐标点。
		 */
		struct WGS84Point {
			double longitude = 0.0; // 经度 (十进制度, -180 到 180)
			double latitude = 0.0;  // 纬度 (十进制度, -90 到 90)
			double altitude = 0.0;  // 海拔高度 (米, 相对于 WGS84 椰球或 MSL，取决于具体约定)

			WGS84Point() = default; // 默认构造
			WGS84Point(double lon, double lat, double alt)
				: longitude(lon), latitude(lat), altitude(alt) {}

			/** @brief 检查坐标是否在有效范围内。*/
			bool isValid() const {
				return latitude >= -90.0 && latitude <= 90.0 &&
					longitude >= -180.0 && longitude <= 180.0 &&
					std::isfinite(altitude); // 高度也应是有效数值
			}

			/**
			 * @brief 将坐标点转换为字符串表示
			 * @return 包含经纬度和高度的字符串
			 */
			std::string toString() const {
				return fmt::format("WGS84({:.6f}°, {:.6f}°, {:.1f}m)", longitude, latitude, altitude);
			}

			/**
			 * @brief 检查此 WGS84Point 实例是否表示一个"自动确定原点"的哨兵值。
			 *        哨兵值通过所有成员都是 NaN 来识别。
			 * @return 如果所有成员都是 NaN，则为 true，否则为 false。
			 */
			bool isAutoDetermineSentinel() const { // 重命名以更清晰地区分 isValid
				return std::isnan(latitude) && std::isnan(longitude) && std::isnan(altitude);
			}

			/**
			 * @brief 检查坐标是否包含 NaN 值
			 * @return 如果任何坐标分量为 NaN 则返回true
			 */
			bool hasNaN() const {
				return std::isnan(latitude) || std::isnan(longitude) || std::isnan(altitude);
			}

			/**
			 * @brief 检查所有坐标分量是否都是有限数值
			 * @return 如果所有坐标分量都是有限数值则返回true
			 */
			bool allFinite() const {
				return std::isfinite(latitude) && std::isfinite(longitude) && std::isfinite(altitude);
			}

			/**
			 * @brief 计算两个 WGS84 点之间的差值（用于变化检测）
			 * @param other 另一个 WGS84 点
			 * @return 包含坐标差值的 Vector3D（纬度差，经度差，高度差）
			 */
			Vector3D operator-(const WGS84Point& other) const {
				return Vector3D(latitude - other.latitude,
					longitude - other.longitude,
					altitude - other.altitude);
			}

			/**
			 * @brief 相等比较运算符
			 * @param other 另一个 WGS84 点
			 * @return 如果两点坐标相等则返回true
			 */
			bool operator==(const WGS84Point& other) const {
				return std::abs(latitude - other.latitude) < 1e-9 &&
					std::abs(longitude - other.longitude) < 1e-9 &&
					std::abs(altitude - other.altitude) < 1e-6;
			}

			/**
			 * @brief 不等比较运算符
			 * @param other 另一个 WGS84 点
			 * @return 如果两点坐标不相等则返回true
			 */
			bool operator!=(const WGS84Point& other) const {
				return !(*this == other);
			}
		};

		// --- 三角形索引结构体 ---
		/**
		 * @struct TriangleIndices
		 * @brief 存储构成三角形的三个顶点索引。
		 */
		struct TriangleIndices {
			int v1 = -1, v2 = -1, v3 = -1; // 顶点索引

			TriangleIndices() = default; // 默认构造
			TriangleIndices(int i1, int i2, int i3) : v1(i1), v2(i2), v3(i3) {}
		};

		// --- 指向相机/传感器的角度对结构体 ---
		/**
		 * @struct AnglePair
		 * @brief 用于描述传感器或相机朝向的角度对。
		 */
		struct AnglePair {
			double azimuth_deg = 0.0;   // 方位角 (度, 通常相对于载具前方或北方)
			double elevation_deg = 0.0; // 俯仰角 (度, 通常相对于水平面)
		};


		struct WGS84BoundingBox {
			double minLongitude = 0.0;
			double maxLongitude = 0.0;
			double minLatitude = 0.0;
			double maxLatitude = 0.0;
			double minAltitude = 0.0;  // 高程信息是必需的
			double maxAltitude = 0.0;  // 高程信息是必需的
			bool isValid = false;

			WGS84BoundingBox() = default;

			WGS84BoundingBox(double minLon, double maxLon, double minLat, double maxLat,
				double minAlt = 0.0, double maxAlt = 0.0)
				: minLongitude(minLon), maxLongitude(maxLon),
				minLatitude(minLat), maxLatitude(maxLat),
				minAltitude(minAlt), maxAltitude(maxAlt) {
				recalculateValidity();
			}

			/**
			 * @brief 从两个 WGS84Point 构造边界框
			 * @param point1 第一个点
			 * @param point2 第二个点
			 * @note 自动确保min/max的正确性，不假设点的相对位置
			 */
			WGS84BoundingBox(const WGS84Point& point1, const WGS84Point& point2)
				: minLongitude(std::min(point1.longitude, point2.longitude)),
				maxLongitude(std::max(point1.longitude, point2.longitude)),
				minLatitude(std::min(point1.latitude, point2.latitude)),
				maxLatitude(std::max(point1.latitude, point2.latitude)),
				minAltitude(std::min(point1.altitude, point2.altitude)),
				maxAltitude(std::max(point1.altitude, point2.altitude)) {
				recalculateValidity();
			}

			void recalculateValidity() {
				isValid = (minLongitude <= maxLongitude && minLatitude <= maxLatitude && minAltitude <= maxAltitude);
			}

			bool isEmpty() const {
				return !isValid;
			}

			/**
			 * @brief 检查边界盒是否跨越180度经线（反子午线）
			 * @return 如果跨越180度经线返回true
			 */
			bool crossesAntimeridian() const {
				return maxLongitude < minLongitude;
			}



			/**
			 * @brief 检查两个边界盒是否相交
			 * @param other 另一个边界盒
			 * @return 如果相交返回true
			 */
			bool intersects(const WGS84BoundingBox& other) const {
				if (!isValid || !other.isValid) return false;

				// 检查纬度相交
				if (maxLatitude < other.minLatitude || minLatitude > other.maxLatitude) {
					return false;
				}

				// 检查经度相交（考虑跨经线情况）
				bool thisSpansAntimeridian = crossesAntimeridian();
				bool otherSpansAntimeridian = other.crossesAntimeridian();

				if (!thisSpansAntimeridian && !otherSpansAntimeridian) {
					// 都不跨经线，正常检查
					if (maxLongitude < other.minLongitude || minLongitude > other.maxLongitude) {
						return false;
					}
				}
				else {
					// 至少一个跨经线，需要特殊处理
					// 简化处理：如果任一跨经线，认为经度上可能相交
					// 更精确的实现需要考虑具体的跨经线情况
				}

				// 检查高程相交
				if (maxAltitude < other.minAltitude || minAltitude > other.maxAltitude) {
					return false;
				}

				return true;
			}

			bool contains(double lon, double lat) const {
				if (!isValid) return false;

				// 检查纬度范围
				bool latInRange = lat >= minLatitude && lat <= maxLatitude;
				if (!latInRange) return false;

				// 处理跨越180度经线的情况
				if (crossesAntimeridian()) {
					// 跨越反子午线：经度在minLongitude以东或maxLongitude以西
					return lon >= minLongitude || lon <= maxLongitude;
				}
				else {
					// 正常情况：经度在minLongitude和maxLongitude之间
					return lon >= minLongitude && lon <= maxLongitude;
				}
			}

			/**
			 * @brief 检查是否包含指定的WGS84点
			 * @param p WGS84点
			 * @return 如果包含该点返回true
			 */
			bool contains(const WGS84Point& p) const {
				bool contains2D = contains(p.longitude, p.latitude);
				if (!contains2D) return false;

				// 检查高程范围
				return p.altitude >= minAltitude && p.altitude <= maxAltitude;
			}

			// 扩展此边界以包含另一个边界
			void extend(const WGS84BoundingBox& other) {
				if (!other.isValid) {
					return;
				}
				if (!isValid) {
					*this = other;
					return;
				}

				// 扩展经纬度和高程范围
				minLongitude = std::min(minLongitude, other.minLongitude);
				maxLongitude = std::max(maxLongitude, other.maxLongitude);
				minLatitude = std::min(minLatitude, other.minLatitude);
				maxLatitude = std::max(maxLatitude, other.maxLatitude);
				minAltitude = std::min(minAltitude, other.minAltitude);
				maxAltitude = std::max(maxAltitude, other.maxAltitude);

				recalculateValidity(); // isValid 可能会改变
			}

			// 获取中心点 (近似，不考虑球面特性)
			WGS84Point getCenter() const {
				if (!isValid) return { 0,0,0 }; // 或者抛出异常

				double centerLon = minLongitude + (maxLongitude - minLongitude) / 2.0;
				double centerLat = minLatitude + (maxLatitude - minLatitude) / 2.0;
				double centerAlt = minAltitude + (maxAltitude - minAltitude) / 2.0;

				return { centerLon, centerLat, centerAlt }; // WGS84Point构造函数: (经度, 纬度, 高度)
			}

			// 获取WGS84边界的字符串表示
			std::string toString() const {
				if (!isValid) return "WGS84BoundingBox(Invalid)";
				std::stringstream ss;
				ss << std::fixed << std::setprecision(6);
				ss << "WGS84BoundingBox(Lon[" << minLongitude << ", " << maxLongitude
					<< "], Lat[" << minLatitude << ", " << maxLatitude
					<< "], Alt[" << minAltitude << ", " << maxAltitude << "])";
				return ss.str();
			}
		};

	} // namespace NSCore
} // namespace NSDrones

using namespace NSDrones::NSCore;