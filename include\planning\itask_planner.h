// include/planning/task_planner/itask_planner.h
#pragma once

#include "core/types.h"
#include "uav/uav_fwd.h"
#include "environment/environment_fwd.h"
#include "mission/task_fwd.h"
#include "mission/task_strategies.h"
#include "planning/planning_result.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include <vector>
#include <memory>
#include <map>
#include <utility>
#include "nlohmann/json.hpp"

namespace NSDrones {
	namespace NSMission { class ControlPoint; }
}

namespace NSDrones {
	namespace NSPlanning {

		/**
		 * @class ITaskPlanner
		 * @brief 特定任务类型规划器的抽象基类接口。
		 *
		 * 负责将一个具体的任务 (Task) 转换为一个或多个无人机的可行航线 (RouteSegment)。
		 * 它利用底层的路径规划器、轨迹优化器、编队管理器、碰撞检测器和环境信息。
		 */
		class ITaskPlanner {
		public:
			/**
			 * @brief 构造函数。
			 * @param path_planner 路径规划器的共享指针。
			 * @param traj_optimizer 轨迹优化器的共享指针 (可以为 nullptr)。
			 * @throws DroneException 如果必要组件 (path_planner) 为空或环境未初始化。
			 * @note 碰撞引擎将从环境中自动获取，无需手动传入。
			 */
			ITaskPlanner(IPathPlannerPtr path_planner,
				ITrajectoryOptimizerPtr traj_optimizer);

			/** @brief 虚析构函数。 */
			virtual ~ITaskPlanner() = default;

			// --- 禁止拷贝和移动 ---
			ITaskPlanner(const ITaskPlanner&) = delete;
			ITaskPlanner& operator=(const ITaskPlanner&) = delete;
			ITaskPlanner(ITaskPlanner&&) = delete;
			ITaskPlanner& operator=(ITaskPlanner&&) = delete;

			/**
			 * @brief (纯虚函数) 规划指定的任务。
			 * @param task 要规划的任务对象 (const 引用)。
			 * @param assigned_uavs 分配给此任务的无人机列表。
			 * @param start_states 每个分配的无人机的起始状态 Map (ID -> UAVState)。
			 * @return 包含规划结果 (航线、告警等) 的 PlanningResult 对象。
			 */
			virtual PlanningResult planTask(const NSMission::Task& task,
				const std::vector<NSUav::UavPtr>& assigned_uavs,
				const std::map<ObjectID, NSUav::UavState>& start_states) = 0;

			/**
			 * @brief (纯虚函数) 检查此规划器是否支持给定的任务类型。
			 * @param task_type 任务类型枚举值。
			 * @return 如果支持，返回 true；否则返回 false。
			 */
			virtual bool isTaskTypeSupported(TaskType task_type) const = 0;

			/**
			 * @brief (纯虚函数) 初始化任务规划器。
			 * @param params 包含配置参数的 ParamValues 对象。
			 * @param raw_config 实例的原始JSON配置。
			 * @return 如果初始化成功则返回 true，否则返回 false。
			 */
			virtual bool initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) = 0;

		protected:
			// --- 共享资源指针 (子类可以直接访问) ---
			IPathPlannerPtr path_planner_;                           // 路径规划器
			ITrajectoryOptimizerPtr trajectory_optimizer_;           // 轨迹优化器 (可能为 nullptr)

			/**
			 * @brief 获取环境实例。
			 * @return 环境的共享指针，如果环境未初始化则返回 nullptr。
			 * @note 使用单例模式获取环境，子类可以直接调用此方法。
			 */
			std::shared_ptr<Environment> getEnvironment() const;

			// --- 通用辅助函数 (子类可以使用) ---
			/**
			 * @brief 获取控制点的绝对ECEF坐标位置（考虑任务策略）。
			 * @param cp 控制点。
			 * @param task 包含此控制点的任务对象，用于获取高度策略。
			 * @return pair，包含绝对ECEF位置和操作是否成功的布尔值。
			 */
			std::pair<EcefPoint, bool> getAbsolutePosition(
				const NSMission::ControlPoint& cp,
				const NSMission::Task& task) const;

			/**
			 * @brief 生成一个简单的直线航段 (只有起点和终点)。
			 * @param start_point 起始航路点 (包含时间、速度等)。
			 * @param end_wgs84_pos 目标WGS84位置。
			 * @param speed 期望的恒定速度。
			 * @param dynamics 无人机动力学模型 (const 引用)。
			 * @return 生成的航段 RouteSegment (包含两个点)。如果速度无效则为空。
			 */
			PlannedRoute generateLinearSegment(const RoutePoint& start_point,
				const WGS84Point& end_wgs84_pos,
				double speed,
				const NSUav::IDynamicModel& dynamics,
				const ObjectID& uav_id) const;

			/**
			 * @brief 检查航段中可能产生的告警 (例如进入/离开告警区)。
			 * @param segment 要检查的航段。
			 * @param uav_id 执行此航段的无人机 ID (用于告警关联)。
			 * @param result 用于添加告警的 PlanningResult 对象引用。
			 * @param task_id (可选) 关联的任务 ID。
			 */
			void checkSegmentWarnings(const PlannedRoute& segment, const ObjectID& uav_id, PlanningResult& result, const ObjectID& task_id = INVALID_OBJECT_ID) const;

			/**
			 * @brief 将几何路径平滑并进行时间参数化。
			 *        首先进行匀速时间参数化，然后（如果配置了优化器）进行轨迹优化。
			 * @param geometric_path 输入的几何路径点 (世界坐标)。
			 * @param uav 执行此路径的无人机指针。
			 * @param start_state 无人机在路径起点的状态。
			 * @param desired_speed 期望速度 (m/s)。
			 * @param optimized_segment 输出参数：优化后的轨迹段。
			 * @param result_ptr (可选) 指向 PlanningResult 的指针，用于添加优化失败等告警。
			 * @param strategies (可选) 应用于此轨迹段的策略。**新增**
			 * @return 如果成功生成有效轨迹，返回 true。
			 */
			bool smoothAndTimeParameterize(const std::vector<WGS84Point>& geometric_path,
				const NSUav::UavPtr& uav,
				const NSUav::UavState& start_state,
				double desired_speed,
				RouteSegment& optimized_segment,
				PlanningResult* result_ptr = nullptr,
				const NSMission::ITaskStrategyMap& strategies = {});

			/**
			 * @brief 平滑几何路径并进行时间参数化（ECEF坐标版本）
			 * @param geometric_path 输入几何路径（ECEF坐标）
			 * @param uav 无人机指针
			 * @param start_state 起始状态
			 * @param desired_speed 期望速度
			 * @param optimized_segment 输出参数：优化后的轨迹段
			 * @param result_ptr (可选) 指向 PlanningResult 的指针，用于添加优化失败等告警
			 * @param strategies (可选) 应用于此轨迹段的策略
			 * @return 如果成功生成有效轨迹，返回 true
			 */
			bool smoothAndTimeParameterizeECEF(const std::vector<EcefPoint>& geometric_path,
				const NSUav::UavPtr& uav,
				const NSUav::UavState& start_state,
				double desired_speed,
				RouteSegment& optimized_segment,
				PlanningResult* result_ptr = nullptr,
				const NSMission::ITaskStrategyMap& strategies = {}); // <-- 使用默认构造

			/**
			 * @brief 检查航段是否满足区域约束。
			 * @param segment 要检查的航段。
			 * @param zone 要检查的区域。
			 * @return 如果满足约束返回 true。
			 * @note 子类可以重写此方法以实现特定区域类型的约束逻辑。
			 */
			virtual bool isZoneConstraintSatisfied(const RouteSegment& segment, const ConstZonePtr& zone) const;

			/**
			 * @brief (内部辅助) 检查路径点是否满足安全约束。
			 * @param point 要检查的路径点。
			 * @param uav_id 无人机 ID。
			 * @param task_id 任务 ID。
			 * @param result 规划结果，用于添加告警。
			 * @param zone_types_to_check 需要检查的特定区域类型列表。
			 * @return 如果满足安全约束则返回 true。
			 */
			bool checkSafetyConstraints(const RoutePoint& point, const ObjectID& uav_id, const ObjectID& task_id, PlanningResult& result, const std::vector<ZoneType>& zone_types_to_check) const;

			/**
			 * @brief (内部辅助) 检查路径段是否安全。
			 * @param wgs84_p1 路径段起点（WGS84坐标）。
			 * @param wgs84_p2 路径段终点（WGS84坐标）。
			 * @param t1 起点时间。
			 * @param t2 终点时间。
			 * @param uav_id 无人机 ID。
			 * @param task_id 任务 ID。
			 * @param result 规划结果，用于添加告警。
			 * @param zone_types_to_check 需要检查的特定区域类型列表。
			 * @return 如果路径段安全则返回 true。
			 */
			bool isPathSegmentSafe(const WGS84Point& wgs84_p1, const WGS84Point& wgs84_p2, Time t1, Time t2, const ObjectID& uav_id, const ObjectID& task_id, PlanningResult& result, const std::vector<ZoneType>& zone_types_to_check) const;

		};

		// 任务规划器接口的智能指针类型别名
		using TaskPlannerPtr = std::shared_ptr<ITaskPlanner>;

	} // namespace NSPlanning
} // namespace NSDrones